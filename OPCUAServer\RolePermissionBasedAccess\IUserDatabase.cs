namespace RolePermissionBasedAccess;

public interface IUserDatabase
{
    /// <summary>
    /// Initialize User Database
    /// </summary>
    void Initialize();

    /// <summary>
    /// Register new user.
    /// </summary>
    /// <param name="userName">The username.</param>
    /// <param name="password">The password.</param>
    /// <param name="roles">The role of the new user.</param>
    /// <returns>true if registered successfull.</returns>
    bool CreateUser(string userName, string password, IEnumerable<string> roles);

    /// <summary>
    /// Delete existing user
    /// </summary>
    /// <param name="userName">The user to delete.</param>
    /// <returns>true if deleted successfully.</returns>
    bool DeleteUser(string userName);

    /// <summary>
    /// Checks if the provided credentials fit a user.
    /// </summary>
    /// <param name="userName">The username.</param>
    /// <param name="password">The password.</param>
    /// <returns>true if userName + PW combination is correct.</returns>
    bool CheckCredentials(string userName, string password);

    /// <summary>
    /// Returns the Role of the provided user.
    /// </summary>
    /// <param name="userName">The username.</param>
    /// <returns>The Role of the provided users.</returns>
    /// <exception cref="ArgumentException">When the user is not found.</exception>
    IEnumerable<string> GetUserRoles(string userName);

    /// <summary>
    /// Changes the password of an existing users.
    /// </summary>
    /// <param name="userName">The username.</param>
    /// <param name="oldPassword">The old password.</param>
    /// <param name="newPassword">The new password.</param>
    /// <returns>true if change was successfull</returns>
    bool ChangePassword(string userName, string oldPassword, string newPassword);

    /// <summary>
    /// Resets a user's password to their original password.
    /// </summary>
    /// <param name="userName">The username.</param>
    /// <returns>true if reset was successful</returns>
    bool ResetPassword(string userName);

    /// <summary>
    /// Gets all users from the database
    /// </summary>
    /// <returns>List of users with username and roles</returns>
    IList<User> GetUsers();
}