﻿using System.Collections.Concurrent;
using PanelHttp;

namespace HTTPInterface;

internal class PAC3120RealtimeData
{
    private const string BLANK = "-99999";
    private ConcurrentDictionary<string, string> _dictPAC3120 = new();

    public void PAC3120RealtimeInit(string itemId)
    {
        this._dictPAC3120 = ReadPanelHttp.InitializeDeviceChannels(itemId);
    }

    public void PAC3120RealtimeUpdate(string itemId)
    {
        ReadPanelHttp.UpdateDeviceChannels(itemId, this._dictPAC3120);
    }

    public string GetChannelValue(string channelName)
    {
        if (string.IsNullOrEmpty(channelName))
        {
            return BLANK;
        }

        return this._dictPAC3120.TryGetValue(channelName, out var value)
            ? value ?? BLANK
            : BLANK;
    }
}
