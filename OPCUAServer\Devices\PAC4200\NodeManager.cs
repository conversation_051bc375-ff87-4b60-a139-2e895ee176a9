using System.Reflection;
using OPCUAServer.Common;
using RolePermissionBasedAccess;
using Serilog;
using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaServer;

namespace OPCUAServer.Devices.PAC4200;

/// <summary>
/// PAC4200NodeManager is a class that manages the PAC4200 system.
/// </summary>
public partial class OPCUANodeManager : BaseNodeManager
{
    // XML template file dependency removed - using programmatic node creation
    // private static readonly string PAC4200TemplateFileName = "PAC4200_Modified.xml";

    private static Dictionary<string, System> _dictPAC4200System = new();

    private readonly ILogger _logger = LoggingHelper.GetLogger<OPCUANodeManager>();

    public ushort InstanceNamespaceIndex { get; private set; }

    public ushort TypeNamespaceIndex { get; set; }

    public string ItemId { get; set; }

    public string ItemName { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="OPCUANodeManager"/> class.
    /// </summary>
    /// <param name="server">The server manager.</param>
    public OPCUANodeManager(ServerManager server)
        : base(server)
    {
        // _logger.Information($"In PAC4200NodeManager constructor, typeNamespaceIndex: {this.TypeNamespaceIndex}, instanceNamespaceIndex: {this.InstanceNamespaceIndex}");
    }

    public static void InitialPAC4200System(int pacNumber, List<string> itemid)
    {
        _dictPAC4200System = itemid.Take(pacNumber)
                                   .ToDictionary(id => id, id => new System());
    }

    /// <summary>
    /// Calculates the next available offset within a block based on existing properties.
    /// This follows the simplified sequential indexing approach used in PAC4200System.
    /// </summary>
    private static int CalculateNextOffset(BlockConfiguration block, NodeId dataType)
    {
        // Since we're using simple sequential indexing (0, 1, 2, 3...),
        // the next offset is simply the count of existing properties
        return block.Properties.Count;
    }

    /// <summary>
    /// Determines the OPC UA data type based on the .NET type of the initial value.
    /// </summary>
    private static NodeId GetDataTypeFromValue(object value)
    {
        return value switch
        {
            bool => DataTypeIds.Boolean,
            sbyte => DataTypeIds.SByte,
            byte => DataTypeIds.Byte,
            short => DataTypeIds.Int16,
            ushort => DataTypeIds.UInt16,
            int => DataTypeIds.Int32,
            uint => DataTypeIds.UInt32,
            long => DataTypeIds.Int64,
            ulong => DataTypeIds.UInt64,
            float => DataTypeIds.Float,
            double => DataTypeIds.Double,
            string => DataTypeIds.String,
            DateTime => DataTypeIds.DateTime,
            Guid => DataTypeIds.Guid,
            byte[] => DataTypeIds.ByteString,
            _ => DataTypeIds.String // Default to string for unknown types
        };
    }

    public override void Startup()
    {
        try
        {
            _logger.Information($"Starting PAC4200NodeManager for {this.ItemName}.");
            base.Startup();

            this.TypeNamespaceIndex = this.AddNamespaceUri(Sentron.PAC4200.Namespaces.PAC4200);
            this.InstanceNamespaceIndex = this.AddNamespaceUri("http://sentron.org/PAC4200" + "/" + this.ItemName);

            _dictPAC4200System[this.ItemId].Initialize(this.ItemId);

            this.CreateObject(
                this.Server.DefaultRequestContext,
                new CreateObjectSettings()
                {
                    ParentNodeId = ObjectIds.ObjectsFolder,
                    ReferenceTypeId = ReferenceTypeIds.Organizes,
                    RequestedNodeId = new NodeId(this.ItemName, this.InstanceNamespaceIndex),
                    BrowseName = new QualifiedName(this.ItemName, this.InstanceNamespaceIndex),
                    TypeDefinitionId = ObjectTypeIds.FolderType
                });

            foreach (BlockConfiguration blockConfig in _dictPAC4200System[this.ItemId].GetBlocks())
            {
                _logger.Information($"Creating block '{blockConfig.Name}' using BaseObjectType (testing independent variable creation)");

                this.CreateObject(this.Server.DefaultRequestContext, new CreateObjectSettings()
                {
                    ParentNodeId = new NodeId(this.ItemName, this.InstanceNamespaceIndex),
                    ReferenceTypeId = ReferenceTypeIds.Organizes,
                    RequestedNodeId = new NodeId(blockConfig.Name, this.InstanceNamespaceIndex),
                    BrowseName = new QualifiedName(blockConfig.Name, this.TypeNamespaceIndex),
                    TypeDefinitionId = ObjectTypeIds.BaseObjectType
                });
            }

            var block = _dictPAC4200System[this.ItemId].GetBlocks().FirstOrDefault(b => b.Type == BlockType.PAC4200);
            if (block is not null)
            {
                ProcessPAC4200ControllerVariables(block);
            }

            var eventBlock = _dictPAC4200System[this.ItemId].GetBlocks().FirstOrDefault(b => b.Type == BlockType.PAC4200Event);
            if (eventBlock is not null)
            {
                AddDynamicProperty(eventBlock.Name, "alert", DataTypeIds.String, string.Empty, false);
            }
        }
        catch (Exception e)
        {
            _logger.Error($"Failed to start PAC4200NodeManager for {this.ItemName}: {e.Message}.");
        }
    }

    /// <summary>
    /// Processes variables for PAC4200Controller blocks.
    /// This method creates the "Ua" variable dynamically and links it to the underlying system data.
    /// Independent of XML configuration - uses hardcoded variable definitions.
    /// </summary>
    private void ProcessPAC4200ControllerVariables(BlockConfiguration block)
    {
        try
        {
            _logger.Information($"Processing PAC4200Controller variables for block '{block.Name}'");

            // Define the "Ua" variable independent of XML configuration
            // This replaces the dependency on PAC4200Configuration.xml properties
            var uaVariableDefinition = new
            {
                Name = "Ua",
                DataType = DataTypeIds.String,  // i=12 from configuration
                Writeable = true,
                Offset = 0  // First property gets offset 0
            };

            // Step 1: Create the "Ua" variable dynamically
            var variableSettings = new CreateVariableSettings()
            {
                ParentNodeId = new NodeId(block.Name, this.InstanceNamespaceIndex),
                ReferenceTypeId = ReferenceTypeIds.HasComponent,
                RequestedNodeId = new NodeId($"{block.Name}.{uaVariableDefinition.Name}", this.InstanceNamespaceIndex),
                BrowseName = new QualifiedName(uaVariableDefinition.Name, this.TypeNamespaceIndex),
                TypeDefinitionId = VariableTypeIds.BaseDataVariableType,
                DataType = uaVariableDefinition.DataType,
                ValueRank = ValueRanks.Scalar,
                AccessLevel = uaVariableDefinition.Writeable ? AccessLevels.CurrentReadOrWrite : AccessLevels.CurrentRead,
                Value = new Variant("0"), // Initial value
                Historizing = false
            };

            VariableNode createdVariable = this.CreateVariable(this.Server.DefaultRequestContext, variableSettings);

            if (createdVariable != null)
            {
                _logger.Information($"✅ Created variable '{uaVariableDefinition.Name}' for block '{block.Name}'");

                // Step 2: Link the variable to the underlying system data
                VariableNode variable = this.SetVariableConfiguration(
                    new NodeId(block.Name, this.InstanceNamespaceIndex),
                    new QualifiedName(uaVariableDefinition.Name, this.TypeNamespaceIndex),
                    NodeHandleType.ExternalPolled,
                    new SystemAddress() { Address = block.Address, Offset = uaVariableDefinition.Offset });

                if (variable != null)
                {
                    _logger.Information($"✅ Successfully linked variable '{uaVariableDefinition.Name}' to system data");

                    // Set node permissions
                    SetNodePermissions(
                        new NodeId(block.Name, this.InstanceNamespaceIndex),
                        new QualifiedName(uaVariableDefinition.Name, this.TypeNamespaceIndex),
                        RolePermissionManager.RolePermissionTypeCollection);
                }
                else
                {
                    _logger.Warning($"⚠️ Failed to link variable '{uaVariableDefinition.Name}' to system data");
                }
            }
            else
            {
                _logger.Error($"❌ Failed to create variable '{uaVariableDefinition.Name}' for block '{block.Name}'");
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, $"❌ Exception processing PAC4200Controller variables for block '{block.Name}': {ex.Message}");
            throw;
        }
    }

    // <summary>
    // Called when the node manager is stopped.
    // </summary>
    public override void Shutdown()
    {
        try
        {
            _logger.Information("Stopping PAC4200NodeManager.");
            base.Shutdown();
        }
        catch (Exception e)
        {
            _logger.Error($"Failed to stop PAC4200NodeManager: {e.Message}.");
        }
    }

    // <summary>
    // An overrideable version of the Dispose.
    // </summary>
    // <param name="disposing"></param>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _dictPAC4200System[this.ItemId].Dispose();
        }
    }

    /// <summary>
    /// Reads the attributes.
    /// </summary>
    /// <param name="context">The context.</param>
    /// <param name="transaction">The transaction.</param>
    /// <param name="operationHandles">The operation handles.</param>
    /// <param name="settings">The settings.</param>
    protected override void Read(
        RequestContext context,
        TransactionHandle transaction,
        IList<NodeAttributeOperationHandle> operationHandles,
        IList<ReadValueId> settings)
    {
        for (int ii = 0; ii < operationHandles.Count; ii++)
        {
            DataValue? dv = null;

            if (operationHandles[ii].NodeHandle.UserData is SystemAddress address)
            {
                if (CannotPassNodeAccessChecks(context, operationHandles[ii].NodeHandle, UserAccessMask.Read, out StatusCode statusCode))
                {
                    dv = new DataValue(statusCode);
                }
                else
                {
                    object? value = _dictPAC4200System[this.ItemId].Read(address.Address, address.Offset);
                    if (value is not null)
                    {
                        dv = new DataValue(new Variant(value, null), DateTime.UtcNow);

                        if (!string.IsNullOrEmpty(settings[ii].IndexRange) || !QualifiedName.IsNull(settings[ii].DataEncoding))
                        {
                            dv = this.ApplyIndexRangeAndEncoding(
                                operationHandles[ii].NodeHandle,
                                dv,
                                settings[ii].IndexRange,
                                settings[ii].DataEncoding);
                        }
                    }
                }
            }

            // set an error if not found.
            dv ??= new DataValue(new StatusCode(StatusCodes.BadNodeIdUnknown));

            ((ReadCompleteEventHandler)transaction.Callback)(
                operationHandles[ii],
                transaction.CallbackData,
                dv,
                true);
        }
    }

    // <summary>
    // Write the attributes
    // </summary>
    // <param name="context">The context.</param>
    // <param name="transaction">The transaction.</param>
    // <param name="operationHandles">The operation handles.</param>
    // <param name="settings">The settings.</param>
    protected override void Write(
        RequestContext context,
        TransactionHandle transaction,
        IList<NodeAttributeOperationHandle> operationHandles,
        IList<WriteValue> settings)
    {
        for (int ii = 0; ii < operationHandles.Count; ii++)
        {
            StatusCode error = StatusCodes.BadNodeIdUnknown;

            if (operationHandles[ii].NodeHandle.UserData is SystemAddress address)
            {
                if (!string.IsNullOrEmpty(settings[ii].IndexRange))
                {
                    error = StatusCodes.BadIndexRangeInvalid;
                }
                else if (CannotPassNodeAccessChecks(context, operationHandles[ii].NodeHandle, UserAccessMask.Write, out StatusCode statusCode))
                {
                    error = statusCode;
                }
                else if (!_dictPAC4200System[this.ItemId].Write(address.Address, address.Offset, settings[ii].Value.Value))
                {
                    error = StatusCodes.BadUserAccessDenied;
                }
            }

            // return the data to the caller.
            ((WriteCompleteEventHandler)transaction.Callback)(
                operationHandles[ii],
                transaction.CallbackData,
                error,
                true);
        }
    }

    /// <summary>
    /// Dynamically adds a new property variable to an existing block node.
    /// This method can be called while the server is running.
    /// </summary>
    /// <param name="blockName">Name of the existing block (parent node)</param>
    /// <param name="propertyName">Name of the new property to add</param>
    /// <param name="dataType">Data type of the new property</param>
    /// <param name="initialValue">Initial value for the property</param>
    /// <param name="isWriteable">Whether the property should be writeable</param>
    /// <returns>True if successful, false otherwise</returns>
    public bool AddDynamicProperty(string blockName, string propertyName, NodeId dataType, object initialValue, bool isWriteable = false)
    {
        try
        {
            _logger.Information($"Adding dynamic property '{propertyName}' to block '{blockName}'");

            // Find the parent block node
            NodeId blockNodeId = new(blockName, this.InstanceNamespaceIndex);
            Node parentNode = this.FindInMemoryNode(blockNodeId);

            if (parentNode == null)
            {
                _logger.Error($"Parent block '{blockName}' not found");
                return false;
            }

            // Generate a unique address/offset for the new property
            // Calculate proper memory offset based on existing variables
            SystemAddress dynamicAddress = GetNextAvailableAddress(blockName, dataType);

            // Step 1: Create the variable node first (this is what was missing!)
            VariableNode? variable = CreateDynamicVariableNode(
                blockNodeId,
                propertyName,
                dataType,
                initialValue,
                isWriteable);

            if (variable == null)
            {
                _logger.Error($"Failed to create variable node for property '{propertyName}'");
                return false;
            }

            // Step 2: Configure the variable with system address (link to underlying data)
            VariableNode configuredVariable = this.SetVariableConfiguration(
                blockNodeId,
                new QualifiedName(propertyName, this.TypeNamespaceIndex),
                NodeHandleType.ExternalPolled,
                new SystemAddress() { Address = dynamicAddress.Address, Offset = dynamicAddress.Offset });

            if (configuredVariable != null)
            {
                lock (this.InMemoryNodeLock)
                {
                    // Set access level
                    configuredVariable.AccessLevel = isWriteable ? AccessLevels.CurrentReadOrWrite : AccessLevels.CurrentRead;

                    // Set initial value
                    configuredVariable.Value = new Variant(initialValue, null);
                    configuredVariable.DataType = dataType;
                }

                // Set permissions (using existing pattern)
                // this.SetNodePermissions(
                //     blockNodeId,
                //     new QualifiedName(propertyName, this.TypeNamespaceIndex),
                //     RolePermissionManager.RolePermissionTypeCollection);

                // Add to the underlying system for data management
                AddPropertyToSystem(blockName, propertyName, dynamicAddress, initialValue);

                _logger.Information($"Successfully added dynamic property '{propertyName}' to block '{blockName}'");
                return true;
            }
            else
            {
                _logger.Error($"Failed to create variable node for property '{propertyName}'");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"Error adding dynamic property '{propertyName}' to block '{blockName}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Gets the next available address for dynamic properties.
    /// This implementation properly calculates memory offsets based on existing variables.
    /// </summary>
    private SystemAddress GetNextAvailableAddress(string blockName, NodeId dataType)
    {
        if (!_dictPAC4200System.ContainsKey(this.ItemId))
        {
            throw new InvalidOperationException($"PAC4200System not found for ItemId: {this.ItemId}");
        }

        System system = _dictPAC4200System[this.ItemId];
        IEnumerable<BlockConfiguration> blocks = system.GetBlocks();

        // Find the target block
        BlockConfiguration? targetBlock = blocks.FirstOrDefault(b => b.Name == blockName);
        if (targetBlock is null)
        {
            throw new ArgumentException($"Block '{blockName}' not found");
        }

        // Calculate the next available offset within this block
        int nextOffset = CalculateNextOffset(targetBlock, dataType);

        return new SystemAddress
        {
            Address = targetBlock.Address,
            Offset = nextOffset
        };
    }

    /// <summary>
    /// Adds the property to the underlying system for data management.
    /// This integrates the dynamic property with the existing data storage and retrieval system.
    /// </summary>
    private void AddPropertyToSystem(string blockName, string propertyName, SystemAddress address, object initialValue)
    {
        try
        {
            if (!_dictPAC4200System.ContainsKey(this.ItemId))
            {
                _logger.Error($"PAC4200System not found for ItemId: {this.ItemId}");
                return;
            }

            var system = _dictPAC4200System[this.ItemId];

            // Find the target block and add the property to its configuration
            var blocks = system.GetBlocks();
            BlockConfiguration? targetBlock = blocks.FirstOrDefault(b => b.Name == blockName);

            if (targetBlock is not null)
            {
                // Create a new BlockProperty for the dynamic property
                var newProperty = new BlockProperty()
                {
                    Offset = address.Offset,
                    Name = propertyName,
                    DataType = GetDataTypeFromValue(initialValue),
                    Writeable = true // Dynamic properties are writeable by default
                };

                // Add to the block's properties list
                targetBlock.Properties.Add(newProperty);

                // Initialize the value in the system's data storage
                // This follows the same pattern as the XML-based initialization
                system.Write(address.Address, address.Offset, initialValue);

                _logger.Information($"Property '{propertyName}' successfully integrated into system at address {address.Address}:{address.Offset}");
            }
            else
            {
                _logger.Error($"Block '{blockName}' not found in system");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"Error adding property to system: {ex.Message}");
        }
    }

    /// <summary>
    /// Finds all existing block nodes in this NodeManager.
    /// Useful for discovering available parent nodes.
    /// </summary>
    /// <returns>List of block node names</returns>
    public List<string> GetExistingBlocks()
    {
        var blocks = new List<string>();

        try
        {
            if (_dictPAC4200System.ContainsKey(this.ItemId))
            {
                foreach (var block in _dictPAC4200System[this.ItemId].GetBlocks())
                {
                    blocks.Add(block.Name);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"Error getting existing blocks: {ex.Message}");
        }

        return blocks;
    }

    /// <summary>
    /// Creates a new variable node dynamically.
    /// This method creates the actual OPC UA node structure before configuration.
    /// </summary>
    private VariableNode? CreateDynamicVariableNode(
        NodeId parentNodeId,
        string propertyName,
        NodeId dataType,
        object initialValue,
        bool isWriteable)
    {
        try
        {
            _logger.Debug($"Creating variable node '{propertyName}' under parent '{parentNodeId}'");

            // Create variable settings similar to the existing pattern
            var settings = new CreateVariableSettings()
            {
                ParentNodeId = parentNodeId,
                ReferenceTypeId = ReferenceTypeIds.HasComponent,
                RequestedNodeId = new NodeId($"{parentNodeId.Identifier}.{propertyName}", InstanceNamespaceIndex),
                BrowseName = new QualifiedName(propertyName, TypeNamespaceIndex),
                TypeDefinitionId = VariableTypeIds.BaseDataVariableType,
                DataType = dataType,
                ValueRank = ValueRanks.Scalar,
                AccessLevel = isWriteable ? AccessLevels.CurrentReadOrWrite : AccessLevels.CurrentRead,
                Value = new Variant(initialValue, null),
                Historizing = false
            };

            // Create the variable node
            VariableNode variable = this.CreateVariable(this.Server.DefaultRequestContext, settings);

            if (variable != null)
            {
                _logger.Information($"✅ Successfully created variable node '{propertyName}'");
                return variable;
            }
            else
            {
                _logger.Error($"❌ CreateVariable returned null for '{propertyName}'");
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, $"❌ Exception creating variable node '{propertyName}': {ex.Message}");
            return null;
        }
    }
}