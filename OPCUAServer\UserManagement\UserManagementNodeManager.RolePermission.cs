﻿using UnifiedAutomation.UaBase;

namespace OPCUAServer.UserManagement;

/// <summary>
/// Partial class for handling role permission setup for UserManagementNodeManager
/// </summary>
internal partial class UserManagementNodeManager
{
    /// <summary>
    /// Sets up method node permissions for different user roles
    /// </summary>
    private void SetupMethodNodePermission()
    {
        RolePermissionTypeCollection methodRolePermissionForAdmin = new()
        {
            new RolePermissionType()
            {
                RoleId = ObjectIds.WellKnownRole_ConfigureAdmin,
                Permissions = PermissionTypeDataType.Read | PermissionTypeDataType.Browse | PermissionTypeDataType.Call
            }
        };

        RolePermissionTypeCollection methodRolePermissionForUser = new()
        {
            new RolePermissionType()
            {
                RoleId = ObjectIds.WellKnownRole_ConfigureAdmin,
                Permissions = PermissionTypeDataType.Read | PermissionTypeDataType.Browse | PermissionTypeDataType.Call
            },
            new RolePermissionType()
            {
                RoleId = ObjectIds.WellKnownRole_Operator,
                Permissions = PermissionTypeDataType.Read | PermissionTypeDataType.Browse | PermissionTypeDataType.Call
            }
        };

        foreach (BlockConfiguration block in this._system.GetBlocks())
        {
            NodeId parentId = new NodeId(block.Name, this.InstanceNamespaceIndex);

            SetNodePermissions(
                parentId,
                new QualifiedName(nameof(BlockMethod.AddUser), this.TypeNamespaceIndex),
                methodRolePermissionForAdmin);
            SetNodePermissions(
                parentId,
                new QualifiedName(nameof(BlockMethod.RemoveUser), this.TypeNamespaceIndex),
                methodRolePermissionForAdmin);
            SetNodePermissions(
                parentId,
                new QualifiedName(nameof(BlockMethod.GetUsers), this.TypeNamespaceIndex),
                methodRolePermissionForAdmin);

            SetNodePermissions(
                parentId,
                new QualifiedName(nameof(BlockMethod.ChangePassword), this.TypeNamespaceIndex),
                methodRolePermissionForUser);
            SetNodePermissions(
                parentId,
                new QualifiedName(nameof(BlockMethod.ResetPassword), this.TypeNamespaceIndex),
                methodRolePermissionForUser);
        }
    }
}
