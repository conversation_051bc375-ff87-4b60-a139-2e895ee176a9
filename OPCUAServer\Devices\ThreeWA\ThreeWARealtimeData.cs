﻿using System.Collections.Concurrent;
using PanelHttp;

namespace HTTPInterface;

internal class ThreeWARealtimeData
{
    private const string BLANK = "-99999";
    private ConcurrentDictionary<string, string> _dictThreeWA = new();

    public void ThreeWARealtimeInit(string itemId)
    {
        this._dictThreeWA = ReadPanelHttp.InitializeDeviceChannels(itemId);
    }

    public void ThreeWARealtimeUpdate(string itemId)
    {
        ReadPanelHttp.UpdateDeviceChannels(itemId, this._dictThreeWA);
    }

    public string GetChannelValue(string channelName)
    {
        if (string.IsNullOrEmpty(channelName))
        {
            return BLANK;
        }

        return this._dictThreeWA.TryGetValue(channelName, out var value)
            ? value ?? BLANK
            : BLANK;
    }
}
