/******************************************************************************
** Copyright (c) 2006-2022 Unified Automation GmbH All rights reserved.
**
** Software License Agreement ("SLA") Version 2.8
**
** Unless explicitly acquired and licensed from Licensor under another
** license, the contents of this file are subject to the Software License
** Agreement ("SLA") Version 2.8, or subsequent versions
** as allowed by the SLA, and You may not copy or use this file in either
** source code or executable form, except in compliance with the terms and
** conditions of the SLA.
**
** All software distributed under the SLA is provided strictly on an
** "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESS OR IMPLIED,
** AND LICENSOR HEREBY DISCLAIMS ALL SUCH WARRANTIES, INCLUDING WITHOUT
** LIMITATION, ANY WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
** PURPOSE, QUIET ENJOYMENT, OR NON-INFRINGEMENT. See the SLA for specific
** language governing rights and limitations under the SLA.
**
** Project: .NET based OPC UA Client Server SDK
**
** Description: OPC Unified Architecture Software Development Kit.
**
** The complete license agreement can be found here:
** http://unifiedautomation.com/License/SLA/2.8/
******************************************************************************/

using System.Reflection;
using OPCUAServer.Common;
using Serilog;
using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaServer;

namespace OPCUAServer.UserManagement;

/// <summary>
/// UserManagementNodeManager is a node manager for the UserManagement system.
/// </summary>
internal partial class UserManagementNodeManager : BaseNodeManager
{
    private readonly ILogger _logger = LoggingHelper.GetLogger<UserManagementNodeManager>();
    private readonly UserManagementSystem _system = new();

    public ushort InstanceNamespaceIndex { get; private set; }

    public ushort TypeNamespaceIndex { get; private set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="UserManagementNodeManager"/> class.
    /// </summary>
    /// <param name="server">The server manager.</param>
    public UserManagementNodeManager(ServerManager server)
        : base(server)
    {
        // Namespace initialization moved to Startup() method
    }

    /// <summary>
    /// Called when the node manager is started.
    /// </summary>
    public override void Startup()
    {
        try
        {
            _logger.Information("Starting UserManagementNodeManager.");

            base.Startup();

            // Initialize namespaces
            TypeNamespaceIndex = AddNamespaceUri(Sentron.UserManagement.Namespaces.UserManagement);
            InstanceNamespaceIndex = AddNamespaceUri("http://sentron.org/UserManagement/");

            _logger.Information("Loading the Controller Model.");
            ImportUaNodeset(Assembly.GetEntryAssembly(), "UserManagement_Modified.xml");

            _logger.Information("Initializing the UserManagementSystem.");
            _system.Initialize();

            _logger.Information("Creating the UserManagement Folder.");
            var settings = new CreateObjectSettings()
            {
                ParentNodeId = ObjectIds.ObjectsFolder,
                ReferenceTypeId = ReferenceTypeIds.Organizes,
                RequestedNodeId = new NodeId("UserManagement", InstanceNamespaceIndex),
                BrowseName = new QualifiedName("UserManagement", InstanceNamespaceIndex),
                TypeDefinitionId = ObjectTypeIds.FolderType,
            };
            CreateObject(Server.DefaultRequestContext, settings);

            _logger.Information("Creating the UserManagement Controllers.");
            foreach (BlockConfiguration block in _system.GetBlocks())
            {
                NodeId typeDefinitionId = ObjectTypeIds.BaseObjectType;
                if (block.Type == BlockType.UserManagement)
                {
                    typeDefinitionId = new NodeId(Sentron.UserManagement.ObjectTypes.UserManagementControllerType, TypeNamespaceIndex);
                }

                settings = new CreateObjectSettings()
                {
                    ParentNodeId = new NodeId("UserManagement", InstanceNamespaceIndex),
                    ReferenceTypeId = ReferenceTypeIds.Organizes,
                    RequestedNodeId = new NodeId(block.Name, InstanceNamespaceIndex),
                    BrowseName = new QualifiedName(block.Name, TypeNamespaceIndex),
                    TypeDefinitionId = typeDefinitionId,
                };
                CreateObject(Server.DefaultRequestContext, settings);

                _logger.Information($"Setting the method user data for the {block.Name} controller.");
                SetMethodUserData(block);
            }

            // SetupEventHistory();
            // SetupVarialbeNodePermission();
            SetupMethodNodePermission();
        }
        catch (Exception e)
        {
            _logger.Error($"Failed to start UserManagementNodeManager. {e.Message}");
        }
    }

    /// <summary>
    /// Called when the node manager is stopped.
    /// </summary>
    public override void Shutdown()
    {
        try
        {
            _logger.Information("Stopping UserManagementNodeManager.");
            base.Shutdown();
        }
        catch (Exception e)
        {
            _logger.Error($"Failed to stop UserManagementNodeManager. {e.Message}");
        }
    }

    /// <summary>
    /// An overrideable version of the Dispose.
    /// </summary>
    /// <param name="disposing">True if the object is being disposed, false if it is being finalized.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _logger.Information("Disposing UserManagementNodeManager.");
            _system.Dispose();
        }
    }
}

