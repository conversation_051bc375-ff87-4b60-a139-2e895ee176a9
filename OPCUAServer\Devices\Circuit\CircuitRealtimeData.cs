﻿using System.Collections.Concurrent;
using PanelHttp;

namespace HTTPInterface;

internal class CircuitRealtimeData
{
    private const string BLANK = "-99999";
    private ConcurrentDictionary<string, string> _dictCircuit = new();

    public void CircuitRealtimeInit(string itemId)
    {
        this._dictCircuit = ReadPanelHttp.InitializeDeviceChannels(itemId);
    }

    public void CircuitRealtimeUpdate(string itemId)
    {
        ReadPanelHttp.UpdateDeviceChannels(itemId, this._dictCircuit);
    }

    public string GetChannelValue(string channelName)
    {
        if (string.IsNullOrEmpty(channelName))
        {
            return BLANK;
        }

        return this._dictCircuit.TryGetValue(channelName, out var value)
            ? value ?? BLANK
            : BLANK;
    }
}
