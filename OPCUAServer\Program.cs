using System.ComponentModel.DataAnnotations;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Sentron.OPCUAServer;
using Sentron.OPCUAServer.Configuration;
using Serilog;

namespace OPCUAServer;

internal static partial class Program
{
    private static ILogger _logger;

    public static IServiceProvider ServiceProvider { get; private set; }

    public static void Main(string[] args)
    {
        Console.OutputEncoding = System.Text.Encoding.UTF8;
        Console.InputEncoding = System.Text.Encoding.UTF8;

        ConfigureSerilog();
        ConfigureServices();
        try
        {
            _logger.Information("=== OPC UA Server Started ===");
            _logger.Information("Initializing OPC UA Server...");
            OpcUAServer.StartOpcUAServer();
        }
        catch (Exception ex)
        {
            _logger.Fatal(ex, "OPC UA Server Start Failed");
            throw;
        }
        finally
        {
            _logger.Information("=== OPC UA Server Closed ===");
            Log.CloseAndFlush();
        }
    }

    /// <summary>
    /// Get the project root directory path
    /// </summary>
    /// <returns>Root directory path</returns>
    private static string GetProjectRootPath()
    {
        // Assembly location: bin/Debug/net6.0/
        // Project root: ../../../
        string assemblyLocation = Assembly.GetExecutingAssembly().Location;
        string projectRoot = Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(assemblyLocation))));

        return projectRoot ?? Directory.GetCurrentDirectory();
    }

    /// <summary>
    /// Configure Serilog Logging System.
    /// </summary>
    private static void ConfigureSerilog()
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .Build();

        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .Enrich.WithProperty("Application", "OPC UA Server")
            .Enrich.WithProperty("Version", "1.0.0")
            .CreateLogger();

        _logger = Log.Logger;
        _logger.Information("Serilog Logging System Initialized");
    }

    private static void ConfigureServices()
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddEnvironmentVariables("OPCUA_")
            .Build();

        var services = new ServiceCollection();

        // Register configuration
        services.Configure<DataSourceConfiguration>(configuration.GetSection("DataSource"));
        services.AddSingleton<IDataSourceConfiguration>(provider =>
        {
            var options = provider.GetRequiredService<IOptions<DataSourceConfiguration>>();
            var config = options.Value;

            // Validate configuration
            var validationContext = new ValidationContext(config);
            var validationResults = new List<ValidationResult>();
            if (!Validator.TryValidateObject(config, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                throw new InvalidOperationException($"Configuration validation failed: {errors}");
            }

            return config;
        });

        // Register server manager
        services.AddSingleton<OPCServerManager>();

        ServiceProvider = services.BuildServiceProvider();
    }
}
