using System.ComponentModel.DataAnnotations;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Sentron.OPCUAServer;
using Sentron.OPCUAServer.Configuration;
using Serilog;

namespace OPCUAServer;

/// <summary>
/// Program Entry Point
/// </summary>
internal static partial class Program
{
    private static ILogger _logger;

    public static IServiceProvider ServiceProvider { get; private set; }

    public static void Main(string[] args)
    {
        Console.OutputEncoding = System.Text.Encoding.UTF8;
        Console.InputEncoding = System.Text.Encoding.UTF8;

        ConfigureSerilog();
        ConfigureServices();
        try
        {
            _logger.Information("=== OPC UA Server Started ===");
            _logger.Information("Initializing OPC UA Server...");
            OpcUAServer.StartOpcUAServer();
        }
        catch (Exception ex)
        {
            _logger.Fatal(ex, "OPC UA Server Start Failed");
            throw;
        }
        finally
        {
            _logger.Information("=== OPC UA Server Closed ===");
            Log.CloseAndFlush();
        }
    }

    /// <summary>
    /// Configure Serilog Logging System.
    /// </summary>
    private static void ConfigureSerilog()
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .Build();

        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .Enrich.WithProperty("Application", "OPC UA Server")
            .Enrich.WithProperty("Version", "1.0.0")
            .CreateLogger();

        _logger = Log.Logger;
        _logger.Information("Serilog Logging System Initialized");
    }

    private static void ConfigureServices()
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddEnvironmentVariables("OPCUA_")
            .Build();

        var services = new ServiceCollection();

        // Register configuration
        services.Configure<DataSourceConfiguration>(configuration.GetSection("DataSource"));

        // Register server manager
        services.AddSingleton<OPCServerManager>();

        ServiceProvider = services.BuildServiceProvider();
    }
}
