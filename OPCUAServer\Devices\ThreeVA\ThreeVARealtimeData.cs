﻿using System.Collections.Concurrent;
using PanelHttp;

namespace HTTPInterface;

internal class ThreeVARealtimeData
{
    private const string BLANK = "-99999";
    private ConcurrentDictionary<string, string> _dictThreeVA = new();

    public void ThreeVARealtimeInit(string itemId)
    {
        this._dictThreeVA = ReadPanelHttp.InitializeDeviceChannels(itemId);
    }

    public void ThreeVARealtimeUpdate(string itemId)
    {
        ReadPanelHttp.UpdateDeviceChannels(itemId, this._dictThreeVA);
    }

    public string GetChannelValue(string channelName)
    {
        if (string.IsNullOrEmpty(channelName))
        {
            return BLANK;
        }

        return this._dictThreeVA.TryGetValue(channelName, out var value)
            ? value ?? BLANK
            : BLANK;
    }
}
