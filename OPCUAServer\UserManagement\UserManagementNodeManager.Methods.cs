/******************************************************************************
** Copyright (c) 2006-2022 Unified Automation GmbH All rights reserved.
**
** Software License Agreement ("SLA") Version 2.8
**
** Unless explicitly acquired and licensed from Licensor under another
** license, the contents of this file are subject to the Software License
** Agreement ("SLA") Version 2.8, or subsequent versions
** as allowed by the SLA, and You may not copy or use this file in either
** source code or executable form, except in compliance with the terms and
** conditions of the SLA.
**
** All software distributed under the SLA is provided strictly on an
** "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESS OR IMPLIED,
** AND LICENSOR HEREBY DISCLAIMS ALL SUCH WARRANTIES, INCLUDING WITHOUT
** LIMITATION, ANY WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
** PURPOSE, QUIET ENJOYMENT, OR NON-INFRINGEMENT. See the SLA for specific
** language governing rights and limitations under the SLA.
**
** Project: .NET based OPC UA Client Server SDK
**
** Description: OPC Unified Architecture Software Development Kit.
**
** The complete license agreement can be found here:
** http://unifiedautomation.com/License/SLA/2.8/
******************************************************************************/

using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaServer;

namespace OPCUAServer.UserManagement;

/// <summary>
/// Methods for the UserManagementNodeManager.
/// </summary>
internal partial class UserManagementNodeManager
{
    /// <summary>
    /// Gets the method dispatcher.
    /// </summary>
    /// <param name="context">The context.</param>
    /// <param name="methodHandle">The method handle.</param>
    /// <returns>CallMethodEventHandler</returns>
    protected override CallMethodEventHandler GetMethodDispatcher(
        RequestContext context,
        MethodHandle methodHandle)
    {
        if (methodHandle.MethodData is SystemFunction)
        {
            return DispatchControllerMethod;
        }

        return null;
    }

    /// <summary>
    /// Sets the method user data.
    /// </summary>
    /// <param name="block">The block configuration.</param>
    private void SetMethodUserData(BlockConfiguration block)
    {
        SetChildUserData(
            new NodeId(block.Name, this.InstanceNamespaceIndex),
            new QualifiedName(Sentron.UserManagement.BrowseNames.AddUser, this.TypeNamespaceIndex),
            new SystemFunction() { Address = block.Address, Function = BlockMethod.AddUser });

        SetChildUserData(
            new NodeId(block.Name, this.InstanceNamespaceIndex),
            new QualifiedName(Sentron.UserManagement.BrowseNames.RemoveUser, this.TypeNamespaceIndex),
            new SystemFunction() { Address = block.Address, Function = BlockMethod.RemoveUser });

        SetChildUserData(
            new NodeId(block.Name, this.InstanceNamespaceIndex),
            new QualifiedName(Sentron.UserManagement.BrowseNames.GetUsers, this.TypeNamespaceIndex),
            new SystemFunction() { Address = block.Address, Function = BlockMethod.GetUsers });

        SetChildUserData(
            new NodeId(block.Name, this.InstanceNamespaceIndex),
            new QualifiedName(Sentron.UserManagement.BrowseNames.ChangePassword, this.TypeNamespaceIndex),
            new SystemFunction() { Address = block.Address, Function = BlockMethod.ChangePassword });

        SetChildUserData(
            new NodeId(block.Name, this.InstanceNamespaceIndex),
            new QualifiedName(Sentron.UserManagement.BrowseNames.ResetPassword, this.TypeNamespaceIndex),
            new SystemFunction() { Address = block.Address, Function = BlockMethod.ResetPassword });
    }

    /// <summary>
    /// Dispatches a method to the controller.
    /// </summary>
    /// <param name="context">The context.</param>
    /// <param name="methodHandle">The method handle.</param>
    /// <param name="inputArguments">The input arguments.</param>
    /// <param name="inputArgumentResults">The input argument results.</param>
    /// <param name="outputArguments">The output arguments.</param>
    /// <returns>The status code.</returns>
    private StatusCode DispatchControllerMethod(
        RequestContext context,
        MethodHandle methodHandle,
        IList<Variant> inputArguments,
        List<StatusCode> inputArgumentResults,
        List<Variant> outputArguments)
    {
        if (methodHandle.MethodData is SystemFunction data)
        {
            if (data.Function == BlockMethod.AddUser)
            {
                return _system.AddUser(data.Address, inputArguments[0].ToString(), inputArguments[1].ToString(), inputArguments[2].ToString());
            }

            if (data.Function == BlockMethod.RemoveUser)
            {
                return _system.RemoveUser(data.Address, inputArguments[0].ToString());
            }

            if (data.Function == BlockMethod.GetUsers)
            {
                var users = _system.GetUsers(data.Address);
                var userStrings = users.Select(user => $"{{\"UserName\":\"{user.UserName}\",\"Roles\":[{string.Join(",", user.Roles.Select(r => $"\"{r}\""))}]}}").ToArray();
                outputArguments[0] = new Variant(userStrings);
                return StatusCodes.Good;
            }

            if (data.Function == BlockMethod.ChangePassword)
            {
                return _system.ChangePassword(data.Address, inputArguments[0].ToString(), inputArguments[1].ToString(), inputArguments[2].ToString());
            }

            if (data.Function == BlockMethod.ResetPassword)
            {
                return _system.ResetPassword(data.Address, inputArguments[0].ToString());
            }
        }

        return StatusCodes.BadNotImplemented;
    }
}