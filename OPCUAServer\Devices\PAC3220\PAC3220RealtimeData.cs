﻿using System.Collections.Concurrent;
using PanelHttp;

namespace HTTPInterface;

internal class PAC3220RealtimeData
{
    private const string BLANK = "-99999";
    private ConcurrentDictionary<string, string> _dictPAC3220 = new();

    public void PAC3220RealtimeInit(string itemId)
    {
        this._dictPAC3220 = ReadPanelHttp.InitializeDeviceChannels(itemId);
    }

    public void PAC3220RealtimeUpdate(string itemId)
    {
        ReadPanelHttp.UpdateDeviceChannels(itemId, this._dictPAC3220);
    }

    public string GetChannelValue(string channelName)
    {
        if (string.IsNullOrEmpty(channelName))
        {
            return BLANK;
        }

        return this._dictPAC3220.TryGetValue(channelName, out var value)
            ? value ?? BLANK
            : BLANK;
    }
}
