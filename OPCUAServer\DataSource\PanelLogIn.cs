using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace PanelHttp;

public class PanelLogIn
{
    public static string LoginAndGotToken(string iP, string port, string name = "admin", string password = "panelmanager")
    {
        using HttpClient client = new();
        string apiUrl = $"http://{iP}:{port}/api/v1/login";
        string jsonContent = JsonConvert.SerializeObject(new { userName = name, password = password });
        var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

        HttpResponseMessage response = client.PostAsync(apiUrl, content).Result;
        response.EnsureSuccessStatusCode();

        string responseContent = response.Content.ReadAsStringAsync().Result;

        JObject jsonObject = JObject.Parse(responseContent);
        if (jsonObject.TryGetValue("data", out JToken? dataToken) && dataToken is JObject dataObject)
        {
            return dataObject["token"]?.ToString() ?? string.Empty;
        }
        else
        {
            throw new Exception("Token not found in response");
        }
    }
}
