using System.Collections.Concurrent;
using OPCUAServer.Common;
using Serilog;

namespace PanelHttp;

internal class ReadPanelHttp
{
    // Centralized constants for all device types
    private const string DEFAULTBLANKVALUE = "-99999";
    private const int DEFAULTWAITINTERVAL = 1000;

    private static readonly ILogger _logger = LoggingHelper.GetLogger<ReadPanelHttp>();

    public static List<IDeviceInfo> ListDevice { get; set; } = new();

    public static ConcurrentDictionary<string, UARealtimeData> DictUaRtData { get; set; } = new();

    /// <summary>
    /// Centralized method to initialize device channels from realtime data
    /// </summary>
    /// <param name="itemId">Device item ID</param>
    /// <param name="defaultValue">Default value for uninitialized channels</param>
    /// <returns>Initialized dictionary with sanitized channel names</returns>
    public static ConcurrentDictionary<string, string> InitializeDeviceChannels(string itemId, string defaultValue = DEFAULTBLANKVALUE) // todo: dataType check
    {
        var deviceDict = new ConcurrentDictionary<string, string>();

        // Wait for data to be available
        while (DictUaRtData[itemId] == null)
        {
            Thread.Sleep(DEFAULTWAITINTERVAL);
        }

        // Process each channel and add to dictionary
        foreach (ChannelVal channelVal in DictUaRtData[itemId].ChannelList)
        {
            if (channelVal.Name != null)
            {
                string sanitizedChannelName = SanitizeChannelName(channelVal.Name);
                deviceDict.TryAdd(sanitizedChannelName, defaultValue);
            }
        }

        return deviceDict;
    }

    /// <summary>
    /// Centralized method to update device channels with current realtime data
    /// </summary>
    /// <param name="itemId">Device item ID</param>
    /// <param name="deviceDict">Device dictionary to update</param>
    public static void UpdateDeviceChannels(string itemId, ConcurrentDictionary<string, string> deviceDict)
    {
        // todo: => tryGet
        if (DictUaRtData[itemId]?.ChannelList == null)
        {
            return;
        }

        foreach (ChannelVal channelVal in DictUaRtData[itemId].ChannelList)
        {
            if (channelVal.Name != null)
            {
                string sanitizedChannelName = SanitizeChannelName(channelVal.Name);
                deviceDict[sanitizedChannelName] = channelVal.Val ?? string.Empty;
            }
        }
    }

    public void StartReadUdcHttp(string iP, string port, string token, int nTimerInterval = 2)
    {
        var readUdcHttpObj = new PanelDeviceList();

        // Read Device List
        ListDevice = readUdcHttpObj.ScanPanelDeviceList(iP, port, token);
        _logger.Information($"Device list:{ListDevice} loaded successfully");
        var deviceItem = new Dictionary<string, string>();
        foreach (var singleDevice in ListDevice)
        {
            deviceItem.Add(singleDevice.ItemId, singleDevice.DeviceType);
            DictUaRtData.TryAdd(singleDevice.ItemId, null);
        }

        Timer? readPanelTimer = null;
        readPanelTimer = new Timer(
            async s =>
            {
                try
                {
                    // Read Device data
                    foreach (var device in deviceItem)
                    {
                        var panelDeviceRtDataObj = new PanelDeviceRtData();
                        var deviceData = await panelDeviceRtDataObj.ReadPanelDeviceDataAsync(iP, port, token, device.Key, device.Value);
                        if (deviceData != null)
                        {
                            DictUaRtData[device.Key] = deviceData;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"ignoring: {ex}");
                }

                readPanelTimer?.Change(TimeSpan.FromSeconds(nTimerInterval), TimeSpan.FromMilliseconds(-1));
            },
            null,
            TimeSpan.FromMilliseconds(-1),
            TimeSpan.FromMilliseconds(-1));

        readPanelTimer?.Change(TimeSpan.FromSeconds(nTimerInterval), TimeSpan.FromMilliseconds(-1));
    }

    /// <summary>
    /// Sanitizes channel names by replacing spaces and hyphens with underscores
    /// </summary>
    /// <param name="channelName">Original channel name</param>
    /// <returns>Sanitized channel name</returns>
    private static string SanitizeChannelName(string channelName)
    {
        return channelName.Replace(" ", "_").Replace("-", "_");
    }
}
