using UnifiedAutomation.UaBase;

namespace Sentron.Circuit;

#region DataType Identifiers
/// <summary>
/// A class that declares constants for all DataTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypes
{
}
#endregion

#region Object Identifiers
/// <summary>
/// A class that declares constants for all Objects in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Objects
{
    /// <summary>
    /// The identifier for the http://sentron.org/Circuit/ Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Circuit_ = 5001;

}
#endregion

#region ObjectType Identifiers
/// <summary>
/// A class that declares constants for all ObjectTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypes
{
    /// <summary>
    /// The identifier for the Circuit ObjectType.
    /// </summary>
    public const uint Circuit = 1003;

}
#endregion

#region Method Identifiers
/// <summary>
/// A class that declares constants for all Methods in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Methods
{
}
#endregion

#region ReferenceType Identifiers
/// <summary>
/// A class that declares constants for all ReferenceTyped in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypes
{
}
#endregion

#region Variable Identifiers
/// <summary>
/// A class that declares constants for all Variables in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Variables
{
    /// <summary>
    /// The identifier for the APhaseTemp1 Variable.
    /// </summary>
    public const uint Circuit_APhaseTemp1 = 6121;

    /// <summary>
    /// The identifier for the APhaseTemp2 Variable.
    /// </summary>
    public const uint Circuit_APhaseTemp2 = 6122;

    /// <summary>
    /// The identifier for the BPhaseTemp1 Variable.
    /// </summary>
    public const uint Circuit_BPhaseTemp1 = 6123;

    /// <summary>
    /// The identifier for the BPhaseTemp2 Variable.
    /// </summary>
    public const uint Circuit_BPhaseTemp2 = 6124;

    /// <summary>
    /// The identifier for the CPhaseTemp1 Variable.
    /// </summary>
    public const uint Circuit_CPhaseTemp1 = 6125;

    /// <summary>
    /// The identifier for the CPhaseTemp2 Variable.
    /// </summary>
    public const uint Circuit_CPhaseTemp2 = 6126;

    /// <summary>
    /// The identifier for the F Variable.
    /// </summary>
    public const uint Circuit_F = 6113;

    /// <summary>
    /// The identifier for the HaveAlarm Variable.
    /// </summary>
    public const uint Circuit_HaveAlarm = 6100;

    /// <summary>
    /// The identifier for the Ia Variable.
    /// </summary>
    public const uint Circuit_Ia = 6107;

    /// <summary>
    /// The identifier for the Ib Variable.
    /// </summary>
    public const uint Circuit_Ib = 6108;

    /// <summary>
    /// The identifier for the Ic Variable.
    /// </summary>
    public const uint Circuit_Ic = 6109;

    /// <summary>
    /// The identifier for the NPhaseTemp1 Variable.
    /// </summary>
    public const uint Circuit_NPhaseTemp1 = 6127;

    /// <summary>
    /// The identifier for the NPhaseTemp2 Variable.
    /// </summary>
    public const uint Circuit_NPhaseTemp2 = 6128;

    /// <summary>
    /// The identifier for the P Variable.
    /// </summary>
    public const uint Circuit_P = 6110;

    /// <summary>
    /// The identifier for the PowFactor Variable.
    /// </summary>
    public const uint Circuit_PowFactor = 6114;

    /// <summary>
    /// The identifier for the Q Variable.
    /// </summary>
    public const uint Circuit_Q = 6111;

    /// <summary>
    /// The identifier for the S Variable.
    /// </summary>
    public const uint Circuit_S = 6112;

    /// <summary>
    /// The identifier for the Switch Variable.
    /// </summary>
    public const uint Circuit_Switch = 6129;

    /// <summary>
    /// The identifier for the THD_Ia Variable.
    /// </summary>
    public const uint Circuit_THD_Ia = 6118;

    /// <summary>
    /// The identifier for the THD_Ib Variable.
    /// </summary>
    public const uint Circuit_THD_Ib = 6119;

    /// <summary>
    /// The identifier for the THD_Ic Variable.
    /// </summary>
    public const uint Circuit_THD_Ic = 6120;

    /// <summary>
    /// The identifier for the THD_Ua Variable.
    /// </summary>
    public const uint Circuit_THD_Ua = 6115;

    /// <summary>
    /// The identifier for the THD_Ub Variable.
    /// </summary>
    public const uint Circuit_THD_Ub = 6116;

    /// <summary>
    /// The identifier for the THD_Uc Variable.
    /// </summary>
    public const uint Circuit_THD_Uc = 6117;

    /// <summary>
    /// The identifier for the Ua Variable.
    /// </summary>
    public const uint Circuit_Ua = 6101;

    /// <summary>
    /// The identifier for the Uab Variable.
    /// </summary>
    public const uint Circuit_Uab = 6104;

    /// <summary>
    /// The identifier for the Ub Variable.
    /// </summary>
    public const uint Circuit_Ub = 6102;

    /// <summary>
    /// The identifier for the Ubc Variable.
    /// </summary>
    public const uint Circuit_Ubc = 6105;

    /// <summary>
    /// The identifier for the Uc Variable.
    /// </summary>
    public const uint Circuit_Uc = 6103;

    /// <summary>
    /// The identifier for the Uca Variable.
    /// </summary>
    public const uint Circuit_Uca = 6106;

    /// <summary>
    /// The identifier for the IsNamespaceSubset Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Circuit__IsNamespaceSubset = 6002;

    /// <summary>
    /// The identifier for the NamespacePublicationDate Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Circuit__NamespacePublicationDate = 6003;

    /// <summary>
    /// The identifier for the NamespaceUri Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Circuit__NamespaceUri = 6004;

    /// <summary>
    /// The identifier for the NamespaceVersion Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Circuit__NamespaceVersion = 6005;

    /// <summary>
    /// The identifier for the StaticNodeIdTypes Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Circuit__StaticNodeIdTypes = 6006;

    /// <summary>
    /// The identifier for the StaticNumericNodeIdRange Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Circuit__StaticNumericNodeIdRange = 6007;

    /// <summary>
    /// The identifier for the StaticStringNodeIdPattern Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Circuit__StaticStringNodeIdPattern = 6008;

}
#endregion

#region VariableTypes Identifiers
/// <summary>
/// A class that declares constants for all VariableTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypes
{
}
#endregion

#region DataType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all DataTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypeIds
{
}
#endregion

#region Method Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Methods in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class MethodIds
{
}
#endregion

#region Object Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectIds
{
    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Circuit_ Object.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Circuit_ = new ExpandedNodeId(Objects.Namespaces_http___sentron_org_Circuit_, Namespaces.Circuit);

}
#endregion

#region ObjectType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypeIds
{
    /// <summary>
    /// The identifier for the Circuit ObjectType.
    /// </summary>
    public static readonly ExpandedNodeId Circuit = new ExpandedNodeId(ObjectTypes.Circuit, Namespaces.Circuit);

}
#endregion

#region ReferenceType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all ReferenceTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypeIds
{
}
#endregion

#region Variable Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Variables in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableIds
{
    /// <summary>
    /// The identifier for the Circuit_APhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_APhaseTemp1 = new ExpandedNodeId(Variables.Circuit_APhaseTemp1, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_APhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_APhaseTemp2 = new ExpandedNodeId(Variables.Circuit_APhaseTemp2, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_BPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_BPhaseTemp1 = new ExpandedNodeId(Variables.Circuit_BPhaseTemp1, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_BPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_BPhaseTemp2 = new ExpandedNodeId(Variables.Circuit_BPhaseTemp2, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_CPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_CPhaseTemp1 = new ExpandedNodeId(Variables.Circuit_CPhaseTemp1, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_CPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_CPhaseTemp2 = new ExpandedNodeId(Variables.Circuit_CPhaseTemp2, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_F Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_F = new ExpandedNodeId(Variables.Circuit_F, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_HaveAlarm Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_HaveAlarm = new ExpandedNodeId(Variables.Circuit_HaveAlarm, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_Ia Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_Ia = new ExpandedNodeId(Variables.Circuit_Ia, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_Ib Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_Ib = new ExpandedNodeId(Variables.Circuit_Ib, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_Ic Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_Ic = new ExpandedNodeId(Variables.Circuit_Ic, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_NPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_NPhaseTemp1 = new ExpandedNodeId(Variables.Circuit_NPhaseTemp1, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_NPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_NPhaseTemp2 = new ExpandedNodeId(Variables.Circuit_NPhaseTemp2, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_P Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_P = new ExpandedNodeId(Variables.Circuit_P, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_PowFactor Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_PowFactor = new ExpandedNodeId(Variables.Circuit_PowFactor, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_Q Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_Q = new ExpandedNodeId(Variables.Circuit_Q, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_S Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_S = new ExpandedNodeId(Variables.Circuit_S, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_Switch Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_Switch = new ExpandedNodeId(Variables.Circuit_Switch, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_THD_Ia Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_THD_Ia = new ExpandedNodeId(Variables.Circuit_THD_Ia, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_THD_Ib Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_THD_Ib = new ExpandedNodeId(Variables.Circuit_THD_Ib, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_THD_Ic Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_THD_Ic = new ExpandedNodeId(Variables.Circuit_THD_Ic, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_THD_Ua Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_THD_Ua = new ExpandedNodeId(Variables.Circuit_THD_Ua, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_THD_Ub Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_THD_Ub = new ExpandedNodeId(Variables.Circuit_THD_Ub, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_THD_Uc Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_THD_Uc = new ExpandedNodeId(Variables.Circuit_THD_Uc, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_Ua Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_Ua = new ExpandedNodeId(Variables.Circuit_Ua, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_Uab Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_Uab = new ExpandedNodeId(Variables.Circuit_Uab, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_Ub Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_Ub = new ExpandedNodeId(Variables.Circuit_Ub, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_Ubc Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_Ubc = new ExpandedNodeId(Variables.Circuit_Ubc, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_Uc Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_Uc = new ExpandedNodeId(Variables.Circuit_Uc, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Circuit_Uca Variable.
    /// </summary>
    public static readonly ExpandedNodeId Circuit_Uca = new ExpandedNodeId(Variables.Circuit_Uca, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Circuit__IsNamespaceSubset Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Circuit__IsNamespaceSubset = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Circuit__IsNamespaceSubset, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Circuit__NamespacePublicationDate Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Circuit__NamespacePublicationDate = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Circuit__NamespacePublicationDate, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Circuit__NamespaceUri Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Circuit__NamespaceUri = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Circuit__NamespaceUri, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Circuit__NamespaceVersion Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Circuit__NamespaceVersion = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Circuit__NamespaceVersion, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Circuit__StaticNodeIdTypes Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Circuit__StaticNodeIdTypes = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Circuit__StaticNodeIdTypes, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Circuit__StaticNumericNodeIdRange Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Circuit__StaticNumericNodeIdRange = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Circuit__StaticNumericNodeIdRange, Namespaces.Circuit);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Circuit__StaticStringNodeIdPattern Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Circuit__StaticStringNodeIdPattern = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Circuit__StaticStringNodeIdPattern, Namespaces.Circuit);

}
#endregion

#region VariableType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all VariableType in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypeIds
{
}
#endregion

#region BrowseName Declarations
/// <summary>
/// Declares all of the BrowseNames used in the Model.
/// </summary>
public static partial class BrowseNames
{
    /// <summary>
    /// The BrowseName for the APhaseTemp1 component.
    /// </summary>
    public const string APhaseTemp1 = "APhaseTemp1";
    /// <summary>
    /// The BrowseName for the APhaseTemp2 component.
    /// </summary>
    public const string APhaseTemp2 = "APhaseTemp2";
    /// <summary>
    /// The BrowseName for the BPhaseTemp1 component.
    /// </summary>
    public const string BPhaseTemp1 = "BPhaseTemp1";
    /// <summary>
    /// The BrowseName for the BPhaseTemp2 component.
    /// </summary>
    public const string BPhaseTemp2 = "BPhaseTemp2";
    /// <summary>
    /// The BrowseName for the CPhaseTemp1 component.
    /// </summary>
    public const string CPhaseTemp1 = "CPhaseTemp1";
    /// <summary>
    /// The BrowseName for the CPhaseTemp2 component.
    /// </summary>
    public const string CPhaseTemp2 = "CPhaseTemp2";
    /// <summary>
    /// The BrowseName for the Circuit component.
    /// </summary>
    public const string Circuit = "Circuit";
    /// <summary>
    /// The BrowseName for the F component.
    /// </summary>
    public const string F = "F";
    /// <summary>
    /// The BrowseName for the HaveAlarm component.
    /// </summary>
    public const string HaveAlarm = "HaveAlarm";
    /// <summary>
    /// The BrowseName for the Ia component.
    /// </summary>
    public const string Ia = "Ia";
    /// <summary>
    /// The BrowseName for the Ib component.
    /// </summary>
    public const string Ib = "Ib";
    /// <summary>
    /// The BrowseName for the Ic component.
    /// </summary>
    public const string Ic = "Ic";
    /// <summary>
    /// The BrowseName for the NPhaseTemp1 component.
    /// </summary>
    public const string NPhaseTemp1 = "NPhaseTemp1";
    /// <summary>
    /// The BrowseName for the NPhaseTemp2 component.
    /// </summary>
    public const string NPhaseTemp2 = "NPhaseTemp2";
    /// <summary>
    /// The BrowseName for the P component.
    /// </summary>
    public const string P = "P";
    /// <summary>
    /// The BrowseName for the PowFactor component.
    /// </summary>
    public const string PowFactor = "PowFactor";
    /// <summary>
    /// The BrowseName for the Q component.
    /// </summary>
    public const string Q = "Q";
    /// <summary>
    /// The BrowseName for the S component.
    /// </summary>
    public const string S = "S";
    /// <summary>
    /// The BrowseName for the Switch component.
    /// </summary>
    public const string Switch = "Switch";
    /// <summary>
    /// The BrowseName for the THD_Ia component.
    /// </summary>
    public const string THDIa = "THD_Ia";
    /// <summary>
    /// The BrowseName for the THD_Ib component.
    /// </summary>
    public const string THDIb = "THD_Ib";
    /// <summary>
    /// The BrowseName for the THD_Ic component.
    /// </summary>
    public const string THDIc = "THD_Ic";
    /// <summary>
    /// The BrowseName for the THD_Ua component.
    /// </summary>
    public const string THDUa = "THD_Ua";
    /// <summary>
    /// The BrowseName for the THD_Ub component.
    /// </summary>
    public const string THDUb = "THD_Ub";
    /// <summary>
    /// The BrowseName for the THD_Uc component.
    /// </summary>
    public const string THDUc = "THD_Uc";
    /// <summary>
    /// The BrowseName for the Ua component.
    /// </summary>
    public const string Ua = "Ua";
    /// <summary>
    /// The BrowseName for the Uab component.
    /// </summary>
    public const string Uab = "Uab";
    /// <summary>
    /// The BrowseName for the Ub component.
    /// </summary>
    public const string Ub = "Ub";
    /// <summary>
    /// The BrowseName for the Ubc component.
    /// </summary>
    public const string Ubc = "Ubc";
    /// <summary>
    /// The BrowseName for the Uc component.
    /// </summary>
    public const string Uc = "Uc";
    /// <summary>
    /// The BrowseName for the Uca component.
    /// </summary>
    public const string Uca = "Uca";
    /// <summary>
    /// The BrowseName for the http://sentron.org/Circuit/ component.
    /// </summary>
    public const string httpSentronOrgCircuit = "http://sentron.org/Circuit/";
}
#endregion

#region Namespace Declarations
/// <summary>
/// Defines constants for all namespaces referenced by the Model.
/// </summary>
public static partial class Namespaces
{
    /// <summary>
    /// The URI for the OpcUa namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUa = "http://opcfoundation.org/UA/";

    /// <summary>
    /// The URI for the OpcUaXsd namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUaXsd = "http://opcfoundation.org/UA/2008/02/Types.xsd";

    /// <summary>
    /// The URI for the Circuit namespace.
    /// </summary>
    public const string Circuit = "http://sentron.org/Circuit/";

    /// <summary>
    /// The URI for the CircuitXsd namespace.
    /// </summary>
    public const string CircuitXsd = "http://sentron.org/Circuit/Types.xsd";
}
#endregion

