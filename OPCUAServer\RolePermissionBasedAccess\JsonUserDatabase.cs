using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;

namespace RolePermissionBasedAccess;

public class JsonUserDatabase : LinqUserDatabase
{
    [JsonIgnore]
    private static JsonUserDatabase? _instance;
    [JsonIgnore]
    private static readonly object _lock = new();
    [JsonIgnore]
    private string _fileName;
    [JsonIgnore]
    private List<UnifiedAutomation.UaSchema.RoleType>? _cachedRoles;

    /// <summary>
    /// Initializes a new instance of the <see cref="JsonUserDatabase"/> class.
    /// </summary>
    /// <param name="fileName">The name of the file to be used for the JSON database.</param>
    private JsonUserDatabase(string fileName)
    {
        _fileName = fileName;
    }

    /// <summary>
    /// Get the singleton instance of the JSON user database.
    /// </summary>
    /// <param name="fileName">The database file name (only used for first initialization)</param>
    /// <returns>The singleton JsonUserDatabase instance</returns>
    public static JsonUserDatabase GetInstance(string fileName = "UserDatabase.json")
    {
        if (_instance == null)
        {
            lock (_lock)
            {
                if (_instance == null)
                {
                    _instance = Load(fileName);
                }
            }
        }

        return _instance;
    }

    /// <summary>
    /// Load the JSON application database.
    /// </summary>
    private static JsonUserDatabase Load(string fileName)
    {
        if (fileName == null)
        {
            throw new ArgumentNullException(nameof(fileName));
        }

        // Create the singleton instance first
        var instance = new JsonUserDatabase(fileName);

        try
        {
            if (File.Exists(fileName))
            {
                byte[] encryptedBytes = File.ReadAllBytes(fileName);

                // string json = Decrypt(encryptedBytes);
                string json = Encoding.UTF8.GetString(encryptedBytes);

                // Deserialize as List first, then convert to HashSet
                var usersList = JsonConvert.DeserializeObject<List<User>>(json);

                usersList?.ForEach(user => Console.WriteLine($"Load user: {user}"));

                // Add users to our singleton instance
                if (usersList != null)
                {
                    foreach (var user in usersList)
                    {
                        instance.Users.Add(user);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
        }

        return instance;
    }

    /// <summary>
    /// Reset the singleton instance (mainly for testing purposes).
    /// </summary>
    public void ResetInstance()
    {
        lock (_lock)
        {
            _instance = null;
        }
    }

    /// <summary>
    /// Save the complete database with encryption.
    /// </summary>
    public override void Save()
    {
        // Convert HashSet to List for better JSON serialization compatibility
        var usersList = this.Users.ToList();
        string json = JsonConvert.SerializeObject(usersList, Formatting.Indented);

        // byte[] encryptedData = Encrypt(json);
        File.WriteAllBytes(_fileName, Encoding.UTF8.GetBytes(json));
    }

    /// <summary>
    /// Generate role configurations from user data.
    /// </summary>
    /// <returns>Array of RoleType configurations</returns>
    public List<UnifiedAutomation.UaSchema.RoleType> GetRoleConfigurations()
    {
        if (_cachedRoles != null)
        {
            return _cachedRoles;
        }

        // Group users by their roles
        var roleGroups = new Dictionary<string, List<string>>();

        foreach (var user in Users)
        {
            foreach (var roleName in user.Roles)
            {
                if (!string.IsNullOrEmpty(roleName) && roleName != "unknown")
                {
                    if (!roleGroups.ContainsKey(roleName))
                    {
                        roleGroups[roleName] = new List<string>();
                    }

                    roleGroups[roleName].Add(user.UserName);
                }
            }
        }

        // Create RoleType array from user data
        var roles = new List<UnifiedAutomation.UaSchema.RoleType>();

        foreach (var roleGroup in roleGroups)
        {
            string roleName = roleGroup.Key;

            RoleNameToNodeIdMapping.TryGetValue(roleName.ToLower(), out var nodeIdString);
            if (string.IsNullOrEmpty(nodeIdString))
            {
                continue;
            }

            List<string> userNames = roleGroup.Value;

            // Capitalize first letter of role name for display
            string displayName = char.ToUpper(roleName[0]) + roleName.Substring(1);

            var identities = userNames.Select(userName => new UnifiedAutomation.UaSchema.IdentityType()
            {
                CriteriaType = UnifiedAutomation.UaSchema.CriteriaType.UserName,
                Value = userName
            }).ToArray();

            roles.Add(new UnifiedAutomation.UaSchema.RoleType()
            {
                Name = displayName,
                NodeId = nodeIdString,
                Identities = identities
            });
        }

        roles.Add(new UnifiedAutomation.UaSchema.RoleType()
        {
            Name = "Observer",
            NodeId = "i=15668",
            Identities = new UnifiedAutomation.UaSchema.IdentityType[]
            {
                new UnifiedAutomation.UaSchema.IdentityType()
                {
                    CriteriaType = UnifiedAutomation.UaSchema.CriteriaType.AuthenticatedUser,
                }
            }
        });

        // Cache the result
        _cachedRoles = roles;
        return _cachedRoles;
    }

    /// <summary>
    /// Clear the cached role configurations (call when users are modified).
    /// </summary>
    public void ClearRoleCache()
    {
        _cachedRoles = null;
    }

    /// <summary>
    /// Create a new user and clear role cache.
    /// </summary>
    /// <param name="userName">The username.</param>
    /// <param name="password">The password.</param>
    /// <param name="roles">The role of the new user.</param>
    /// <returns>if user created successfully</returns>
    public override bool CreateUser(string userName, string password, IEnumerable<string> roles)
    {
        bool result = base.CreateUser(userName, password, roles);
        if (result)
        {
            ClearRoleCache();
        }

        return result;
    }

    /// <summary>
    /// Delete a user and clear role cache.
    /// </summary>
    /// <returns>if user deleted successfully</returns>
    public override bool DeleteUser(string userName)
    {
        bool result = base.DeleteUser(userName);
        if (result)
        {
            ClearRoleCache();
        }

        return result;
    }

    /// <summary>
    /// Force refresh role configurations (bypass cache)
    /// </summary>
    /// <returns>List of RoleType configurations</returns>
    public List<UnifiedAutomation.UaSchema.RoleType> RefreshRoleConfigurations()
    {
        ClearRoleCache();
        return GetRoleConfigurations();
    }

    /// <summary>
    /// Get all users from the database.
    /// </summary>
    /// <returns>List of users with username and roles.</returns>
    public override IList<User> GetUsers()
    {
        return Users.ToList();
    }

    /// <summary>
    /// Get or set the filename.
    /// </summary>
    [JsonIgnore]
    public string FileName { get { return _fileName; } private set { _fileName = value; } }

    private static readonly byte[] s_key = Encoding.UTF8.GetBytes("ThisIsASecretKeyForOpcUaDemo0123"); // Must be 32 bytes for AES-256

    private static byte[] Encrypt(string plainText)
    {
        using (Aes aes = Aes.Create())
        {
            aes.Key = s_key;
            aes.GenerateIV(); // Generate a new IV for each encryption
            var iv = aes.IV;

            var encryptor = aes.CreateEncryptor(aes.Key, iv);

            using (var ms = new MemoryStream())
            {
                ms.Write(iv, 0, iv.Length); // Prepend IV to the ciphertext
                using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                {
                    using (var sw = new StreamWriter(cs))
                    {
                        sw.Write(plainText);
                    }
                }

                return ms.ToArray();
            }
        }
    }

    private static string Decrypt(byte[] cipherText)
    {
        using (Aes aes = Aes.Create())
        {
            aes.Key = s_key;

            // The IV is the first 16 bytes of the ciphertext
            byte[] iv = new byte[16];
            Array.Copy(cipherText, 0, iv, 0, iv.Length);
            aes.IV = iv;

            var decryptor = aes.CreateDecryptor(aes.Key, iv);

            using (var ms = new MemoryStream(cipherText, iv.Length, cipherText.Length - iv.Length))
            {
                using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                {
                    using (var sr = new StreamReader(cs))
                    {
                        return sr.ReadToEnd();
                    }
                }
            }
        }
    }
}
