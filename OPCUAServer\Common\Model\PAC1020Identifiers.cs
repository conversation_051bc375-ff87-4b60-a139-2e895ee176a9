using UnifiedAutomation.UaBase;

namespace Sentron.PAC1020;

#region DataType Identifiers
/// <summary>
/// A class that declares constants for all DataTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypes
{
}
#endregion

#region Object Identifiers
/// <summary>
/// A class that declares constants for all Objects in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Objects
{
    /// <summary>
    /// The identifier for the http://sentron.org/PAC1020/ Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC1020_ = 5001;

}
#endregion

#region ObjectType Identifiers
/// <summary>
/// A class that declares constants for all ObjectTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypes
{
    /// <summary>
    /// The identifier for the PAC1020 ObjectType.
    /// </summary>
    public const uint PAC1020 = 1003;

}
#endregion

#region Method Identifiers
/// <summary>
/// A class that declares constants for all Methods in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Methods
{
}
#endregion

#region ReferenceType Identifiers
/// <summary>
/// A class that declares constants for all ReferenceTyped in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypes
{
}
#endregion

#region Variable Identifiers
/// <summary>
/// A class that declares constants for all Variables in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Variables
{
    /// <summary>
    /// The identifier for the ActiveEnergy Variable.
    /// </summary>
    public const uint PAC1020_ActiveEnergy = 6117;

    /// <summary>
    /// The identifier for the DI_0.0 Variable.
    /// </summary>
    public const uint PAC1020_DI_0_0 = 6120;

    /// <summary>
    /// The identifier for the DO_0.0 Variable.
    /// </summary>
    public const uint PAC1020_DO_0_0 = 6119;

    /// <summary>
    /// The identifier for the F Variable.
    /// </summary>
    public const uint PAC1020_F = 6116;

    /// <summary>
    /// The identifier for the Ia Variable.
    /// </summary>
    public const uint PAC1020_Ia = 6107;

    /// <summary>
    /// The identifier for the Ib Variable.
    /// </summary>
    public const uint PAC1020_Ib = 6108;

    /// <summary>
    /// The identifier for the Ic Variable.
    /// </summary>
    public const uint PAC1020_Ic = 6109;

    /// <summary>
    /// The identifier for the In Variable.
    /// </summary>
    public const uint PAC1020_In = 6110;

    /// <summary>
    /// The identifier for the IsConnected Variable.
    /// </summary>
    public const uint PAC1020_IsConnected = 6100;

    /// <summary>
    /// The identifier for the P Variable.
    /// </summary>
    public const uint PAC1020_P = 6111;

    /// <summary>
    /// The identifier for the PowFactor_A Variable.
    /// </summary>
    public const uint PAC1020_PowFactor_A = 6113;

    /// <summary>
    /// The identifier for the PowFactor_B Variable.
    /// </summary>
    public const uint PAC1020_PowFactor_B = 6114;

    /// <summary>
    /// The identifier for the PowFactor_C Variable.
    /// </summary>
    public const uint PAC1020_PowFactor_C = 6115;

    /// <summary>
    /// The identifier for the PrimaryCurrent Variable.
    /// </summary>
    public const uint PAC1020_PrimaryCurrent = 6124;

    /// <summary>
    /// The identifier for the PrimaryVoltage Variable.
    /// </summary>
    public const uint PAC1020_PrimaryVoltage = 6122;

    /// <summary>
    /// The identifier for the Q Variable.
    /// </summary>
    public const uint PAC1020_Q = 6112;

    /// <summary>
    /// The identifier for the ReactiveEnergy Variable.
    /// </summary>
    public const uint PAC1020_ReactiveEnergy = 6118;

    /// <summary>
    /// The identifier for the SecondaryCurrent Variable.
    /// </summary>
    public const uint PAC1020_SecondaryCurrent = 6125;

    /// <summary>
    /// The identifier for the SecondaryVoltage Variable.
    /// </summary>
    public const uint PAC1020_SecondaryVoltage = 6123;

    /// <summary>
    /// The identifier for the Ua Variable.
    /// </summary>
    public const uint PAC1020_Ua = 6101;

    /// <summary>
    /// The identifier for the Uab Variable.
    /// </summary>
    public const uint PAC1020_Uab = 6104;

    /// <summary>
    /// The identifier for the Ub Variable.
    /// </summary>
    public const uint PAC1020_Ub = 6102;

    /// <summary>
    /// The identifier for the Ubc Variable.
    /// </summary>
    public const uint PAC1020_Ubc = 6105;

    /// <summary>
    /// The identifier for the Uc Variable.
    /// </summary>
    public const uint PAC1020_Uc = 6103;

    /// <summary>
    /// The identifier for the Uca Variable.
    /// </summary>
    public const uint PAC1020_Uca = 6106;

    /// <summary>
    /// The identifier for the UseVoltageTransformer Variable.
    /// </summary>
    public const uint PAC1020_UseVoltageTransformer = 6121;

    /// <summary>
    /// The identifier for the IsNamespaceSubset Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC1020__IsNamespaceSubset = 6002;

    /// <summary>
    /// The identifier for the NamespacePublicationDate Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC1020__NamespacePublicationDate = 6003;

    /// <summary>
    /// The identifier for the NamespaceUri Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC1020__NamespaceUri = 6004;

    /// <summary>
    /// The identifier for the NamespaceVersion Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC1020__NamespaceVersion = 6005;

    /// <summary>
    /// The identifier for the StaticNodeIdTypes Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC1020__StaticNodeIdTypes = 6006;

    /// <summary>
    /// The identifier for the StaticNumericNodeIdRange Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC1020__StaticNumericNodeIdRange = 6007;

    /// <summary>
    /// The identifier for the StaticStringNodeIdPattern Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC1020__StaticStringNodeIdPattern = 6008;

}
#endregion

#region VariableTypes Identifiers
/// <summary>
/// A class that declares constants for all VariableTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypes
{
}
#endregion

#region DataType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all DataTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypeIds
{
}
#endregion

#region Method Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Methods in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class MethodIds
{
}
#endregion

#region Object Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectIds
{
    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC1020_ Object.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC1020_ = new ExpandedNodeId(Objects.Namespaces_http___sentron_org_PAC1020_, Namespaces.PAC1020);

}
#endregion

#region ObjectType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypeIds
{
    /// <summary>
    /// The identifier for the PAC1020 ObjectType.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020 = new ExpandedNodeId(ObjectTypes.PAC1020, Namespaces.PAC1020);

}
#endregion

#region ReferenceType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all ReferenceTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypeIds
{
}
#endregion

#region Variable Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Variables in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableIds
{
    /// <summary>
    /// The identifier for the PAC1020_ActiveEnergy Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_ActiveEnergy = new ExpandedNodeId(Variables.PAC1020_ActiveEnergy, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_DI_0_0 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_DI_0_0 = new ExpandedNodeId(Variables.PAC1020_DI_0_0, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_DO_0_0 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_DO_0_0 = new ExpandedNodeId(Variables.PAC1020_DO_0_0, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_F Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_F = new ExpandedNodeId(Variables.PAC1020_F, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_Ia Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_Ia = new ExpandedNodeId(Variables.PAC1020_Ia, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_Ib Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_Ib = new ExpandedNodeId(Variables.PAC1020_Ib, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_Ic Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_Ic = new ExpandedNodeId(Variables.PAC1020_Ic, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_In Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_In = new ExpandedNodeId(Variables.PAC1020_In, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_IsConnected Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_IsConnected = new ExpandedNodeId(Variables.PAC1020_IsConnected, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_P Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_P = new ExpandedNodeId(Variables.PAC1020_P, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_PowFactor_A Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_PowFactor_A = new ExpandedNodeId(Variables.PAC1020_PowFactor_A, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_PowFactor_B Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_PowFactor_B = new ExpandedNodeId(Variables.PAC1020_PowFactor_B, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_PowFactor_C Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_PowFactor_C = new ExpandedNodeId(Variables.PAC1020_PowFactor_C, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_PrimaryCurrent Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_PrimaryCurrent = new ExpandedNodeId(Variables.PAC1020_PrimaryCurrent, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_PrimaryVoltage Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_PrimaryVoltage = new ExpandedNodeId(Variables.PAC1020_PrimaryVoltage, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_Q Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_Q = new ExpandedNodeId(Variables.PAC1020_Q, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_ReactiveEnergy Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_ReactiveEnergy = new ExpandedNodeId(Variables.PAC1020_ReactiveEnergy, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_SecondaryCurrent Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_SecondaryCurrent = new ExpandedNodeId(Variables.PAC1020_SecondaryCurrent, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_SecondaryVoltage Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_SecondaryVoltage = new ExpandedNodeId(Variables.PAC1020_SecondaryVoltage, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_Ua Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_Ua = new ExpandedNodeId(Variables.PAC1020_Ua, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_Uab Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_Uab = new ExpandedNodeId(Variables.PAC1020_Uab, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_Ub Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_Ub = new ExpandedNodeId(Variables.PAC1020_Ub, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_Ubc Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_Ubc = new ExpandedNodeId(Variables.PAC1020_Ubc, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_Uc Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_Uc = new ExpandedNodeId(Variables.PAC1020_Uc, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_Uca Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_Uca = new ExpandedNodeId(Variables.PAC1020_Uca, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the PAC1020_UseVoltageTransformer Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC1020_UseVoltageTransformer = new ExpandedNodeId(Variables.PAC1020_UseVoltageTransformer, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC1020__IsNamespaceSubset Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC1020__IsNamespaceSubset = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC1020__IsNamespaceSubset, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC1020__NamespacePublicationDate Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC1020__NamespacePublicationDate = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC1020__NamespacePublicationDate, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC1020__NamespaceUri Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC1020__NamespaceUri = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC1020__NamespaceUri, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC1020__NamespaceVersion Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC1020__NamespaceVersion = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC1020__NamespaceVersion, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC1020__StaticNodeIdTypes Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC1020__StaticNodeIdTypes = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC1020__StaticNodeIdTypes, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC1020__StaticNumericNodeIdRange Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC1020__StaticNumericNodeIdRange = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC1020__StaticNumericNodeIdRange, Namespaces.PAC1020);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC1020__StaticStringNodeIdPattern Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC1020__StaticStringNodeIdPattern = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC1020__StaticStringNodeIdPattern, Namespaces.PAC1020);

}
#endregion

#region VariableType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all VariableType in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypeIds
{
}
#endregion

#region BrowseName Declarations
/// <summary>
/// Declares all of the BrowseNames used in the Model.
/// </summary>
public static partial class BrowseNames
{
    /// <summary>
    /// The BrowseName for the ActiveEnergy component.
    /// </summary>
    public const string ActiveEnergy = "ActiveEnergy";
    /// <summary>
    /// The BrowseName for the DI_0.0 component.
    /// </summary>
    public const string DI00 = "DI_0.0";
    /// <summary>
    /// The BrowseName for the DO_0.0 component.
    /// </summary>
    public const string DO00 = "DO_0.0";
    /// <summary>
    /// The BrowseName for the F component.
    /// </summary>
    public const string F = "F";
    /// <summary>
    /// The BrowseName for the Ia component.
    /// </summary>
    public const string Ia = "Ia";
    /// <summary>
    /// The BrowseName for the Ib component.
    /// </summary>
    public const string Ib = "Ib";
    /// <summary>
    /// The BrowseName for the Ic component.
    /// </summary>
    public const string Ic = "Ic";
    /// <summary>
    /// The BrowseName for the In component.
    /// </summary>
    public const string In = "In";
    /// <summary>
    /// The BrowseName for the IsConnected component.
    /// </summary>
    public const string IsConnected = "IsConnected";
    /// <summary>
    /// The BrowseName for the P component.
    /// </summary>
    public const string P = "P";
    /// <summary>
    /// The BrowseName for the PAC1020 component.
    /// </summary>
    public const string PAC1020 = "PAC1020";
    /// <summary>
    /// The BrowseName for the PowFactor_A component.
    /// </summary>
    public const string PowFactorA = "PowFactor_A";
    /// <summary>
    /// The BrowseName for the PowFactor_B component.
    /// </summary>
    public const string PowFactorB = "PowFactor_B";
    /// <summary>
    /// The BrowseName for the PowFactor_C component.
    /// </summary>
    public const string PowFactorC = "PowFactor_C";
    /// <summary>
    /// The BrowseName for the PrimaryCurrent component.
    /// </summary>
    public const string PrimaryCurrent = "PrimaryCurrent";
    /// <summary>
    /// The BrowseName for the PrimaryVoltage component.
    /// </summary>
    public const string PrimaryVoltage = "PrimaryVoltage";
    /// <summary>
    /// The BrowseName for the Q component.
    /// </summary>
    public const string Q = "Q";
    /// <summary>
    /// The BrowseName for the ReactiveEnergy component.
    /// </summary>
    public const string ReactiveEnergy = "ReactiveEnergy";
    /// <summary>
    /// The BrowseName for the SecondaryCurrent component.
    /// </summary>
    public const string SecondaryCurrent = "SecondaryCurrent";
    /// <summary>
    /// The BrowseName for the SecondaryVoltage component.
    /// </summary>
    public const string SecondaryVoltage = "SecondaryVoltage";
    /// <summary>
    /// The BrowseName for the Ua component.
    /// </summary>
    public const string Ua = "Ua";
    /// <summary>
    /// The BrowseName for the Uab component.
    /// </summary>
    public const string Uab = "Uab";
    /// <summary>
    /// The BrowseName for the Ub component.
    /// </summary>
    public const string Ub = "Ub";
    /// <summary>
    /// The BrowseName for the Ubc component.
    /// </summary>
    public const string Ubc = "Ubc";
    /// <summary>
    /// The BrowseName for the Uc component.
    /// </summary>
    public const string Uc = "Uc";
    /// <summary>
    /// The BrowseName for the Uca component.
    /// </summary>
    public const string Uca = "Uca";
    /// <summary>
    /// The BrowseName for the UseVoltageTransformer component.
    /// </summary>
    public const string UseVoltageTransformer = "UseVoltageTransformer";
    /// <summary>
    /// The BrowseName for the http://sentron.org/PAC1020/ component.
    /// </summary>
    public const string httpSentronOrgPAC1020 = "http://sentron.org/PAC1020/";
}
#endregion

#region Namespace Declarations
/// <summary>
/// Defines constants for all namespaces referenced by the Model.
/// </summary>
public static partial class Namespaces
{
    /// <summary>
    /// The URI for the OpcUa namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUa = "http://opcfoundation.org/UA/";

    /// <summary>
    /// The URI for the OpcUaXsd namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUaXsd = "http://opcfoundation.org/UA/2008/02/Types.xsd";

    /// <summary>
    /// The URI for the PAC1020 namespace.
    /// </summary>
    public const string PAC1020 = "http://sentron.org/PAC1020/";

    /// <summary>
    /// The URI for the PAC1020Xsd namespace.
    /// </summary>
    public const string PAC1020Xsd = "http://sentron.org/PAC1020/Types.xsd";
}
#endregion

