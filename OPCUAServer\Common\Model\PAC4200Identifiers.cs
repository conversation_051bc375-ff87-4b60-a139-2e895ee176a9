using UnifiedAutomation.UaBase;

namespace Sentron.PAC4200;

#region DataType Identifiers
/// <summary>
/// A class that declares constants for all DataTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypes
{
}
#endregion

#region Object Identifiers
/// <summary>
/// A class that declares constants for all Objects in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Objects
{
    /// <summary>
    /// The identifier for the http://sentron.org/PAC4200/ Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC4200_ = 5001;

}
#endregion

#region ObjectType Identifiers
/// <summary>
/// A class that declares constants for all ObjectTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypes
{
    /// <summary>
    /// The identifier for the PAC4200 ObjectType.
    /// </summary>
    public const uint PAC4200Controller = 1003;
    public const uint PAC4200EventController = 1004;

}
#endregion

#region Method Identifiers
/// <summary>
/// A class that declares constants for all Methods in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Methods
{
}
#endregion

#region ReferenceType Identifiers
/// <summary>
/// A class that declares constants for all ReferenceTyped in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypes
{
}
#endregion

#region Variable Identifiers
/// <summary>
/// A class that declares constants for all Variables in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Variables
{
    /// <summary>
    /// The identifier for the ActualTariff Variable.
    /// </summary>
    public const uint PAC4200_ActualTariff = 6170;

    /// <summary>
    /// The identifier for the DI_0.0 Variable.
    /// </summary>
    public const uint PAC4200_DI_0_0 = 6142;

    /// <summary>
    /// The identifier for the DI_0.1 Variable.
    /// </summary>
    public const uint PAC4200_DI_0_1 = 6143;

    /// <summary>
    /// The identifier for the DI_4.0 Variable.
    /// </summary>
    public const uint PAC4200_DI_4_0 = 6144;

    /// <summary>
    /// The identifier for the DI_4.1 Variable.
    /// </summary>
    public const uint PAC4200_DI_4_1 = 6145;

    /// <summary>
    /// The identifier for the DI_4.2 Variable.
    /// </summary>
    public const uint PAC4200_DI_4_2 = 6146;

    /// <summary>
    /// The identifier for the DI_4.3 Variable.
    /// </summary>
    public const uint PAC4200_DI_4_3 = 6147;

    /// <summary>
    /// The identifier for the DI_8.0 Variable.
    /// </summary>
    public const uint PAC4200_DI_8_0 = 6148;

    /// <summary>
    /// The identifier for the DI_8.1 Variable.
    /// </summary>
    public const uint PAC4200_DI_8_1 = 6149;

    /// <summary>
    /// The identifier for the DI_8.2 Variable.
    /// </summary>
    public const uint PAC4200_DI_8_2 = 6150;

    /// <summary>
    /// The identifier for the DI_8.3 Variable.
    /// </summary>
    public const uint PAC4200_DI_8_3 = 6151;

    /// <summary>
    /// The identifier for the DO_0.0 Variable.
    /// </summary>
    public const uint PAC4200_DO_0_0 = 6136;

    /// <summary>
    /// The identifier for the DO_0.1 Variable.
    /// </summary>
    public const uint PAC4200_DO_0_1 = 6137;

    /// <summary>
    /// The identifier for the DO_4.0 Variable.
    /// </summary>
    public const uint PAC4200_DO_4_0 = 6138;

    /// <summary>
    /// The identifier for the DO_4.1 Variable.
    /// </summary>
    public const uint PAC4200_DO_4_1 = 6139;

    /// <summary>
    /// The identifier for the DO_8.0 Variable.
    /// </summary>
    public const uint PAC4200_DO_8_0 = 6140;

    /// <summary>
    /// The identifier for the DO_8.1 Variable.
    /// </summary>
    public const uint PAC4200_DO_8_1 = 6141;

    /// <summary>
    /// The identifier for the F Variable.
    /// </summary>
    public const uint PAC4200_F = 6117;

    /// <summary>
    /// The identifier for the ForwardActivePower Variable.
    /// </summary>
    public const uint PAC4200_ForwardActivePower = 6124;

    /// <summary>
    /// The identifier for the ForwardActivePower_Tariff1 Variable.
    /// </summary>
    public const uint PAC4200_ForwardActivePower_Tariff1 = 6128;

    /// <summary>
    /// The identifier for the ForwardActivePower_Tariff2 Variable.
    /// </summary>
    public const uint PAC4200_ForwardActivePower_Tariff2 = 6132;

    /// <summary>
    /// The identifier for the ForwardReactivePower Variable.
    /// </summary>
    public const uint PAC4200_ForwardReactivePower = 6125;

    /// <summary>
    /// The identifier for the ForwardReactivePower_Tariff1 Variable.
    /// </summary>
    public const uint PAC4200_ForwardReactivePower_Tariff1 = 6129;

    /// <summary>
    /// The identifier for the ForwardReactivePower_Tariff2 Variable.
    /// </summary>
    public const uint PAC4200_ForwardReactivePower_Tariff2 = 6133;

    /// <summary>
    /// The identifier for the Ia Variable.
    /// </summary>
    public const uint PAC4200_Ia = 6107;

    /// <summary>
    /// The identifier for the Ib Variable.
    /// </summary>
    public const uint PAC4200_Ib = 6108;

    /// <summary>
    /// The identifier for the Ic Variable.
    /// </summary>
    public const uint PAC4200_Ic = 6109;

    /// <summary>
    /// The identifier for the In Variable.
    /// </summary>
    public const uint PAC4200_In = 6110;

    /// <summary>
    /// The identifier for the IsConnected Variable.
    /// </summary>
    public const uint PAC4200_IsConnected = 6100;

    /// <summary>
    /// The identifier for the LimitMonitoring_0 Variable.
    /// </summary>
    public const uint PAC4200_LimitMonitoring_0 = 6152;

    /// <summary>
    /// The identifier for the LimitMonitoring_1 Variable.
    /// </summary>
    public const uint PAC4200_LimitMonitoring_1 = 6153;

    /// <summary>
    /// The identifier for the LimitMonitoring_10 Variable.
    /// </summary>
    public const uint PAC4200_LimitMonitoring_10 = 6162;

    /// <summary>
    /// The identifier for the LimitMonitoring_11 Variable.
    /// </summary>
    public const uint PAC4200_LimitMonitoring_11 = 6163;

    /// <summary>
    /// The identifier for the LimitMonitoring_2 Variable.
    /// </summary>
    public const uint PAC4200_LimitMonitoring_2 = 6154;

    /// <summary>
    /// The identifier for the LimitMonitoring_3 Variable.
    /// </summary>
    public const uint PAC4200_LimitMonitoring_3 = 6155;

    /// <summary>
    /// The identifier for the LimitMonitoring_4 Variable.
    /// </summary>
    public const uint PAC4200_LimitMonitoring_4 = 6156;

    /// <summary>
    /// The identifier for the LimitMonitoring_5 Variable.
    /// </summary>
    public const uint PAC4200_LimitMonitoring_5 = 6157;

    /// <summary>
    /// The identifier for the LimitMonitoring_6 Variable.
    /// </summary>
    public const uint PAC4200_LimitMonitoring_6 = 6158;

    /// <summary>
    /// The identifier for the LimitMonitoring_7 Variable.
    /// </summary>
    public const uint PAC4200_LimitMonitoring_7 = 6159;

    /// <summary>
    /// The identifier for the LimitMonitoring_8 Variable.
    /// </summary>
    public const uint PAC4200_LimitMonitoring_8 = 6160;

    /// <summary>
    /// The identifier for the LimitMonitoring_9 Variable.
    /// </summary>
    public const uint PAC4200_LimitMonitoring_9 = 6161;

    /// <summary>
    /// The identifier for the LogicFunction_1 Variable.
    /// </summary>
    public const uint PAC4200_LogicFunction_1 = 6165;

    /// <summary>
    /// The identifier for the LogicFunction_2 Variable.
    /// </summary>
    public const uint PAC4200_LogicFunction_2 = 6166;

    /// <summary>
    /// The identifier for the LogicFunction_3 Variable.
    /// </summary>
    public const uint PAC4200_LogicFunction_3 = 6167;

    /// <summary>
    /// The identifier for the LogicFunction_4 Variable.
    /// </summary>
    public const uint PAC4200_LogicFunction_4 = 6168;

    /// <summary>
    /// The identifier for the LogicResult Variable.
    /// </summary>
    public const uint PAC4200_LogicResult = 6164;

    /// <summary>
    /// The identifier for the OperatingHours Variable.
    /// </summary>
    public const uint PAC4200_OperatingHours = 6169;

    /// <summary>
    /// The identifier for the P Variable.
    /// </summary>
    public const uint PAC4200_P = 6111;

    /// <summary>
    /// The identifier for the PowFactor_A Variable.
    /// </summary>
    public const uint PAC4200_PowFactor_A = 6114;

    /// <summary>
    /// The identifier for the PowFactor_B Variable.
    /// </summary>
    public const uint PAC4200_PowFactor_B = 6115;

    /// <summary>
    /// The identifier for the PowFactor_C Variable.
    /// </summary>
    public const uint PAC4200_PowFactor_C = 6116;

    /// <summary>
    /// The identifier for the PrimaryCurrent Variable.
    /// </summary>
    public const uint PAC4200_PrimaryCurrent = 6176;

    /// <summary>
    /// The identifier for the PrimaryVoltage Variable.
    /// </summary>
    public const uint PAC4200_PrimaryVoltage = 6174;

    /// <summary>
    /// The identifier for the Q Variable.
    /// </summary>
    public const uint PAC4200_Q = 6112;

    /// <summary>
    /// The identifier for the ReverseActivePower Variable.
    /// </summary>
    public const uint PAC4200_ReverseActivePower = 6126;

    /// <summary>
    /// The identifier for the ReverseActivePower_Tariff1 Variable.
    /// </summary>
    public const uint PAC4200_ReverseActivePower_Tariff1 = 6130;

    /// <summary>
    /// The identifier for the ReverseActivePower_Tariff2 Variable.
    /// </summary>
    public const uint PAC4200_ReverseActivePower_Tariff2 = 6134;

    /// <summary>
    /// The identifier for the ReverseReactivePower Variable.
    /// </summary>
    public const uint PAC4200_ReverseReactivePower = 6127;

    /// <summary>
    /// The identifier for the ReverseReactivePower_Tariff1 Variable.
    /// </summary>
    public const uint PAC4200_ReverseReactivePower_Tariff1 = 6131;

    /// <summary>
    /// The identifier for the ReverseReactivePower_Tariff2 Variable.
    /// </summary>
    public const uint PAC4200_ReverseReactivePower_Tariff2 = 6135;

    /// <summary>
    /// The identifier for the S Variable.
    /// </summary>
    public const uint PAC4200_S = 6113;

    /// <summary>
    /// The identifier for the SecondaryCurrent Variable.
    /// </summary>
    public const uint PAC4200_SecondaryCurrent = 6177;

    /// <summary>
    /// The identifier for the SecondaryVoltage Variable.
    /// </summary>
    public const uint PAC4200_SecondaryVoltage = 6175;

    /// <summary>
    /// The identifier for the Slot_1 Variable.
    /// </summary>
    public const uint PAC4200_Slot_1 = 6171;

    /// <summary>
    /// The identifier for the Slot_2 Variable.
    /// </summary>
    public const uint PAC4200_Slot_2 = 6172;

    /// <summary>
    /// The identifier for the THD_Ia Variable.
    /// </summary>
    public const uint PAC4200_THD_Ia = 6121;

    /// <summary>
    /// The identifier for the THD_Ib Variable.
    /// </summary>
    public const uint PAC4200_THD_Ib = 6122;

    /// <summary>
    /// The identifier for the THD_Ic Variable.
    /// </summary>
    public const uint PAC4200_THD_Ic = 6123;

    /// <summary>
    /// The identifier for the THD_Ua Variable.
    /// </summary>
    public const uint PAC4200_THD_Ua = 6118;

    /// <summary>
    /// The identifier for the THD_Ub Variable.
    /// </summary>
    public const uint PAC4200_THD_Ub = 6119;

    /// <summary>
    /// The identifier for the THD_Uc Variable.
    /// </summary>
    public const uint PAC4200_THD_Uc = 6120;

    /// <summary>
    /// The identifier for the Ua Variable.
    /// </summary>
    public const uint PAC4200_Ua = 6101;

    /// <summary>
    /// The identifier for the Uab Variable.
    /// </summary>
    public const uint PAC4200_Uab = 6104;

    /// <summary>
    /// The identifier for the Ub Variable.
    /// </summary>
    public const uint PAC4200_Ub = 6102;

    /// <summary>
    /// The identifier for the Ubc Variable.
    /// </summary>
    public const uint PAC4200_Ubc = 6105;

    /// <summary>
    /// The identifier for the Uc Variable.
    /// </summary>
    public const uint PAC4200_Uc = 6103;

    /// <summary>
    /// The identifier for the Uca Variable.
    /// </summary>
    public const uint PAC4200_Uca = 6106;

    /// <summary>
    /// The identifier for the UseVoltageTransformer Variable.
    /// </summary>
    public const uint PAC4200_UseVoltageTransformer = 6173;

    /// <summary>
    /// The identifier for the IsNamespaceSubset Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC4200__IsNamespaceSubset = 6002;

    /// <summary>
    /// The identifier for the NamespacePublicationDate Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC4200__NamespacePublicationDate = 6003;

    /// <summary>
    /// The identifier for the NamespaceUri Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC4200__NamespaceUri = 6004;

    /// <summary>
    /// The identifier for the NamespaceVersion Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC4200__NamespaceVersion = 6005;

    /// <summary>
    /// The identifier for the StaticNodeIdTypes Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC4200__StaticNodeIdTypes = 6006;

    /// <summary>
    /// The identifier for the StaticNumericNodeIdRange Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC4200__StaticNumericNodeIdRange = 6007;

    /// <summary>
    /// The identifier for the StaticStringNodeIdPattern Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_PAC4200__StaticStringNodeIdPattern = 6008;

}
#endregion

#region VariableTypes Identifiers
/// <summary>
/// A class that declares constants for all VariableTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypes
{
}
#endregion

#region DataType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all DataTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypeIds
{
}
#endregion

#region Method Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Methods in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class MethodIds
{
}
#endregion

#region Object Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectIds
{
    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC4200_ Object.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC4200_ = new ExpandedNodeId(Objects.Namespaces_http___sentron_org_PAC4200_, Namespaces.PAC4200);

}
#endregion

#region ObjectType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypeIds
{
    /// <summary>
    /// The identifier for the PAC4200 ObjectType.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200 = new ExpandedNodeId(ObjectTypes.PAC4200Controller, Namespaces.PAC4200);

}
#endregion

#region ReferenceType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all ReferenceTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypeIds
{
}
#endregion

#region Variable Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Variables in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableIds
{
    /// <summary>
    /// The identifier for the PAC4200_ActualTariff Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_ActualTariff = new ExpandedNodeId(Variables.PAC4200_ActualTariff, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DI_0_0 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DI_0_0 = new ExpandedNodeId(Variables.PAC4200_DI_0_0, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DI_0_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DI_0_1 = new ExpandedNodeId(Variables.PAC4200_DI_0_1, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DI_4_0 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DI_4_0 = new ExpandedNodeId(Variables.PAC4200_DI_4_0, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DI_4_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DI_4_1 = new ExpandedNodeId(Variables.PAC4200_DI_4_1, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DI_4_2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DI_4_2 = new ExpandedNodeId(Variables.PAC4200_DI_4_2, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DI_4_3 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DI_4_3 = new ExpandedNodeId(Variables.PAC4200_DI_4_3, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DI_8_0 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DI_8_0 = new ExpandedNodeId(Variables.PAC4200_DI_8_0, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DI_8_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DI_8_1 = new ExpandedNodeId(Variables.PAC4200_DI_8_1, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DI_8_2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DI_8_2 = new ExpandedNodeId(Variables.PAC4200_DI_8_2, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DI_8_3 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DI_8_3 = new ExpandedNodeId(Variables.PAC4200_DI_8_3, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DO_0_0 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DO_0_0 = new ExpandedNodeId(Variables.PAC4200_DO_0_0, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DO_0_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DO_0_1 = new ExpandedNodeId(Variables.PAC4200_DO_0_1, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DO_4_0 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DO_4_0 = new ExpandedNodeId(Variables.PAC4200_DO_4_0, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DO_4_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DO_4_1 = new ExpandedNodeId(Variables.PAC4200_DO_4_1, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DO_8_0 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DO_8_0 = new ExpandedNodeId(Variables.PAC4200_DO_8_0, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_DO_8_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_DO_8_1 = new ExpandedNodeId(Variables.PAC4200_DO_8_1, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_F Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_F = new ExpandedNodeId(Variables.PAC4200_F, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_ForwardActivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_ForwardActivePower = new ExpandedNodeId(Variables.PAC4200_ForwardActivePower, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_ForwardActivePower_Tariff1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_ForwardActivePower_Tariff1 = new ExpandedNodeId(Variables.PAC4200_ForwardActivePower_Tariff1, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_ForwardActivePower_Tariff2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_ForwardActivePower_Tariff2 = new ExpandedNodeId(Variables.PAC4200_ForwardActivePower_Tariff2, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_ForwardReactivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_ForwardReactivePower = new ExpandedNodeId(Variables.PAC4200_ForwardReactivePower, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_ForwardReactivePower_Tariff1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_ForwardReactivePower_Tariff1 = new ExpandedNodeId(Variables.PAC4200_ForwardReactivePower_Tariff1, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_ForwardReactivePower_Tariff2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_ForwardReactivePower_Tariff2 = new ExpandedNodeId(Variables.PAC4200_ForwardReactivePower_Tariff2, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_Ia Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_Ia = new ExpandedNodeId(Variables.PAC4200_Ia, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_Ib Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_Ib = new ExpandedNodeId(Variables.PAC4200_Ib, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_Ic Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_Ic = new ExpandedNodeId(Variables.PAC4200_Ic, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_In Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_In = new ExpandedNodeId(Variables.PAC4200_In, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_IsConnected Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_IsConnected = new ExpandedNodeId(Variables.PAC4200_IsConnected, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LimitMonitoring_0 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LimitMonitoring_0 = new ExpandedNodeId(Variables.PAC4200_LimitMonitoring_0, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LimitMonitoring_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LimitMonitoring_1 = new ExpandedNodeId(Variables.PAC4200_LimitMonitoring_1, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LimitMonitoring_10 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LimitMonitoring_10 = new ExpandedNodeId(Variables.PAC4200_LimitMonitoring_10, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LimitMonitoring_11 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LimitMonitoring_11 = new ExpandedNodeId(Variables.PAC4200_LimitMonitoring_11, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LimitMonitoring_2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LimitMonitoring_2 = new ExpandedNodeId(Variables.PAC4200_LimitMonitoring_2, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LimitMonitoring_3 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LimitMonitoring_3 = new ExpandedNodeId(Variables.PAC4200_LimitMonitoring_3, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LimitMonitoring_4 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LimitMonitoring_4 = new ExpandedNodeId(Variables.PAC4200_LimitMonitoring_4, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LimitMonitoring_5 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LimitMonitoring_5 = new ExpandedNodeId(Variables.PAC4200_LimitMonitoring_5, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LimitMonitoring_6 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LimitMonitoring_6 = new ExpandedNodeId(Variables.PAC4200_LimitMonitoring_6, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LimitMonitoring_7 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LimitMonitoring_7 = new ExpandedNodeId(Variables.PAC4200_LimitMonitoring_7, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LimitMonitoring_8 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LimitMonitoring_8 = new ExpandedNodeId(Variables.PAC4200_LimitMonitoring_8, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LimitMonitoring_9 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LimitMonitoring_9 = new ExpandedNodeId(Variables.PAC4200_LimitMonitoring_9, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LogicFunction_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LogicFunction_1 = new ExpandedNodeId(Variables.PAC4200_LogicFunction_1, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LogicFunction_2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LogicFunction_2 = new ExpandedNodeId(Variables.PAC4200_LogicFunction_2, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LogicFunction_3 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LogicFunction_3 = new ExpandedNodeId(Variables.PAC4200_LogicFunction_3, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LogicFunction_4 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LogicFunction_4 = new ExpandedNodeId(Variables.PAC4200_LogicFunction_4, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_LogicResult Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_LogicResult = new ExpandedNodeId(Variables.PAC4200_LogicResult, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_OperatingHours Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_OperatingHours = new ExpandedNodeId(Variables.PAC4200_OperatingHours, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_P Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_P = new ExpandedNodeId(Variables.PAC4200_P, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_PowFactor_A Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_PowFactor_A = new ExpandedNodeId(Variables.PAC4200_PowFactor_A, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_PowFactor_B Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_PowFactor_B = new ExpandedNodeId(Variables.PAC4200_PowFactor_B, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_PowFactor_C Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_PowFactor_C = new ExpandedNodeId(Variables.PAC4200_PowFactor_C, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_PrimaryCurrent Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_PrimaryCurrent = new ExpandedNodeId(Variables.PAC4200_PrimaryCurrent, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_PrimaryVoltage Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_PrimaryVoltage = new ExpandedNodeId(Variables.PAC4200_PrimaryVoltage, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_Q Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_Q = new ExpandedNodeId(Variables.PAC4200_Q, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_ReverseActivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_ReverseActivePower = new ExpandedNodeId(Variables.PAC4200_ReverseActivePower, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_ReverseActivePower_Tariff1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_ReverseActivePower_Tariff1 = new ExpandedNodeId(Variables.PAC4200_ReverseActivePower_Tariff1, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_ReverseActivePower_Tariff2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_ReverseActivePower_Tariff2 = new ExpandedNodeId(Variables.PAC4200_ReverseActivePower_Tariff2, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_ReverseReactivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_ReverseReactivePower = new ExpandedNodeId(Variables.PAC4200_ReverseReactivePower, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_ReverseReactivePower_Tariff1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_ReverseReactivePower_Tariff1 = new ExpandedNodeId(Variables.PAC4200_ReverseReactivePower_Tariff1, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_ReverseReactivePower_Tariff2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_ReverseReactivePower_Tariff2 = new ExpandedNodeId(Variables.PAC4200_ReverseReactivePower_Tariff2, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_S Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_S = new ExpandedNodeId(Variables.PAC4200_S, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_SecondaryCurrent Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_SecondaryCurrent = new ExpandedNodeId(Variables.PAC4200_SecondaryCurrent, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_SecondaryVoltage Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_SecondaryVoltage = new ExpandedNodeId(Variables.PAC4200_SecondaryVoltage, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_Slot_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_Slot_1 = new ExpandedNodeId(Variables.PAC4200_Slot_1, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_Slot_2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_Slot_2 = new ExpandedNodeId(Variables.PAC4200_Slot_2, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_THD_Ia Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_THD_Ia = new ExpandedNodeId(Variables.PAC4200_THD_Ia, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_THD_Ib Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_THD_Ib = new ExpandedNodeId(Variables.PAC4200_THD_Ib, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_THD_Ic Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_THD_Ic = new ExpandedNodeId(Variables.PAC4200_THD_Ic, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_THD_Ua Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_THD_Ua = new ExpandedNodeId(Variables.PAC4200_THD_Ua, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_THD_Ub Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_THD_Ub = new ExpandedNodeId(Variables.PAC4200_THD_Ub, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_THD_Uc Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_THD_Uc = new ExpandedNodeId(Variables.PAC4200_THD_Uc, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_Ua Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_Ua = new ExpandedNodeId(Variables.PAC4200_Ua, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_Uab Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_Uab = new ExpandedNodeId(Variables.PAC4200_Uab, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_Ub Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_Ub = new ExpandedNodeId(Variables.PAC4200_Ub, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_Ubc Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_Ubc = new ExpandedNodeId(Variables.PAC4200_Ubc, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_Uc Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_Uc = new ExpandedNodeId(Variables.PAC4200_Uc, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_Uca Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_Uca = new ExpandedNodeId(Variables.PAC4200_Uca, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the PAC4200_UseVoltageTransformer Variable.
    /// </summary>
    public static readonly ExpandedNodeId PAC4200_UseVoltageTransformer = new ExpandedNodeId(Variables.PAC4200_UseVoltageTransformer, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC4200__IsNamespaceSubset Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC4200__IsNamespaceSubset = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC4200__IsNamespaceSubset, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC4200__NamespacePublicationDate Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC4200__NamespacePublicationDate = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC4200__NamespacePublicationDate, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC4200__NamespaceUri Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC4200__NamespaceUri = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC4200__NamespaceUri, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC4200__NamespaceVersion Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC4200__NamespaceVersion = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC4200__NamespaceVersion, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC4200__StaticNodeIdTypes Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC4200__StaticNodeIdTypes = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC4200__StaticNodeIdTypes, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC4200__StaticNumericNodeIdRange Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC4200__StaticNumericNodeIdRange = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC4200__StaticNumericNodeIdRange, Namespaces.PAC4200);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_PAC4200__StaticStringNodeIdPattern Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_PAC4200__StaticStringNodeIdPattern = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_PAC4200__StaticStringNodeIdPattern, Namespaces.PAC4200);

}
#endregion

#region VariableType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all VariableType in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypeIds
{
}
#endregion

#region BrowseName Declarations
/// <summary>
/// Declares all of the BrowseNames used in the Model.
/// </summary>
public static partial class BrowseNames
{
    /// <summary>
    /// The BrowseName for the ActualTariff component.
    /// </summary>
    public const string ActualTariff = "ActualTariff";
    /// <summary>
    /// The BrowseName for the DI_0.0 component.
    /// </summary>
    public const string DI00 = "DI_0.0";
    /// <summary>
    /// The BrowseName for the DI_0.1 component.
    /// </summary>
    public const string DI01 = "DI_0.1";
    /// <summary>
    /// The BrowseName for the DI_4.0 component.
    /// </summary>
    public const string DI40 = "DI_4.0";
    /// <summary>
    /// The BrowseName for the DI_4.1 component.
    /// </summary>
    public const string DI41 = "DI_4.1";
    /// <summary>
    /// The BrowseName for the DI_4.2 component.
    /// </summary>
    public const string DI42 = "DI_4.2";
    /// <summary>
    /// The BrowseName for the DI_4.3 component.
    /// </summary>
    public const string DI43 = "DI_4.3";
    /// <summary>
    /// The BrowseName for the DI_8.0 component.
    /// </summary>
    public const string DI80 = "DI_8.0";
    /// <summary>
    /// The BrowseName for the DI_8.1 component.
    /// </summary>
    public const string DI81 = "DI_8.1";
    /// <summary>
    /// The BrowseName for the DI_8.2 component.
    /// </summary>
    public const string DI82 = "DI_8.2";
    /// <summary>
    /// The BrowseName for the DI_8.3 component.
    /// </summary>
    public const string DI83 = "DI_8.3";
    /// <summary>
    /// The BrowseName for the DO_0.0 component.
    /// </summary>
    public const string DO00 = "DO_0.0";
    /// <summary>
    /// The BrowseName for the DO_0.1 component.
    /// </summary>
    public const string DO01 = "DO_0.1";
    /// <summary>
    /// The BrowseName for the DO_4.0 component.
    /// </summary>
    public const string DO40 = "DO_4.0";
    /// <summary>
    /// The BrowseName for the DO_4.1 component.
    /// </summary>
    public const string DO41 = "DO_4.1";
    /// <summary>
    /// The BrowseName for the DO_8.0 component.
    /// </summary>
    public const string DO80 = "DO_8.0";
    /// <summary>
    /// The BrowseName for the DO_8.1 component.
    /// </summary>
    public const string DO81 = "DO_8.1";
    /// <summary>
    /// The BrowseName for the F component.
    /// </summary>
    public const string F = "F";
    /// <summary>
    /// The BrowseName for the ForwardActivePower component.
    /// </summary>
    public const string ForwardActivePower = "ForwardActivePower";
    /// <summary>
    /// The BrowseName for the ForwardActivePower_Tariff1 component.
    /// </summary>
    public const string ForwardActivePowerTariff1 = "ForwardActivePower_Tariff1";
    /// <summary>
    /// The BrowseName for the ForwardActivePower_Tariff2 component.
    /// </summary>
    public const string ForwardActivePowerTariff2 = "ForwardActivePower_Tariff2";
    /// <summary>
    /// The BrowseName for the ForwardReactivePower component.
    /// </summary>
    public const string ForwardReactivePower = "ForwardReactivePower";
    /// <summary>
    /// The BrowseName for the ForwardReactivePower_Tariff1 component.
    /// </summary>
    public const string ForwardReactivePowerTariff1 = "ForwardReactivePower_Tariff1";
    /// <summary>
    /// The BrowseName for the ForwardReactivePower_Tariff2 component.
    /// </summary>
    public const string ForwardReactivePowerTariff2 = "ForwardReactivePower_Tariff2";
    /// <summary>
    /// The BrowseName for the Ia component.
    /// </summary>
    public const string Ia = "Ia";
    /// <summary>
    /// The BrowseName for the Ib component.
    /// </summary>
    public const string Ib = "Ib";
    /// <summary>
    /// The BrowseName for the Ic component.
    /// </summary>
    public const string Ic = "Ic";
    /// <summary>
    /// The BrowseName for the In component.
    /// </summary>
    public const string In = "In";
    /// <summary>
    /// The BrowseName for the IsConnected component.
    /// </summary>
    public const string IsConnected = "IsConnected";
    /// <summary>
    /// The BrowseName for the LimitMonitoring_0 component.
    /// </summary>
    public const string LimitMonitoring0 = "LimitMonitoring_0";
    /// <summary>
    /// The BrowseName for the LimitMonitoring_1 component.
    /// </summary>
    public const string LimitMonitoring1 = "LimitMonitoring_1";
    /// <summary>
    /// The BrowseName for the LimitMonitoring_10 component.
    /// </summary>
    public const string LimitMonitoring10 = "LimitMonitoring_10";
    /// <summary>
    /// The BrowseName for the LimitMonitoring_11 component.
    /// </summary>
    public const string LimitMonitoring11 = "LimitMonitoring_11";
    /// <summary>
    /// The BrowseName for the LimitMonitoring_2 component.
    /// </summary>
    public const string LimitMonitoring2 = "LimitMonitoring_2";
    /// <summary>
    /// The BrowseName for the LimitMonitoring_3 component.
    /// </summary>
    public const string LimitMonitoring3 = "LimitMonitoring_3";
    /// <summary>
    /// The BrowseName for the LimitMonitoring_4 component.
    /// </summary>
    public const string LimitMonitoring4 = "LimitMonitoring_4";
    /// <summary>
    /// The BrowseName for the LimitMonitoring_5 component.
    /// </summary>
    public const string LimitMonitoring5 = "LimitMonitoring_5";
    /// <summary>
    /// The BrowseName for the LimitMonitoring_6 component.
    /// </summary>
    public const string LimitMonitoring6 = "LimitMonitoring_6";
    /// <summary>
    /// The BrowseName for the LimitMonitoring_7 component.
    /// </summary>
    public const string LimitMonitoring7 = "LimitMonitoring_7";
    /// <summary>
    /// The BrowseName for the LimitMonitoring_8 component.
    /// </summary>
    public const string LimitMonitoring8 = "LimitMonitoring_8";
    /// <summary>
    /// The BrowseName for the LimitMonitoring_9 component.
    /// </summary>
    public const string LimitMonitoring9 = "LimitMonitoring_9";
    /// <summary>
    /// The BrowseName for the LogicFunction_1 component.
    /// </summary>
    public const string LogicFunction1 = "LogicFunction_1";
    /// <summary>
    /// The BrowseName for the LogicFunction_2 component.
    /// </summary>
    public const string LogicFunction2 = "LogicFunction_2";
    /// <summary>
    /// The BrowseName for the LogicFunction_3 component.
    /// </summary>
    public const string LogicFunction3 = "LogicFunction_3";
    /// <summary>
    /// The BrowseName for the LogicFunction_4 component.
    /// </summary>
    public const string LogicFunction4 = "LogicFunction_4";
    /// <summary>
    /// The BrowseName for the LogicResult component.
    /// </summary>
    public const string LogicResult = "LogicResult";
    /// <summary>
    /// The BrowseName for the OperatingHours component.
    /// </summary>
    public const string OperatingHours = "OperatingHours";
    /// <summary>
    /// The BrowseName for the P component.
    /// </summary>
    public const string P = "P";
    /// <summary>
    /// The BrowseName for the PAC4200 component.
    /// </summary>
    public const string PAC4200 = "PAC4200";
    /// <summary>
    /// The BrowseName for the PowFactor_A component.
    /// </summary>
    public const string PowFactorA = "PowFactor_A";
    /// <summary>
    /// The BrowseName for the PowFactor_B component.
    /// </summary>
    public const string PowFactorB = "PowFactor_B";
    /// <summary>
    /// The BrowseName for the PowFactor_C component.
    /// </summary>
    public const string PowFactorC = "PowFactor_C";
    /// <summary>
    /// The BrowseName for the PrimaryCurrent component.
    /// </summary>
    public const string PrimaryCurrent = "PrimaryCurrent";
    /// <summary>
    /// The BrowseName for the PrimaryVoltage component.
    /// </summary>
    public const string PrimaryVoltage = "PrimaryVoltage";
    /// <summary>
    /// The BrowseName for the Q component.
    /// </summary>
    public const string Q = "Q";
    /// <summary>
    /// The BrowseName for the ReverseActivePower component.
    /// </summary>
    public const string ReverseActivePower = "ReverseActivePower";
    /// <summary>
    /// The BrowseName for the ReverseActivePower_Tariff1 component.
    /// </summary>
    public const string ReverseActivePowerTariff1 = "ReverseActivePower_Tariff1";
    /// <summary>
    /// The BrowseName for the ReverseActivePower_Tariff2 component.
    /// </summary>
    public const string ReverseActivePowerTariff2 = "ReverseActivePower_Tariff2";
    /// <summary>
    /// The BrowseName for the ReverseReactivePower component.
    /// </summary>
    public const string ReverseReactivePower = "ReverseReactivePower";
    /// <summary>
    /// The BrowseName for the ReverseReactivePower_Tariff1 component.
    /// </summary>
    public const string ReverseReactivePowerTariff1 = "ReverseReactivePower_Tariff1";
    /// <summary>
    /// The BrowseName for the ReverseReactivePower_Tariff2 component.
    /// </summary>
    public const string ReverseReactivePowerTariff2 = "ReverseReactivePower_Tariff2";
    /// <summary>
    /// The BrowseName for the S component.
    /// </summary>
    public const string S = "S";
    /// <summary>
    /// The BrowseName for the SecondaryCurrent component.
    /// </summary>
    public const string SecondaryCurrent = "SecondaryCurrent";
    /// <summary>
    /// The BrowseName for the SecondaryVoltage component.
    /// </summary>
    public const string SecondaryVoltage = "SecondaryVoltage";
    /// <summary>
    /// The BrowseName for the Slot_1 component.
    /// </summary>
    public const string Slot1 = "Slot_1";
    /// <summary>
    /// The BrowseName for the Slot_2 component.
    /// </summary>
    public const string Slot2 = "Slot_2";
    /// <summary>
    /// The BrowseName for the THD_Ia component.
    /// </summary>
    public const string THDIa = "THD_Ia";
    /// <summary>
    /// The BrowseName for the THD_Ib component.
    /// </summary>
    public const string THDIb = "THD_Ib";
    /// <summary>
    /// The BrowseName for the THD_Ic component.
    /// </summary>
    public const string THDIc = "THD_Ic";
    /// <summary>
    /// The BrowseName for the THD_Ua component.
    /// </summary>
    public const string THDUa = "THD_Ua";
    /// <summary>
    /// The BrowseName for the THD_Ub component.
    /// </summary>
    public const string THDUb = "THD_Ub";
    /// <summary>
    /// The BrowseName for the THD_Uc component.
    /// </summary>
    public const string THDUc = "THD_Uc";
    /// <summary>
    /// The BrowseName for the Ua component.
    /// </summary>
    public const string Ua = "Ua";
    /// <summary>
    /// The BrowseName for the Uab component.
    /// </summary>
    public const string Uab = "Uab";
    /// <summary>
    /// The BrowseName for the Ub component.
    /// </summary>
    public const string Ub = "Ub";
    /// <summary>
    /// The BrowseName for the Ubc component.
    /// </summary>
    public const string Ubc = "Ubc";
    /// <summary>
    /// The BrowseName for the Uc component.
    /// </summary>
    public const string Uc = "Uc";
    /// <summary>
    /// The BrowseName for the Uca component.
    /// </summary>
    public const string Uca = "Uca";
    /// <summary>
    /// The BrowseName for the UseVoltageTransformer component.
    /// </summary>
    public const string UseVoltageTransformer = "UseVoltageTransformer";
    /// <summary>
    /// The BrowseName for the http://sentron.org/PAC4200/ component.
    /// </summary>
    public const string httpSentronOrgPAC4200 = "http://sentron.org/PAC4200/";
}
#endregion

#region Namespace Declarations
/// <summary>
/// Defines constants for all namespaces referenced by the Model.
/// </summary>
public static partial class Namespaces
{
    /// <summary>
    /// The URI for the OpcUa namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUa = "http://opcfoundation.org/UA/";

    /// <summary>
    /// The URI for the OpcUaXsd namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUaXsd = "http://opcfoundation.org/UA/2008/02/Types.xsd";

    /// <summary>
    /// The URI for the PAC4200 namespace.
    /// </summary>
    public const string PAC4200 = "http://sentron.org/PAC4200/";

    /// <summary>
    /// The URI for the PAC4200Xsd namespace.
    /// </summary>
    public const string PAC4200Xsd = "http://sentron.org/PAC4200/Types.xsd";
}
#endregion

