using OPCUAServer.Common;
using Serilog;
using UnifiedAutomation.UaBase;

namespace OPCUAServer.Utilites;

/// <summary>
/// A class that provides utility methods for converting data types.
/// </summary>
public static class DataTypeConvert
{
    private static readonly ILogger _logger = LoggingHelper.GetLogger(nameof(DataTypeConvert));

    public static object? ConvertValueToDataType(RegisterValue? registerValue, NodeId expectedDataType)
    {
        if (registerValue?.Value == null)
        {
            _logger.Warning($"Failed to convert value from {registerValue?.DataType} to {expectedDataType}: value is null");
            return null;
        }

        var value = registerValue.Value;

        if (registerValue.DataType.Equals(expectedDataType))
        {
            return value;
        }

        try
        {
            return (uint)expectedDataType.Identifier switch
            {
                DataTypes.Int32 => value switch
                {
                    int i => i,
                    string s => int.Parse(s),
                    double d => (int)d,
                    float f => (int)f,
                    _ => Convert.ToInt32(value)
                },
                DataTypes.Double => value switch
                {
                    double d => d,
                    string s => double.Parse(s),
                    int i => (double)i,
                    float f => (double)f,
                    _ => Convert.ToDouble(value)
                },
                DataTypes.String => value.ToString(),
                DataTypes.Boolean => value switch
                {
                    bool b => b,
                    string s => bool.Parse(s),
                    int i => i != 0,
                    _ => Convert.ToBoolean(value)
                },
                _ => value
            };
        }
        catch (Exception ex)
        {
            _logger.Warning($"Failed to convert value {value} from {registerValue.DataType} to {expectedDataType}: {ex.Message}");
            return value;
        }
    }
}

