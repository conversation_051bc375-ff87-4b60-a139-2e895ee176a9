<?xml version="1.0" encoding="utf-8"?>
<UANodeSet xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:uax="http://opcfoundation.org/UA/2008/02/Types.xsd" xmlns="http://opcfoundation.org/UA/2011/03/UANodeSet.xsd" xmlns:s1="http://yourorganisation.org/BuildingAutomation/Types.xsd" xmlns:ua="http://unifiedautomation.com/Configuration/NodeSet.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <NamespaceUris>
        <Uri>http://sentron.org/UserManagement/</Uri>
    </NamespaceUris>
    <Aliases>
        <Alias Alias="Boolean">i=1</Alias>
        <Alias Alias="UInt16">i=5</Alias>
        <Alias Alias="Int32">i=6</Alias>
        <Alias Alias="Double">i=11</Alias>
        <Alias Alias="String">i=12</Alias>
        <Alias Alias="ByteString">i=15</Alias>
        <Alias Alias="NodeId">i=17</Alias>
        <Alias Alias="StatusCode">i=19</Alias>
        <Alias Alias="LocalizedText">i=21</Alias>
        <Alias Alias="HasModellingRule">i=37</Alias>
        <Alias Alias="HasTypeDefinition">i=40</Alias>
        <Alias Alias="HasSubtype">i=45</Alias>
        <Alias Alias="HasProperty">i=46</Alias>
        <Alias Alias="HasComponent">i=47</Alias>
        <Alias Alias="UtcTime">i=294</Alias>
        <Alias Alias="Argument">i=296</Alias>
        <Alias Alias="Range">i=884</Alias>
        <Alias Alias="AlwaysGeneratesEvent">i=3065</Alias>
        <Alias Alias="TimeZoneDataType">i=8912</Alias>
        <Alias Alias="HasTrueSubState">i=9004</Alias>
    </Aliases>
    <Extensions>
        <Extension>
            <ua:ModelInfo Tool="UaModeler" Hash="MODIFIED_BY_AI_ASSISTANT" Version="1.0.0"/>
        </Extension>
    </Extensions>

    <UAObjectType NodeId="ns=1;i=1001" BrowseName="1:ControllerType">
        <DisplayName>ControllerType</DisplayName>
        <References>
            <Reference ReferenceType="HasSubtype" IsForward="false">i=58</Reference>
            <Reference ReferenceType="HasComponent">ns=1;i=7001</Reference>
            <Reference ReferenceType="HasComponent">ns=1;i=7002</Reference>
            <Reference ReferenceType="HasComponent">ns=1;i=7003</Reference>
            <Reference ReferenceType="HasComponent">ns=1;i=7004</Reference>
            <Reference ReferenceType="HasComponent">ns=1;i=7005</Reference>
           <Reference ReferenceType="HasComponent">ns=1;i=6001</Reference>
           <Reference ReferenceType="HasComponent">ns=1;i=6002</Reference>
        </References>
    </UAObjectType>

    <!-- AddUser Method for ControllerType -->
    <UAMethod ParentNodeId="ns=1;i=1001" NodeId="ns=1;i=7001" BrowseName="1:AddUser">
        <DisplayName>AddUser</DisplayName>
        <References>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1001</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=8001</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=8002</Reference>
        </References>
    </UAMethod>

    <!-- Definition for the InputArguments of the AddUser method -->
   <UAVariable ParentNodeId="ns=1;i=7001" NodeId="ns=1;i=8001" BrowseName="InputArguments" DataType="i=296">
       <DisplayName>InputArguments</DisplayName>
       <References>
           <Reference ReferenceType="HasModellingRule">i=78</Reference>
           <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=7001</Reference>
       </References>
       <Value>
           <uax:ListOfExtensionObject>
               <uax:ExtensionObject>
                   <uax:TypeId>
                       <uax:Identifier>i=297</uax:Identifier>
                   </uax:TypeId>
                   <uax:Body>
                       <uax:Argument>
                           <uax:Name>username</uax:Name>
                           <uax:DataType>
                               <uax:Identifier>i=12</uax:Identifier>
                           </uax:DataType>
                           <uax:ValueRank>-1</uax:ValueRank>
                           <uax:ArrayDimensions />
                           <uax:Description><uax:Text>The name of the user to be added.</uax:Text></uax:Description>
                      </uax:Argument>
                   </uax:Body>
               </uax:ExtensionObject>
               <uax:ExtensionObject>
                   <uax:TypeId>
                       <uax:Identifier>i=297</uax:Identifier>
                   </uax:TypeId>
                   <uax:Body>
                       <uax:Argument>
                           <uax:Name>password</uax:Name>
                           <uax:DataType>
                               <uax:Identifier>i=12</uax:Identifier>
                           </uax:DataType>
                           <uax:ValueRank>-1</uax:ValueRank>
                           <uax:ArrayDimensions />
                           <uax:Description><uax:Text>The password for the new user. Requirements: minimum 10 characters, at least 1 digit, 1 special character, and at least one uppercase and one lowercase letter.</uax:Text></uax:Description>
                       </uax:Argument>
                   </uax:Body>
               </uax:ExtensionObject>
               <!-- <uax:ExtensionObject>
                   <uax:TypeId>
                       <uax:Identifier>i=297</uax:Identifier>
                   </uax:TypeId>
                   <uax:Body>
                       <uax:Argument>
                           <uax:Name>roleName</uax:Name>
                           <uax:DataType>
                               <uax:Identifier>i=12</uax:Identifier>
                           </uax:DataType>
                           <uax:ValueRank>-1</uax:ValueRank>
                           <uax:ArrayDimensions />
                           <uax:Description><uax:Text>The ID of the role to assign to the user.</uax:Text></uax:Description>
                       </uax:Argument>
                   </uax:Body>
               </uax:ExtensionObject> -->
           </uax:ListOfExtensionObject>
       </Value>
   </UAVariable>

    <!-- RemoveUser Method for ControllerType -->
    <UAMethod ParentNodeId="ns=1;i=1001" NodeId="ns=1;i=7002" BrowseName="1:RemoveUser">
        <DisplayName>RemoveUser</DisplayName>
        <References>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1001</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=8004</Reference>
        </References>
    </UAMethod>

    <!-- Definition for the InputArguments of the RemoveUser method -->
    <UAVariable ParentNodeId="ns=1;i=7002" NodeId="ns=1;i=8004" BrowseName="InputArguments" DataType="i=296">
        <DisplayName>InputArguments</DisplayName>
        <References>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=7002</Reference>
        </References>
        <Value>
            <uax:ListOfExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>username</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=12</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>-1</uax:ValueRank>
                            <uax:ArrayDimensions />
                            <uax:Description><uax:Text>The name of the user to be removed.</uax:Text></uax:Description>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
            </uax:ListOfExtensionObject>
        </Value>
    </UAVariable>

    <!-- GetUsers Method for ControllerType -->
    <UAMethod ParentNodeId="ns=1;i=1001" NodeId="ns=1;i=7003" BrowseName="1:GetUsers">
        <DisplayName>GetUsers</DisplayName>
        <References>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1001</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=8003</Reference>
        </References>
    </UAMethod>

    <!-- Definition for the OutputArguments of the GetUsers method -->
    <UAVariable ParentNodeId="ns=1;i=7003" NodeId="ns=1;i=8003" BrowseName="OutputArguments" DataType="i=296">
        <DisplayName>OutputArguments</DisplayName>
        <References>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=7003</Reference>
        </References>
        <Value>
            <uax:ListOfExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>users</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=12</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>1</uax:ValueRank>
                            <uax:ArrayDimensions />
                            <uax:Description><uax:Text>Array of user information in JSON format.</uax:Text></uax:Description>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
            </uax:ListOfExtensionObject>
        </Value>
    </UAVariable>

    <!-- ChangePassword Method for ControllerType -->
    <UAMethod ParentNodeId="ns=1;i=1001" NodeId="ns=1;i=7004" BrowseName="1:ChangePassword">
        <DisplayName>ChangePassword</DisplayName>
        <References>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1001</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=8005</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=8006</Reference>
        </References>
    </UAMethod>

    <!-- Definition for the InputArguments of the ChangePassword method -->
    <UAVariable ParentNodeId="ns=1;i=7004" NodeId="ns=1;i=8005" BrowseName="InputArguments" DataType="i=296">
        <DisplayName>InputArguments</DisplayName>
        <References>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=7004</Reference>
        </References>
        <Value>
            <uax:ListOfExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>username</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=12</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>-1</uax:ValueRank>
                            <uax:ArrayDimensions />
                            <uax:Description><uax:Text>The name of the user whose password will be changed.</uax:Text></uax:Description>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>oldPassword</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=12</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>-1</uax:ValueRank>
                            <uax:ArrayDimensions />
                            <uax:Description><uax:Text>The current password of the user for verification.</uax:Text></uax:Description>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>newPassword</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=12</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>-1</uax:ValueRank>
                            <uax:ArrayDimensions />
                            <uax:Description><uax:Text>The new password for the user. Requirements: minimum 10 characters, at least 1 digit, 1 special character, and at least one uppercase and one lowercase letter.</uax:Text></uax:Description>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
            </uax:ListOfExtensionObject>
        </Value>
    </UAVariable>

    <!-- ResetPassword Method for ControllerType -->
    <UAMethod ParentNodeId="ns=1;i=1001" NodeId="ns=1;i=7005" BrowseName="1:ResetPassword">
        <DisplayName>ResetPassword</DisplayName>
        <References>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1001</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=8007</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=8008</Reference>
        </References>
    </UAMethod>

    <!-- Definition for the InputArguments of the ResetPassword method -->
    <UAVariable ParentNodeId="ns=1;i=7005" NodeId="ns=1;i=8007" BrowseName="InputArguments" DataType="i=296">
        <DisplayName>InputArguments</DisplayName>
        <References>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=7005</Reference>
        </References>
        <Value>
            <uax:ListOfExtensionObject>
                <uax:ExtensionObject>
                    <uax:TypeId>
                        <uax:Identifier>i=297</uax:Identifier>
                    </uax:TypeId>
                    <uax:Body>
                        <uax:Argument>
                            <uax:Name>username</uax:Name>
                            <uax:DataType>
                                <uax:Identifier>i=12</uax:Identifier>
                            </uax:DataType>
                            <uax:ValueRank>-1</uax:ValueRank>
                            <uax:ArrayDimensions />
                            <uax:Description><uax:Text>The name of the user whose password will be reset to the original password.</uax:Text></uax:Description>
                        </uax:Argument>
                    </uax:Body>
                </uax:ExtensionObject>
            </uax:ListOfExtensionObject>
        </Value>
    </UAVariable>

    <!-- UserManagementCControllerType inherits from ControllerType to get AddUser/RemoveUser methods -->
    <UAObjectType NodeId="ns=1;i=1003" BrowseName="1:UserManagementControllerType">
        <DisplayName>UserManagementCControllerType</DisplayName>
        <References>
            <Reference ReferenceType="HasSubtype" IsForward="false">ns=1;i=1001</Reference>
        </References>
    </UAObjectType>
</UANodeSet>
