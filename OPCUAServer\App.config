<?xml version="1.0"?>
<configuration>
  <configSections>
    <section name="UaApplicationConfiguration" type="UnifiedAutomation.UaBase.ApplicationConfigurationSection,UnifiedAutomation.UaBase"/>
  </configSections>
  <UaApplicationConfiguration>

    <SecuredApplication xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://opcfoundation.org/UA/2011/03/SecuredApplication.xsd" xmlns:ua="http://opcfoundation.org/UA/2008/02/Types.xsd">

      <!-- Identify the Application -->
      <ApplicationName>OPCUAServer</ApplicationName>
      <ApplicationUri>urn:localhost:Siemens:OPCUAServer</ApplicationUri>
      <ApplicationType>Server_0</ApplicationType>

      <!-- Specify location of Certificates and Trust Lists -->
      <ApplicationCertificate>
        <StoreType>Directory</StoreType>
        <StorePath>%CommonApplicationData%\unifiedautomation\UaSdkNetBundleBinary\pkiserver\own</StorePath>
        <SubjectName>CN=GettingStartedServer/O=UnifiedAutomation/DC=localhost</SubjectName>
        <ValidationOptions>0</ValidationOptions>
      </ApplicationCertificate>
      <TrustedCertificateStore>
        <StoreType>Directory</StoreType>
        <StorePath>%CommonApplicationData%\unifiedautomation\UaSdkNetBundleBinary\pkiserver\trusted</StorePath>
        <ValidationOptions>0</ValidationOptions>
      </TrustedCertificateStore>
      <IssuerCertificateStore>
        <StoreType>Directory</StoreType>
        <StorePath>%CommonApplicationData%\unifiedautomation\UaSdkNetBundleBinary\pkiserver\issuers</StorePath>
        <ValidationOptions>0</ValidationOptions>
      </IssuerCertificateStore>
      <RejectedCertificatesStore>
        <StoreType>Directory</StoreType>
        <StorePath>%CommonApplicationData%\unifiedautomation\UaSdkNetBundleBinary\pkiserver\rejected</StorePath>
        <ValidationOptions>0</ValidationOptions>
      </RejectedCertificatesStore>

      <!-- Specify Endpoints the Server will use -->
      <BaseAddresses>
        <BaseAddress>opc.tcp://localhost:48030</BaseAddress>
      </BaseAddresses>

      <!-- Specify the SecurityProfiles the Server supports -->
      <SecurityProfiles>
        <SecurityProfile>
          <ProfileUri>http://opcfoundation.org/UA/SecurityPolicy#Basic256Sha256</ProfileUri>
          <Enabled>true</Enabled>
        </SecurityProfile>
        <SecurityProfile>
          <ProfileUri>http://opcfoundation.org/UA/SecurityPolicy#Aes128_Sha256_RsaOaep</ProfileUri>
          <Enabled>true</Enabled>
        </SecurityProfile>
        <SecurityProfile>
          <ProfileUri>http://opcfoundation.org/UA/SecurityPolicy#Aes256_Sha256_RsaPss</ProfileUri>
          <Enabled>true</Enabled>
        </SecurityProfile>
        <SecurityProfile>
          <!-- This SecurityProfile is enabled for testing purposes. It shall NOT be enabled in end user products. -->
          <ProfileUri>http://opcfoundation.org/UA/SecurityPolicy#None</ProfileUri>
          <Enabled>true</Enabled>
        </SecurityProfile>
      </SecurityProfiles>

      <!-- Specify Configuration for Different Components (Can include 'sentron' Configuration) -->
      <Extensions>
          <!-- the EndpointUrl attribute for an Endpoint node must match one of the BaseAddresses -->
          <Extension>
              <EndpointSettings xmlns="http://unifiedautomation.com/schemas/2011/12/Application.xsd">
                  <Endpoint EndpointUrl="opc.tcp://localhost:48030">
                      <EnableSignOnly>true</EnableSignOnly>
                      <OutgoingReverseConnections>
                          <OutgoingReverseConnectionConfiguration>
                              <RemoteClientUrl>opc.tcp://localhost:48070</RemoteClientUrl>
                              <ConnectRetryInterval>10</ConnectRetryInterval>
                              <ConnectCount>4</ConnectCount>
                          </OutgoingReverseConnectionConfiguration>
                      </OutgoingReverseConnections>
                  </Endpoint>
              </EndpointSettings>
          </Extension>

          <Extension>
              <TransportSettings xmlns="http://unifiedautomation.com/schemas/2011/12/Application.xsd">
                  <SecurityTokenLifetime>60000</SecurityTokenLifetime>
                  <OperationTimeout>20000</OperationTimeout>
              </TransportSettings>
          </Extension>
        <!-- Specify the Trace Settings for the Application -->
        <Extension>
          <TraceSettings xmlns="http://unifiedautomation.com/schemas/2011/12/Application.xsd" MasterTraceEnabled="true" DefaultTraceLevel="Info">
            <TraceFile>%CommonApplicationData%\unifiedautomation\logs\UaSdkNetBundleBinary\UaGettingStartedServerNet.log.txt</TraceFile>
            <MaxEntriesPerLog>100000</MaxEntriesPerLog>
            <MaxLogFileBackups>3</MaxLogFileBackups>
            <ModuleSettings>
              <ModuleTraceSettings ModuleName="UnifiedAutomation.Stack"/>
              <ModuleTraceSettings ModuleName="UnifiedAutomation.Server"/>
            </ModuleSettings>
          </TraceSettings>
        </Extension>

        <!-- Specify Settings when EXE is run with the /install argument -->
        <Extension>
          <InstallationSettings xmlns="http://unifiedautomation.com/schemas/2011/12/Application.xsd">
            <GenerateCertificateIfNone>true</GenerateCertificateIfNone>
            <DeleteCertificateOnUninstall>true</DeleteCertificateOnUninstall>
          </InstallationSettings>
        </Extension>

        <!-- Specify Settings for the ServerManager -->
        <Extension>
          <ServerSettings xmlns="http://unifiedautomation.com/schemas/2011/12/Application.xsd">
            <ProductName>Sentron OPCUAServer</ProductName>
            <DiscoveryRegistration>
              <Enabled>true</Enabled>
            </DiscoveryRegistration>
          </ServerSettings>
        </Extension>

        <Extension>
          <SessionSettings xmlns="http://unifiedautomation.com/schemas/2011/12/Application.xsd">
            <MaxSessionCount>100</MaxSessionCount>
          </SessionSettings>
        </Extension>

        <!-- Specify Settings for the SubscriptionManager -->
        <Extension>
          <SubscriptionSettings xmlns="http://unifiedautomation.com/schemas/2011/12/Application.xsd">
            <MaxSubscriptionCount>500</MaxSubscriptionCount>
          </SubscriptionSettings>
        </Extension>

      </Extensions>
    </SecuredApplication>
  </UaApplicationConfiguration>
</configuration>
