﻿using System.Collections.Concurrent;
using PanelHttp;

namespace HTTPInterface;

internal class PAC4200RealtimeData
{
    private const string BLANK = "-99999";
    private ConcurrentDictionary<string, string> _dictPAC4200 = new();

    public void PAC4200RealtimeInit(string itemId)
    {
        this._dictPAC4200 = ReadPanelHttp.InitializeDeviceChannels(itemId);
    }

    public void PAC4200RealtimeUpdate(string itemId)
    {
        ReadPanelHttp.UpdateDeviceChannels(itemId, this._dictPAC4200);
    }

    public string GetChannelValue(string channelName)
    {
        if (string.IsNullOrEmpty(channelName))
        {
            return BLANK;
        }

        return this._dictPAC4200.TryGetValue(channelName, out var value)
            ? value ?? BLANK
            : BLANK;
    }
}
