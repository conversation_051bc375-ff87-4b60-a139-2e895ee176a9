using OPCUAServer.Common;
using OPCUAServer.Devices.PAC4200;
using Serilog;
using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaServer;

namespace OPCUAServer.Services;

/// <summary>
/// Integration helper for DynamicNodeService with the main OPC UA server.
/// This class helps integrate dynamic node addition into your existing application.
/// </summary>
public static class DynamicNodeServiceIntegration
{
    private static readonly ILogger _logger = LoggingHelper.GetLogger(nameof(DynamicNodeServiceIntegration));
    private static DynamicNodeService? _dynamicNodeService;

    /// <summary>
    /// Initializes and starts the dynamic node service after server startup.
    /// Call this method after your OPC UA server has started and NodeManagers are initialized.
    /// </summary>
    /// <param name="pac4200NodeManagers">List of PAC4200NodeManager instances</param>
    /// <param name="intervalSeconds">Interval between dynamic additions (default: 30 seconds)</param>
    public static void StartDynamicNodeService(
        IEnumerable<OPCUANodeManager> pac4200NodeManagers,
        int intervalSeconds = 120)
    {
        try
        {
            _logger.Information("Initializing Dynamic Node Service...");

            // Create the service
            _dynamicNodeService = new DynamicNodeService();

            // Register all PAC4200NodeManagers
            foreach (var nodeManager in pac4200NodeManagers)
            {
                _dynamicNodeService.RegisterNodeManager(nodeManager);
            }

            // Start the timer-based dynamic addition
            _dynamicNodeService.StartDynamicNodeAddition(intervalSeconds);

            _logger.Information($"Dynamic Node Service started with {intervalSeconds}s interval");
            _logger.Information("🚀 Dynamic properties will be added automatically - monitor your OPC UA client!");
        }
        catch (Exception ex)
        {
            _logger.Error($"Failed to start Dynamic Node Service: {ex.Message}");
        }
    }

    /// <summary>
    /// Adds a batch of test properties immediately for quick testing.
    /// </summary>
    public static void AddTestPropertiesNow()
    {
        if (_dynamicNodeService != null)
        {
            _logger.Information("Adding test properties immediately...");
            _dynamicNodeService.AddTestPropertiesBatch();
        }
        else
        {
            _logger.Warning("Dynamic Node Service not initialized. Call StartDynamicNodeService first.");
        }
    }

    /// <summary>
    /// Stops the dynamic node service.
    /// </summary>
    public static void StopDynamicNodeService()
    {
        if (_dynamicNodeService != null)
        {
            _dynamicNodeService.StopDynamicNodeAddition();
            _dynamicNodeService.Dispose();
            _dynamicNodeService = null;
            _logger.Information("Dynamic Node Service stopped");
        }
    }

    /// <summary>
    /// Gets the current status of the dynamic node service.
    /// </summary>
    public static string GetServiceStatus()
    {
        return _dynamicNodeService?.GetServiceStatus() ?? "Service not initialized";
    }

    /// <summary>
    /// Example integration method showing how to integrate with your existing server startup.
    /// Modify this to match your actual server initialization code.
    /// </summary>
    public static void ExampleIntegration()
    {
        _logger.Information("=== Dynamic Node Service Integration Example ===");
        _logger.Information("");
        _logger.Information("To integrate this into your existing application:");
        _logger.Information("");
        _logger.Information("1. After your OPC UA server starts and NodeManagers are created:");
        _logger.Information("   var pac4200Managers = GetYourPAC4200NodeManagers();");
        _logger.Information("   DynamicNodeServiceIntegration.StartDynamicNodeService(pac4200Managers, 30);");
        _logger.Information("");
        _logger.Information("2. For immediate testing:");
        _logger.Information("   DynamicNodeServiceIntegration.AddTestPropertiesNow();");
        _logger.Information("");
        _logger.Information("3. To stop the service:");
        _logger.Information("   DynamicNodeServiceIntegration.StopDynamicNodeService();");
        _logger.Information("");
        _logger.Information("4. Monitor your OPC UA client to see new properties appear!");
    }
}

/// <summary>
/// Extension methods for easier integration with existing code.
/// </summary>
public static class NodeManagerExtensions
{
    private static readonly ILogger _logger = LoggingHelper.GetLogger(nameof(NodeManagerExtensions));

    /// <summary>
    /// Extension method to easily start dynamic node service for a single NodeManager.
    /// </summary>
    public static void StartDynamicNodeAddition(this OPCUANodeManager nodeManager, int intervalSeconds = 120)
    {
        var managers = new[] { nodeManager };
        DynamicNodeServiceIntegration.StartDynamicNodeService(managers, intervalSeconds);
    }

    /// <summary>
    /// Extension method to add a single test property immediately.
    /// </summary>
    public static bool AddTestProperty(this OPCUANodeManager nodeManager, string propertyName = "TestProperty")
    {
        try
        {
            var blocks = nodeManager.GetExistingBlocks();
            if (blocks.Count == 0)
            {
                _logger.Warning("No blocks available for test property");
                return false;
            }

            string targetBlock = blocks[0];
            bool success = nodeManager.AddDynamicProperty(
                targetBlock,
                propertyName,
                DataTypeIds.String,
                $"Test value at {DateTime.Now:HH:mm:ss}",
                true);

            if (success)
            {
                _logger.Information($"✅ Added test property '{propertyName}' to block '{targetBlock}'");
            }
            else
            {
                _logger.Error($"❌ Failed to add test property '{propertyName}'");
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.Error($"Error adding test property: {ex.Message}");
            return false;
        }
    }
}

/// <summary>
/// Console helper for testing dynamic node addition from command line or debug console.
/// </summary>
public static class DynamicNodeConsoleHelper
{
    private static readonly ILogger _logger = LoggingHelper.GetLogger(nameof(DynamicNodeConsoleHelper));

    /// <summary>
    /// Provides a simple console interface for testing dynamic nodes.
    /// Call this from your main method or debug console.
    /// </summary>
    public static void StartConsoleInterface(IEnumerable<OPCUANodeManager> nodeManagers)
    {
        _logger.Information("=== Dynamic Node Console Interface ===");
        _logger.Information("Commands:");
        _logger.Information("  start [interval] - Start dynamic service (default 30s)");
        _logger.Information("  test            - Add test properties now");
        _logger.Information("  status          - Show service status");
        _logger.Information("  stop            - Stop dynamic service");
        _logger.Information("  exit            - Exit interface");
        _logger.Information("");

        var managers = nodeManagers.ToList();
        
        while (true)
        {
            Console.Write("DynamicNode> ");
            string? input = Console.ReadLine()?.Trim().ToLower();
            
            if (string.IsNullOrEmpty(input)) continue;

            var parts = input.Split(' ');
            string command = parts[0];

            switch (command)
            {
                case "start":
                    int interval = parts.Length > 1 && int.TryParse(parts[1], out int i) ? i : 30;
                    DynamicNodeServiceIntegration.StartDynamicNodeService(managers, interval);
                    break;

                case "test":
                    DynamicNodeServiceIntegration.AddTestPropertiesNow();
                    break;

                case "status":
                    Console.WriteLine(DynamicNodeServiceIntegration.GetServiceStatus());
                    break;

                case "stop":
                    DynamicNodeServiceIntegration.StopDynamicNodeService();
                    break;

                case "exit":
                    DynamicNodeServiceIntegration.StopDynamicNodeService();
                    return;

                default:
                    Console.WriteLine("Unknown command. Type 'exit' to quit.");
                    break;
            }
        }
    }
}
