﻿namespace PanelHttp;

public class ChannelInfo : IChannel
{
    public string Internal_name { get; set; } = string.Empty;

    public string Display_name { get; set; } = string.Empty;

    public string Display_value { get; set; } = string.Empty;

    public string? Unit { get; set; }

    string IChannel.Internal_name => this.Internal_name;

    string IChannel.Display_name => this.Display_name;

    string IChannel.Display_value => this.Display_value;

    string? IChannel.Unit => this.Unit;
}
