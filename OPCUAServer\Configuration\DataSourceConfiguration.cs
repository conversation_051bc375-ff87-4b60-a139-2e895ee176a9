using System.ComponentModel.DataAnnotations;

namespace Sentron.OPCUAServer.Configuration;

public class DataSourceConfiguration
{
    [Required]
    public string IP { get; set; } = "127.0.0.1";

    [Required]
    public string Port { get; set; } = "6002";

    [Range(1, 3600)]
    public int Interval { get; set; } = 2;

    [Range(1, 100)]
    public int MaxRetry { get; set; } = 10;

    [Range(50, 5000)]
    public int RetryInterval { get; set; } = 100;
}