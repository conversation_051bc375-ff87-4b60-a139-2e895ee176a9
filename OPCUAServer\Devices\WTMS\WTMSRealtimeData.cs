﻿using System.Collections.Concurrent;
using PanelHttp;

namespace HTTPInterface;

internal class WTMSRealtimeData
{
    private const string BLANK = "-99999";
    private ConcurrentDictionary<string, string> _dictWTMS = new();

    public void WTMSRealtimeInit(string itemId)
    {
        this._dictWTMS = ReadPanelHttp.InitializeDeviceChannels(itemId);
    }

    public void WTMSRealtimeUpdate(string itemId)
    {
        ReadPanelHttp.UpdateDeviceChannels(itemId, this._dictWTMS);
    }

    public string GetChannelValue(string channelName)
    {
        if (string.IsNullOrEmpty(channelName))
        {
            return BLANK;
        }

        return this._dictWTMS.TryGetValue(channelName, out var value)
            ? value ?? BLANK
            : BLANK;
    }
}
