using UnifiedAutomation.UaBase;

namespace Sentron.ThreeWA;

#region DataType Identifiers
/// <summary>
/// A class that declares constants for all DataTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypes
{
}
#endregion

#region Object Identifiers
/// <summary>
/// A class that declares constants for all Objects in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Objects
{
    /// <summary>
    /// The identifier for the http://sentron.org/ThreeWA/ Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWA_ = 5001;

}
#endregion

#region ObjectType Identifiers
/// <summary>
/// A class that declares constants for all ObjectTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypes
{
    /// <summary>
    /// The identifier for the ThreeWA ObjectType.
    /// </summary>
    public const uint ThreeWA = 1003;

}
#endregion

#region Method Identifiers
/// <summary>
/// A class that declares constants for all Methods in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Methods
{
}
#endregion

#region ReferenceType Identifiers
/// <summary>
/// A class that declares constants for all ReferenceTyped in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypes
{
}
#endregion

#region Variable Identifiers
/// <summary>
/// A class that declares constants for all Variables in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Variables
{
    /// <summary>
    /// The identifier for the AllTrips Variable.
    /// </summary>
    public const uint ThreeWA_AllTrips = 6141;

    /// <summary>
    /// The identifier for the APhaseTemp1 Variable.
    /// </summary>
    public const uint ThreeWA_APhaseTemp1 = 6142;

    /// <summary>
    /// The identifier for the APhaseTemp2 Variable.
    /// </summary>
    public const uint ThreeWA_APhaseTemp2 = 6143;

    /// <summary>
    /// The identifier for the BPhaseTemp1 Variable.
    /// </summary>
    public const uint ThreeWA_BPhaseTemp1 = 6144;

    /// <summary>
    /// The identifier for the BPhaseTemp2 Variable.
    /// </summary>
    public const uint ThreeWA_BPhaseTemp2 = 6145;

    /// <summary>
    /// The identifier for the BreakerTemp Variable.
    /// </summary>
    public const uint ThreeWA_BreakerTemp = 6107;

    /// <summary>
    /// The identifier for the ComTemp Variable.
    /// </summary>
    public const uint ThreeWA_ComTemp = 6108;

    /// <summary>
    /// The identifier for the CPhaseTemp1 Variable.
    /// </summary>
    public const uint ThreeWA_CPhaseTemp1 = 6146;

    /// <summary>
    /// The identifier for the CPhaseTemp2 Variable.
    /// </summary>
    public const uint ThreeWA_CPhaseTemp2 = 6147;

    /// <summary>
    /// The identifier for the ElectricalSwitchCycles Variable.
    /// </summary>
    public const uint ThreeWA_ElectricalSwitchCycles = 6105;

    /// <summary>
    /// The identifier for the F Variable.
    /// </summary>
    public const uint ThreeWA_F = 6125;

    /// <summary>
    /// The identifier for the ForwardActivePower Variable.
    /// </summary>
    public const uint ThreeWA_ForwardActivePower = 6132;

    /// <summary>
    /// The identifier for the ForwardReactivePower Variable.
    /// </summary>
    public const uint ThreeWA_ForwardReactivePower = 6133;

    /// <summary>
    /// The identifier for the GF_ALARM_ONOFF Variable.
    /// </summary>
    public const uint ThreeWA_GF_ALARM_ONOFF = 6181;

    /// <summary>
    /// The identifier for the GF_IG_DIRECT_ALARM Variable.
    /// </summary>
    public const uint ThreeWA_GF_IG_DIRECT_ALARM = 6182;

    /// <summary>
    /// The identifier for the GF_INTERMITTENT_ONOFF Variable.
    /// </summary>
    public const uint ThreeWA_GF_INTERMITTENT_ONOFF = 6177;

    /// <summary>
    /// The identifier for the GF_ONOFF Variable.
    /// </summary>
    public const uint ThreeWA_GF_ONOFF = 6176;

    /// <summary>
    /// The identifier for the GF_STD_IG_DIRECT Variable.
    /// </summary>
    public const uint ThreeWA_GF_STD_IG_DIRECT = 6179;

    /// <summary>
    /// The identifier for the GF_STD_PARA_CURVE Variable.
    /// </summary>
    public const uint ThreeWA_GF_STD_PARA_CURVE = 6178;

    /// <summary>
    /// The identifier for the GF_STD_TG_DIRECT Variable.
    /// </summary>
    public const uint ThreeWA_GF_STD_TG_DIRECT = 6180;

    /// <summary>
    /// The identifier for the GF_TG_ALARM Variable.
    /// </summary>
    public const uint ThreeWA_GF_TG_ALARM = 6183;

    /// <summary>
    /// The identifier for the HaveAlarm Variable.
    /// </summary>
    public const uint ThreeWA_HaveAlarm = 6100;

    /// <summary>
    /// The identifier for the HealthScore Variable.
    /// </summary>
    public const uint ThreeWA_HealthScore = 6150;

    /// <summary>
    /// The identifier for the I_Avg Variable.
    /// </summary>
    public const uint ThreeWA_I_Avg = 6136;

    /// <summary>
    /// The identifier for the Ia Variable.
    /// </summary>
    public const uint ThreeWA_Ia = 6115;

    /// <summary>
    /// The identifier for the Ib Variable.
    /// </summary>
    public const uint ThreeWA_Ib = 6116;

    /// <summary>
    /// The identifier for the Ic Variable.
    /// </summary>
    public const uint ThreeWA_Ic = 6117;

    /// <summary>
    /// The identifier for the INST_II Variable.
    /// </summary>
    public const uint ThreeWA_INST_II = 6175;

    /// <summary>
    /// The identifier for the INST_ONOFF Variable.
    /// </summary>
    public const uint ThreeWA_INST_ONOFF = 6174;

    /// <summary>
    /// The identifier for the IsConnected Variable.
    /// </summary>
    public const uint ThreeWA_IsConnected = 6101;

    /// <summary>
    /// The identifier for the LT_IR Variable.
    /// </summary>
    public const uint ThreeWA_LT_IR = 6154;

    /// <summary>
    /// The identifier for the LT_IR_REMOTE Variable.
    /// </summary>
    public const uint ThreeWA_LT_IR_REMOTE = 6158;

    /// <summary>
    /// The identifier for the LT_ONOFF Variable.
    /// </summary>
    public const uint ThreeWA_LT_ONOFF = 6153;

    /// <summary>
    /// The identifier for the LT_ONOFF_REMOTE Variable.
    /// </summary>
    public const uint ThreeWA_LT_ONOFF_REMOTE = 6157;

    /// <summary>
    /// The identifier for the LT_PARA_CURVE Variable.
    /// </summary>
    public const uint ThreeWA_LT_PARA_CURVE = 6156;

    /// <summary>
    /// The identifier for the LT_PARA_CURVE_REMOTE Variable.
    /// </summary>
    public const uint ThreeWA_LT_PARA_CURVE_REMOTE = 6160;

    /// <summary>
    /// The identifier for the LT_TAU Variable.
    /// </summary>
    public const uint ThreeWA_LT_TAU = 6161;

    /// <summary>
    /// The identifier for the LT_TR Variable.
    /// </summary>
    public const uint ThreeWA_LT_TR = 6155;

    /// <summary>
    /// The identifier for the LT_TR_REMOTE Variable.
    /// </summary>
    public const uint ThreeWA_LT_TR_REMOTE = 6159;

    /// <summary>
    /// The identifier for the LTN_IN Variable.
    /// </summary>
    public const uint ThreeWA_LTN_IN = 6163;

    /// <summary>
    /// The identifier for the LTN_ONOFF Variable.
    /// </summary>
    public const uint ThreeWA_LTN_ONOFF = 6162;

    /// <summary>
    /// The identifier for the LTTrips Variable.
    /// </summary>
    public const uint ThreeWA_LTTrips = 6139;

    /// <summary>
    /// The identifier for the MainContantHealth Variable.
    /// </summary>
    public const uint ThreeWA_MainContantHealth = 6152;

    /// <summary>
    /// The identifier for the MainContantStatus Variable.
    /// </summary>
    public const uint ThreeWA_MainContantStatus = 6138;

    /// <summary>
    /// The identifier for the MechanicalSwitchCycles Variable.
    /// </summary>
    public const uint ThreeWA_MechanicalSwitchCycles = 6106;

    /// <summary>
    /// The identifier for the NPhaseTemp1 Variable.
    /// </summary>
    public const uint ThreeWA_NPhaseTemp1 = 6148;

    /// <summary>
    /// The identifier for the NPhaseTemp2 Variable.
    /// </summary>
    public const uint ThreeWA_NPhaseTemp2 = 6149;

    /// <summary>
    /// The identifier for the OperatingHours Variable.
    /// </summary>
    public const uint ThreeWA_OperatingHours = 6137;

    /// <summary>
    /// The identifier for the P Variable.
    /// </summary>
    public const uint ThreeWA_P = 6118;

    /// <summary>
    /// The identifier for the PAL_IN Variable.
    /// </summary>
    public const uint ThreeWA_PAL_IN = 6164;

    /// <summary>
    /// The identifier for the PowFactor Variable.
    /// </summary>
    public const uint ThreeWA_PowFactor = 6121;

    /// <summary>
    /// The identifier for the PowFactor_A Variable.
    /// </summary>
    public const uint ThreeWA_PowFactor_A = 6122;

    /// <summary>
    /// The identifier for the PowFactor_B Variable.
    /// </summary>
    public const uint ThreeWA_PowFactor_B = 6123;

    /// <summary>
    /// The identifier for the PowFactor_C Variable.
    /// </summary>
    public const uint ThreeWA_PowFactor_C = 6124;

    /// <summary>
    /// The identifier for the Q Variable.
    /// </summary>
    public const uint ThreeWA_Q = 6119;

    /// <summary>
    /// The identifier for the RemainingLife Variable.
    /// </summary>
    public const uint ThreeWA_RemainingLife = 6151;

    /// <summary>
    /// The identifier for the ReverseActivePower Variable.
    /// </summary>
    public const uint ThreeWA_ReverseActivePower = 6134;

    /// <summary>
    /// The identifier for the ReverseReactivePower Variable.
    /// </summary>
    public const uint ThreeWA_ReverseReactivePower = 6135;

    /// <summary>
    /// The identifier for the S Variable.
    /// </summary>
    public const uint ThreeWA_S = 6120;

    /// <summary>
    /// The identifier for the SpringCharged Variable.
    /// </summary>
    public const uint ThreeWA_SpringCharged = 6103;

    /// <summary>
    /// The identifier for the ST_I2t_ONOFF Variable.
    /// </summary>
    public const uint ThreeWA_ST_I2t_ONOFF = 6168;

    /// <summary>
    /// The identifier for the ST_ISD Variable.
    /// </summary>
    public const uint ThreeWA_ST_ISD = 6166;

    /// <summary>
    /// The identifier for the ST_ISD_REF_I2TSD Variable.
    /// </summary>
    public const uint ThreeWA_ST_ISD_REF_I2TSD = 6169;

    /// <summary>
    /// The identifier for the ST_ISD_REMOTE Variable.
    /// </summary>
    public const uint ThreeWA_ST_ISD_REMOTE = 6171;

    /// <summary>
    /// The identifier for the ST_ONOFF Variable.
    /// </summary>
    public const uint ThreeWA_ST_ONOFF = 6165;

    /// <summary>
    /// The identifier for the ST_ONOFF_REMOTE Variable.
    /// </summary>
    public const uint ThreeWA_ST_ONOFF_REMOTE = 6170;

    /// <summary>
    /// The identifier for the ST_TSD Variable.
    /// </summary>
    public const uint ThreeWA_ST_TSD = 6167;

    /// <summary>
    /// The identifier for the ST_TSD_REMOTE Variable.
    /// </summary>
    public const uint ThreeWA_ST_TSD_REMOTE = 6172;

    /// <summary>
    /// The identifier for the STTrips Variable.
    /// </summary>
    public const uint ThreeWA_STTrips = 6140;

    /// <summary>
    /// The identifier for the Switch Variable.
    /// </summary>
    public const uint ThreeWA_Switch = 6102;

    /// <summary>
    /// The identifier for the SwitchReady Variable.
    /// </summary>
    public const uint ThreeWA_SwitchReady = 6104;

    /// <summary>
    /// The identifier for the THD_Ia Variable.
    /// </summary>
    public const uint ThreeWA_THD_Ia = 6129;

    /// <summary>
    /// The identifier for the THD_Ib Variable.
    /// </summary>
    public const uint ThreeWA_THD_Ib = 6130;

    /// <summary>
    /// The identifier for the THD_Ic Variable.
    /// </summary>
    public const uint ThreeWA_THD_Ic = 6131;

    /// <summary>
    /// The identifier for the THD_Ua Variable.
    /// </summary>
    public const uint ThreeWA_THD_Ua = 6126;

    /// <summary>
    /// The identifier for the THD_Ub Variable.
    /// </summary>
    public const uint ThreeWA_THD_Ub = 6127;

    /// <summary>
    /// The identifier for the THD_Uc Variable.
    /// </summary>
    public const uint ThreeWA_THD_Uc = 6128;

    /// <summary>
    /// The identifier for the Ua Variable.
    /// </summary>
    public const uint ThreeWA_Ua = 6109;

    /// <summary>
    /// The identifier for the Uab Variable.
    /// </summary>
    public const uint ThreeWA_Uab = 6112;

    /// <summary>
    /// The identifier for the Ub Variable.
    /// </summary>
    public const uint ThreeWA_Ub = 6110;

    /// <summary>
    /// The identifier for the Ubc Variable.
    /// </summary>
    public const uint ThreeWA_Ubc = 6113;

    /// <summary>
    /// The identifier for the Uc Variable.
    /// </summary>
    public const uint ThreeWA_Uc = 6111;

    /// <summary>
    /// The identifier for the Uca Variable.
    /// </summary>
    public const uint ThreeWA_Uca = 6114;

    /// <summary>
    /// The identifier for the ZSI_IN_ONOFF_dST Variable.
    /// </summary>
    public const uint ThreeWA_ZSI_IN_ONOFF_dST = 6173;

    /// <summary>
    /// The identifier for the IsNamespaceSubset Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWA__IsNamespaceSubset = 6008;

    /// <summary>
    /// The identifier for the NamespacePublicationDate Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWA__NamespacePublicationDate = 6009;

    /// <summary>
    /// The identifier for the NamespaceUri Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWA__NamespaceUri = 6010;

    /// <summary>
    /// The identifier for the NamespaceVersion Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWA__NamespaceVersion = 6011;

    /// <summary>
    /// The identifier for the StaticNodeIdTypes Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWA__StaticNodeIdTypes = 6012;

    /// <summary>
    /// The identifier for the StaticNumericNodeIdRange Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWA__StaticNumericNodeIdRange = 6013;

    /// <summary>
    /// The identifier for the StaticStringNodeIdPattern Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWA__StaticStringNodeIdPattern = 6014;

}
#endregion

#region VariableTypes Identifiers
/// <summary>
/// A class that declares constants for all VariableTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypes
{
}
#endregion

#region DataType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all DataTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypeIds
{
}
#endregion

#region Method Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Methods in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class MethodIds
{
}
#endregion

#region Object Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectIds
{
    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWA_ Object.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWA_ = new ExpandedNodeId(Objects.Namespaces_http___sentron_org_ThreeWA_, Namespaces.ThreeWA);

}
#endregion

#region ObjectType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypeIds
{
    /// <summary>
    /// The identifier for the ThreeWA ObjectType.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA = new ExpandedNodeId(ObjectTypes.ThreeWA, Namespaces.ThreeWA);

}
#endregion

#region ReferenceType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all ReferenceTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypeIds
{
}
#endregion

#region Variable Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Variables in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableIds
{
    /// <summary>
    /// The identifier for the ThreeWA_AllTrips Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_AllTrips = new ExpandedNodeId(Variables.ThreeWA_AllTrips, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_APhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_APhaseTemp1 = new ExpandedNodeId(Variables.ThreeWA_APhaseTemp1, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_APhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_APhaseTemp2 = new ExpandedNodeId(Variables.ThreeWA_APhaseTemp2, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_BPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_BPhaseTemp1 = new ExpandedNodeId(Variables.ThreeWA_BPhaseTemp1, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_BPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_BPhaseTemp2 = new ExpandedNodeId(Variables.ThreeWA_BPhaseTemp2, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_BreakerTemp Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_BreakerTemp = new ExpandedNodeId(Variables.ThreeWA_BreakerTemp, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ComTemp Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ComTemp = new ExpandedNodeId(Variables.ThreeWA_ComTemp, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_CPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_CPhaseTemp1 = new ExpandedNodeId(Variables.ThreeWA_CPhaseTemp1, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_CPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_CPhaseTemp2 = new ExpandedNodeId(Variables.ThreeWA_CPhaseTemp2, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ElectricalSwitchCycles Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ElectricalSwitchCycles = new ExpandedNodeId(Variables.ThreeWA_ElectricalSwitchCycles, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_F Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_F = new ExpandedNodeId(Variables.ThreeWA_F, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ForwardActivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ForwardActivePower = new ExpandedNodeId(Variables.ThreeWA_ForwardActivePower, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ForwardReactivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ForwardReactivePower = new ExpandedNodeId(Variables.ThreeWA_ForwardReactivePower, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_GF_ALARM_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_GF_ALARM_ONOFF = new ExpandedNodeId(Variables.ThreeWA_GF_ALARM_ONOFF, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_GF_IG_DIRECT_ALARM Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_GF_IG_DIRECT_ALARM = new ExpandedNodeId(Variables.ThreeWA_GF_IG_DIRECT_ALARM, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_GF_INTERMITTENT_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_GF_INTERMITTENT_ONOFF = new ExpandedNodeId(Variables.ThreeWA_GF_INTERMITTENT_ONOFF, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_GF_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_GF_ONOFF = new ExpandedNodeId(Variables.ThreeWA_GF_ONOFF, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_GF_STD_IG_DIRECT Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_GF_STD_IG_DIRECT = new ExpandedNodeId(Variables.ThreeWA_GF_STD_IG_DIRECT, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_GF_STD_PARA_CURVE Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_GF_STD_PARA_CURVE = new ExpandedNodeId(Variables.ThreeWA_GF_STD_PARA_CURVE, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_GF_STD_TG_DIRECT Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_GF_STD_TG_DIRECT = new ExpandedNodeId(Variables.ThreeWA_GF_STD_TG_DIRECT, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_GF_TG_ALARM Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_GF_TG_ALARM = new ExpandedNodeId(Variables.ThreeWA_GF_TG_ALARM, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_HaveAlarm Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_HaveAlarm = new ExpandedNodeId(Variables.ThreeWA_HaveAlarm, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_HealthScore Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_HealthScore = new ExpandedNodeId(Variables.ThreeWA_HealthScore, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_I_Avg Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_I_Avg = new ExpandedNodeId(Variables.ThreeWA_I_Avg, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_Ia Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_Ia = new ExpandedNodeId(Variables.ThreeWA_Ia, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_Ib Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_Ib = new ExpandedNodeId(Variables.ThreeWA_Ib, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_Ic Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_Ic = new ExpandedNodeId(Variables.ThreeWA_Ic, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_INST_II Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_INST_II = new ExpandedNodeId(Variables.ThreeWA_INST_II, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_INST_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_INST_ONOFF = new ExpandedNodeId(Variables.ThreeWA_INST_ONOFF, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_IsConnected Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_IsConnected = new ExpandedNodeId(Variables.ThreeWA_IsConnected, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_LT_IR Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_LT_IR = new ExpandedNodeId(Variables.ThreeWA_LT_IR, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_LT_IR_REMOTE Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_LT_IR_REMOTE = new ExpandedNodeId(Variables.ThreeWA_LT_IR_REMOTE, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_LT_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_LT_ONOFF = new ExpandedNodeId(Variables.ThreeWA_LT_ONOFF, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_LT_ONOFF_REMOTE Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_LT_ONOFF_REMOTE = new ExpandedNodeId(Variables.ThreeWA_LT_ONOFF_REMOTE, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_LT_PARA_CURVE Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_LT_PARA_CURVE = new ExpandedNodeId(Variables.ThreeWA_LT_PARA_CURVE, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_LT_PARA_CURVE_REMOTE Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_LT_PARA_CURVE_REMOTE = new ExpandedNodeId(Variables.ThreeWA_LT_PARA_CURVE_REMOTE, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_LT_TAU Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_LT_TAU = new ExpandedNodeId(Variables.ThreeWA_LT_TAU, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_LT_TR Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_LT_TR = new ExpandedNodeId(Variables.ThreeWA_LT_TR, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_LT_TR_REMOTE Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_LT_TR_REMOTE = new ExpandedNodeId(Variables.ThreeWA_LT_TR_REMOTE, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_LTN_IN Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_LTN_IN = new ExpandedNodeId(Variables.ThreeWA_LTN_IN, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_LTN_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_LTN_ONOFF = new ExpandedNodeId(Variables.ThreeWA_LTN_ONOFF, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_LTTrips Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_LTTrips = new ExpandedNodeId(Variables.ThreeWA_LTTrips, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_MainContantHealth Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_MainContantHealth = new ExpandedNodeId(Variables.ThreeWA_MainContantHealth, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_MainContantStatus Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_MainContantStatus = new ExpandedNodeId(Variables.ThreeWA_MainContantStatus, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_MechanicalSwitchCycles Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_MechanicalSwitchCycles = new ExpandedNodeId(Variables.ThreeWA_MechanicalSwitchCycles, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_NPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_NPhaseTemp1 = new ExpandedNodeId(Variables.ThreeWA_NPhaseTemp1, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_NPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_NPhaseTemp2 = new ExpandedNodeId(Variables.ThreeWA_NPhaseTemp2, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_OperatingHours Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_OperatingHours = new ExpandedNodeId(Variables.ThreeWA_OperatingHours, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_P Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_P = new ExpandedNodeId(Variables.ThreeWA_P, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_PAL_IN Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_PAL_IN = new ExpandedNodeId(Variables.ThreeWA_PAL_IN, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_PowFactor Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_PowFactor = new ExpandedNodeId(Variables.ThreeWA_PowFactor, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_PowFactor_A Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_PowFactor_A = new ExpandedNodeId(Variables.ThreeWA_PowFactor_A, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_PowFactor_B Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_PowFactor_B = new ExpandedNodeId(Variables.ThreeWA_PowFactor_B, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_PowFactor_C Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_PowFactor_C = new ExpandedNodeId(Variables.ThreeWA_PowFactor_C, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_Q Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_Q = new ExpandedNodeId(Variables.ThreeWA_Q, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_RemainingLife Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_RemainingLife = new ExpandedNodeId(Variables.ThreeWA_RemainingLife, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ReverseActivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ReverseActivePower = new ExpandedNodeId(Variables.ThreeWA_ReverseActivePower, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ReverseReactivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ReverseReactivePower = new ExpandedNodeId(Variables.ThreeWA_ReverseReactivePower, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_S Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_S = new ExpandedNodeId(Variables.ThreeWA_S, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_SpringCharged Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_SpringCharged = new ExpandedNodeId(Variables.ThreeWA_SpringCharged, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ST_I2t_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ST_I2t_ONOFF = new ExpandedNodeId(Variables.ThreeWA_ST_I2t_ONOFF, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ST_ISD Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ST_ISD = new ExpandedNodeId(Variables.ThreeWA_ST_ISD, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ST_ISD_REF_I2TSD Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ST_ISD_REF_I2TSD = new ExpandedNodeId(Variables.ThreeWA_ST_ISD_REF_I2TSD, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ST_ISD_REMOTE Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ST_ISD_REMOTE = new ExpandedNodeId(Variables.ThreeWA_ST_ISD_REMOTE, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ST_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ST_ONOFF = new ExpandedNodeId(Variables.ThreeWA_ST_ONOFF, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ST_ONOFF_REMOTE Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ST_ONOFF_REMOTE = new ExpandedNodeId(Variables.ThreeWA_ST_ONOFF_REMOTE, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ST_TSD Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ST_TSD = new ExpandedNodeId(Variables.ThreeWA_ST_TSD, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ST_TSD_REMOTE Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ST_TSD_REMOTE = new ExpandedNodeId(Variables.ThreeWA_ST_TSD_REMOTE, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_STTrips Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_STTrips = new ExpandedNodeId(Variables.ThreeWA_STTrips, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_Switch Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_Switch = new ExpandedNodeId(Variables.ThreeWA_Switch, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_SwitchReady Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_SwitchReady = new ExpandedNodeId(Variables.ThreeWA_SwitchReady, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_THD_Ia Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_THD_Ia = new ExpandedNodeId(Variables.ThreeWA_THD_Ia, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_THD_Ib Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_THD_Ib = new ExpandedNodeId(Variables.ThreeWA_THD_Ib, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_THD_Ic Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_THD_Ic = new ExpandedNodeId(Variables.ThreeWA_THD_Ic, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_THD_Ua Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_THD_Ua = new ExpandedNodeId(Variables.ThreeWA_THD_Ua, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_THD_Ub Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_THD_Ub = new ExpandedNodeId(Variables.ThreeWA_THD_Ub, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_THD_Uc Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_THD_Uc = new ExpandedNodeId(Variables.ThreeWA_THD_Uc, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_Ua Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_Ua = new ExpandedNodeId(Variables.ThreeWA_Ua, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_Uab Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_Uab = new ExpandedNodeId(Variables.ThreeWA_Uab, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_Ub Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_Ub = new ExpandedNodeId(Variables.ThreeWA_Ub, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_Ubc Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_Ubc = new ExpandedNodeId(Variables.ThreeWA_Ubc, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_Uc Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_Uc = new ExpandedNodeId(Variables.ThreeWA_Uc, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_Uca Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_Uca = new ExpandedNodeId(Variables.ThreeWA_Uca, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the ThreeWA_ZSI_IN_ONOFF_dST Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWA_ZSI_IN_ONOFF_dST = new ExpandedNodeId(Variables.ThreeWA_ZSI_IN_ONOFF_dST, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWA__IsNamespaceSubset Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWA__IsNamespaceSubset = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWA__IsNamespaceSubset, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWA__NamespacePublicationDate Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWA__NamespacePublicationDate = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWA__NamespacePublicationDate, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWA__NamespaceUri Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWA__NamespaceUri = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWA__NamespaceUri, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWA__NamespaceVersion Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWA__NamespaceVersion = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWA__NamespaceVersion, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWA__StaticNodeIdTypes Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWA__StaticNodeIdTypes = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWA__StaticNodeIdTypes, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWA__StaticNumericNodeIdRange Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWA__StaticNumericNodeIdRange = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWA__StaticNumericNodeIdRange, Namespaces.ThreeWA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWA__StaticStringNodeIdPattern Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWA__StaticStringNodeIdPattern = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWA__StaticStringNodeIdPattern, Namespaces.ThreeWA);

}
#endregion

#region VariableType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all VariableType in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypeIds
{
}
#endregion

#region BrowseName Declarations
/// <summary>
/// Declares all of the BrowseNames used in the Model.
/// </summary>
public static partial class BrowseNames
{
    /// <summary>
    /// The BrowseName for the APhaseTemp1 component.
    /// </summary>
    public const string APhaseTemp1 = "APhaseTemp1";
    /// <summary>
    /// The BrowseName for the APhaseTemp2 component.
    /// </summary>
    public const string APhaseTemp2 = "APhaseTemp2";
    /// <summary>
    /// The BrowseName for the AllTrips component.
    /// </summary>
    public const string AllTrips = "AllTrips";
    /// <summary>
    /// The BrowseName for the BPhaseTemp1 component.
    /// </summary>
    public const string BPhaseTemp1 = "BPhaseTemp1";
    /// <summary>
    /// The BrowseName for the BPhaseTemp2 component.
    /// </summary>
    public const string BPhaseTemp2 = "BPhaseTemp2";
    /// <summary>
    /// The BrowseName for the BreakerTemp component.
    /// </summary>
    public const string BreakerTemp = "BreakerTemp";
    /// <summary>
    /// The BrowseName for the CPhaseTemp1 component.
    /// </summary>
    public const string CPhaseTemp1 = "CPhaseTemp1";
    /// <summary>
    /// The BrowseName for the CPhaseTemp2 component.
    /// </summary>
    public const string CPhaseTemp2 = "CPhaseTemp2";
    /// <summary>
    /// The BrowseName for the ComTemp component.
    /// </summary>
    public const string ComTemp = "ComTemp";
    /// <summary>
    /// The BrowseName for the ElectricalSwitchCycles component.
    /// </summary>
    public const string ElectricalSwitchCycles = "ElectricalSwitchCycles";
    /// <summary>
    /// The BrowseName for the F component.
    /// </summary>
    public const string F = "F";
    /// <summary>
    /// The BrowseName for the ForwardActivePower component.
    /// </summary>
    public const string ForwardActivePower = "ForwardActivePower";
    /// <summary>
    /// The BrowseName for the ForwardReactivePower component.
    /// </summary>
    public const string ForwardReactivePower = "ForwardReactivePower";
    /// <summary>
    /// The BrowseName for the GF_ALARM_ONOFF component.
    /// </summary>
    public const string GFALARMONOFF = "GF_ALARM_ONOFF";
    /// <summary>
    /// The BrowseName for the GF_IG_DIRECT_ALARM component.
    /// </summary>
    public const string GFIGDIRECTALARM = "GF_IG_DIRECT_ALARM";
    /// <summary>
    /// The BrowseName for the GF_INTERMITTENT_ONOFF component.
    /// </summary>
    public const string GFINTERMITTENTONOFF = "GF_INTERMITTENT_ONOFF";
    /// <summary>
    /// The BrowseName for the GF_ONOFF component.
    /// </summary>
    public const string GFONOFF = "GF_ONOFF";
    /// <summary>
    /// The BrowseName for the GF_STD_IG_DIRECT component.
    /// </summary>
    public const string GFSTDIGDIRECT = "GF_STD_IG_DIRECT";
    /// <summary>
    /// The BrowseName for the GF_STD_PARA_CURVE component.
    /// </summary>
    public const string GFSTDPARACURVE = "GF_STD_PARA_CURVE";
    /// <summary>
    /// The BrowseName for the GF_STD_TG_DIRECT component.
    /// </summary>
    public const string GFSTDTGDIRECT = "GF_STD_TG_DIRECT";
    /// <summary>
    /// The BrowseName for the GF_TG_ALARM component.
    /// </summary>
    public const string GFTGALARM = "GF_TG_ALARM";
    /// <summary>
    /// The BrowseName for the HaveAlarm component.
    /// </summary>
    public const string HaveAlarm = "HaveAlarm";
    /// <summary>
    /// The BrowseName for the HealthScore component.
    /// </summary>
    public const string HealthScore = "HealthScore";
    /// <summary>
    /// The BrowseName for the INST_II component.
    /// </summary>
    public const string INSTII = "INST_II";
    /// <summary>
    /// The BrowseName for the INST_ONOFF component.
    /// </summary>
    public const string INSTONOFF = "INST_ONOFF";
    /// <summary>
    /// The BrowseName for the I_Avg component.
    /// </summary>
    public const string IAvg = "I_Avg";
    /// <summary>
    /// The BrowseName for the Ia component.
    /// </summary>
    public const string Ia = "Ia";
    /// <summary>
    /// The BrowseName for the Ib component.
    /// </summary>
    public const string Ib = "Ib";
    /// <summary>
    /// The BrowseName for the Ic component.
    /// </summary>
    public const string Ic = "Ic";
    /// <summary>
    /// The BrowseName for the IsConnected component.
    /// </summary>
    public const string IsConnected = "IsConnected";
    /// <summary>
    /// The BrowseName for the LTN_IN component.
    /// </summary>
    public const string LTNIN = "LTN_IN";
    /// <summary>
    /// The BrowseName for the LTN_ONOFF component.
    /// </summary>
    public const string LTNONOFF = "LTN_ONOFF";
    /// <summary>
    /// The BrowseName for the LTTrips component.
    /// </summary>
    public const string LTTrips = "LTTrips";
    /// <summary>
    /// The BrowseName for the LT_IR component.
    /// </summary>
    public const string LTIR = "LT_IR";
    /// <summary>
    /// The BrowseName for the LT_IR_REMOTE component.
    /// </summary>
    public const string LTIRREMOTE = "LT_IR_REMOTE";
    /// <summary>
    /// The BrowseName for the LT_ONOFF component.
    /// </summary>
    public const string LTONOFF = "LT_ONOFF";
    /// <summary>
    /// The BrowseName for the LT_ONOFF_REMOTE component.
    /// </summary>
    public const string LTONOFFREMOTE = "LT_ONOFF_REMOTE";
    /// <summary>
    /// The BrowseName for the LT_PARA_CURVE component.
    /// </summary>
    public const string LTPARACURVE = "LT_PARA_CURVE";
    /// <summary>
    /// The BrowseName for the LT_PARA_CURVE_REMOTE component.
    /// </summary>
    public const string LTPARACURVEREMOTE = "LT_PARA_CURVE_REMOTE";
    /// <summary>
    /// The BrowseName for the LT_TAU component.
    /// </summary>
    public const string LTTAU = "LT_TAU";
    /// <summary>
    /// The BrowseName for the LT_TR component.
    /// </summary>
    public const string LTTR = "LT_TR";
    /// <summary>
    /// The BrowseName for the LT_TR_REMOTE component.
    /// </summary>
    public const string LTTRREMOTE = "LT_TR_REMOTE";
    /// <summary>
    /// The BrowseName for the MainContantHealth component.
    /// </summary>
    public const string MainContantHealth = "MainContantHealth";
    /// <summary>
    /// The BrowseName for the MainContantStatus component.
    /// </summary>
    public const string MainContantStatus = "MainContantStatus";
    /// <summary>
    /// The BrowseName for the MechanicalSwitchCycles component.
    /// </summary>
    public const string MechanicalSwitchCycles = "MechanicalSwitchCycles";
    /// <summary>
    /// The BrowseName for the NPhaseTemp1 component.
    /// </summary>
    public const string NPhaseTemp1 = "NPhaseTemp1";
    /// <summary>
    /// The BrowseName for the NPhaseTemp2 component.
    /// </summary>
    public const string NPhaseTemp2 = "NPhaseTemp2";
    /// <summary>
    /// The BrowseName for the OperatingHours component.
    /// </summary>
    public const string OperatingHours = "OperatingHours";
    /// <summary>
    /// The BrowseName for the P component.
    /// </summary>
    public const string P = "P";
    /// <summary>
    /// The BrowseName for the PAL_IN component.
    /// </summary>
    public const string PALIN = "PAL_IN";
    /// <summary>
    /// The BrowseName for the PowFactor component.
    /// </summary>
    public const string PowFactor = "PowFactor";
    /// <summary>
    /// The BrowseName for the PowFactor_A component.
    /// </summary>
    public const string PowFactorA = "PowFactor_A";
    /// <summary>
    /// The BrowseName for the PowFactor_B component.
    /// </summary>
    public const string PowFactorB = "PowFactor_B";
    /// <summary>
    /// The BrowseName for the PowFactor_C component.
    /// </summary>
    public const string PowFactorC = "PowFactor_C";
    /// <summary>
    /// The BrowseName for the Q component.
    /// </summary>
    public const string Q = "Q";
    /// <summary>
    /// The BrowseName for the RemainingLife component.
    /// </summary>
    public const string RemainingLife = "RemainingLife";
    /// <summary>
    /// The BrowseName for the ReverseActivePower component.
    /// </summary>
    public const string ReverseActivePower = "ReverseActivePower";
    /// <summary>
    /// The BrowseName for the ReverseReactivePower component.
    /// </summary>
    public const string ReverseReactivePower = "ReverseReactivePower";
    /// <summary>
    /// The BrowseName for the S component.
    /// </summary>
    public const string S = "S";
    /// <summary>
    /// The BrowseName for the STTrips component.
    /// </summary>
    public const string STTrips = "STTrips";
    /// <summary>
    /// The BrowseName for the ST_I2t_ONOFF component.
    /// </summary>
    public const string STI2tONOFF = "ST_I2t_ONOFF";
    /// <summary>
    /// The BrowseName for the ST_ISD component.
    /// </summary>
    public const string STISD = "ST_ISD";
    /// <summary>
    /// The BrowseName for the ST_ISD_REF_I2TSD component.
    /// </summary>
    public const string STISDREFI2TSD = "ST_ISD_REF_I2TSD";
    /// <summary>
    /// The BrowseName for the ST_ISD_REMOTE component.
    /// </summary>
    public const string STISDREMOTE = "ST_ISD_REMOTE";
    /// <summary>
    /// The BrowseName for the ST_ONOFF component.
    /// </summary>
    public const string STONOFF = "ST_ONOFF";
    /// <summary>
    /// The BrowseName for the ST_ONOFF_REMOTE component.
    /// </summary>
    public const string STONOFFREMOTE = "ST_ONOFF_REMOTE";
    /// <summary>
    /// The BrowseName for the ST_TSD component.
    /// </summary>
    public const string STTSD = "ST_TSD";
    /// <summary>
    /// The BrowseName for the ST_TSD_REMOTE component.
    /// </summary>
    public const string STTSDREMOTE = "ST_TSD_REMOTE";
    /// <summary>
    /// The BrowseName for the SpringCharged component.
    /// </summary>
    public const string SpringCharged = "SpringCharged";
    /// <summary>
    /// The BrowseName for the Switch component.
    /// </summary>
    public const string Switch = "Switch";
    /// <summary>
    /// The BrowseName for the SwitchReady component.
    /// </summary>
    public const string SwitchReady = "SwitchReady";
    /// <summary>
    /// The BrowseName for the THD_Ia component.
    /// </summary>
    public const string THDIa = "THD_Ia";
    /// <summary>
    /// The BrowseName for the THD_Ib component.
    /// </summary>
    public const string THDIb = "THD_Ib";
    /// <summary>
    /// The BrowseName for the THD_Ic component.
    /// </summary>
    public const string THDIc = "THD_Ic";
    /// <summary>
    /// The BrowseName for the THD_Ua component.
    /// </summary>
    public const string THDUa = "THD_Ua";
    /// <summary>
    /// The BrowseName for the THD_Ub component.
    /// </summary>
    public const string THDUb = "THD_Ub";
    /// <summary>
    /// The BrowseName for the THD_Uc component.
    /// </summary>
    public const string THDUc = "THD_Uc";
    /// <summary>
    /// The BrowseName for the ThreeWA component.
    /// </summary>
    public const string ThreeWA = "ThreeWA";
    /// <summary>
    /// The BrowseName for the Ua component.
    /// </summary>
    public const string Ua = "Ua";
    /// <summary>
    /// The BrowseName for the Uab component.
    /// </summary>
    public const string Uab = "Uab";
    /// <summary>
    /// The BrowseName for the Ub component.
    /// </summary>
    public const string Ub = "Ub";
    /// <summary>
    /// The BrowseName for the Ubc component.
    /// </summary>
    public const string Ubc = "Ubc";
    /// <summary>
    /// The BrowseName for the Uc component.
    /// </summary>
    public const string Uc = "Uc";
    /// <summary>
    /// The BrowseName for the Uca component.
    /// </summary>
    public const string Uca = "Uca";
    /// <summary>
    /// The BrowseName for the ZSI_IN_ONOFF_dST component.
    /// </summary>
    public const string ZSIINONOFFDST = "ZSI_IN_ONOFF_dST";
    /// <summary>
    /// The BrowseName for the http://sentron.org/ThreeWA/ component.
    /// </summary>
    public const string httpSentronOrgThreeWA = "http://sentron.org/ThreeWA/";
}
#endregion

#region Namespace Declarations
/// <summary>
/// Defines constants for all namespaces referenced by the Model.
/// </summary>
public static partial class Namespaces
{
    /// <summary>
    /// The URI for the OpcUa namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUa = "http://opcfoundation.org/UA/";

    /// <summary>
    /// The URI for the OpcUaXsd namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUaXsd = "http://opcfoundation.org/UA/2008/02/Types.xsd";

    /// <summary>
    /// The URI for the ThreeWA namespace.
    /// </summary>
    public const string ThreeWA = "http://sentron.org/ThreeWA/";

    /// <summary>
    /// The URI for the ThreeWAXsd namespace.
    /// </summary>
    public const string ThreeWAXsd = "http://sentron.org/ThreeWA/Types.xsd";
}
#endregion

