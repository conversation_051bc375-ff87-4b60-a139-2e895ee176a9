using System.Collections.Generic;

namespace PanelHttp;

public class Item
{
    public string? Internal_name { get; set; }

    public string Name { get; set; }

    public int Id { get; set; }

    public string? Display_name { get; set; }

    public string? Unit { get; set; }

    public bool Valid { get; set; }
}

public class Embedded
{
    public List<Item> Item { get; set; }
}

public class UADataSource
{
    public string Item_id { get; set; }

    public string Device_Name { get; set; }

    public string IP { get; set; }

    public string TimeStamp { get; set; }

    public string Channel_count { get; set; }

    public Embedded Embedded { get; set; }
}
