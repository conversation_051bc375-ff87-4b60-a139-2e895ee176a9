using System.Xml.Serialization;
using HTTPInterface;
using OPCUAServer.Common;
using OPCUAServer.Utilites;
using Serilog;
using UnifiedAutomation.UaBase;

namespace OPCUAServer.Devices.PAC4200;

/// <summary>
/// A class that provides access to the underlying system.
/// </summary>
public class System : IDisposable
{
    private const int PUSHINTERVAL = 2000;

    private static readonly ILogger _logger = LoggingHelper.GetLogger<System>();
    private readonly object _lock = new();
    private readonly Dictionary<string, RegisterValue> _registers = new();

    private readonly Dictionary<int, BlockConfiguration> _blocks = new();

    private readonly PAC4200RealtimeData _realtimeDataObj = new();

    private Configuration? _configuration;
    private Timer? _doWorkTimer;

    public System()
    {
    }

    /// <summary>
    /// Initializes this instance.
    /// </summary>
    /// <param name="itemId">The item id.</param>
    public void Initialize(string itemId)
    {
        this.Load();

        this._realtimeDataObj.PAC4200RealtimeInit(itemId);

        this._doWorkTimer = new Timer(this.DoWork, itemId, PUSHINTERVAL, PUSHINTERVAL);
    }

    /// <summary>
    /// Gets the blockAddress configurations.
    /// </summary>
    /// <returns>Returns the block configurations.</returns>
    public IEnumerable<BlockConfiguration> GetBlocks()
    {
        return this._blocks.Values;
    }

    /// <summary>
    /// Reads the tag value.
    /// </summary>
    /// <param name="blockAddress">The blockAddress.</param>
    /// <param name="offset">The tag.</param>
    /// <returns>The value. null if no value exists.</returns>
    public object? Read(int blockAddress, int offset)
    {
        lock (this._lock)
        {
            if (blockAddress < 0 || offset < 0)
            {
                return null;
            }

            if (!this._blocks.TryGetValue(blockAddress, out var controller))
            {
                return null;
            }

            foreach (BlockProperty property in controller.Properties)
            {
                if (property.Offset == offset)
                {
                    if (this._registers.TryGetValue(property.Name, out var storedValue))
                    {
                        return DataTypeConvert.ConvertValueToDataType(storedValue, property.DataType);
                    }
                }
            }

            return null;
        }
    }

    /// <summary>
    /// Writes the tag value.
    /// </summary>
    /// <param name="blockAddress">The blockAddress.</param>
    /// <param name="offset">The tag.</param>
    /// <param name="value">The value.</param>
    /// <returns>
    /// True if the write was successful.
    /// </returns>
    public bool Write(int blockAddress, int offset, object value)
    {
        lock (this._lock)
        {
            if (blockAddress < 0 || offset < 0)
            {
                return false;
            }

            if (!this._blocks.TryGetValue(blockAddress, out var controller))
            {
                _logger.Error($"Failed to write to blockAddress {blockAddress} and tag {offset}. BlockAddress not found.");
                return false;
            }

            foreach (BlockProperty property in controller.Properties)
            {
                if (property.Offset == offset)
                {
                    if (!property.Writeable)
                    {
                        _logger.Error($"Failed to write to blockAddress {blockAddress} and tag {offset}. Property is not writeable.");
                        return false;
                    }

                    if (property.DataType == DataTypeIds.Double)
                    {
                        this.WriteToRegister(property.Name, (string)value, DataTypeIds.Double);
                    }
                    else if (property.DataType == DataTypeIds.Int32)
                    {
                        this.WriteToRegister(property.Name, (string)value, DataTypeIds.Int32);
                    }
                    else if (property.DataType == DataTypeIds.String)
                    {
                        this.WriteToRegister(property.Name, (string)value, DataTypeIds.String);
                    }
                }

                _logger.Information($"Successfully write to blockAddress {blockAddress} and tag {offset}.");
                return true;
            }

            _logger.Error($"Failed to write to blockAddress {blockAddress} and tag {offset}. Property not found.");
            return false;
        }
    }

    /// <summary>
    /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
    /// </summary>
    public void Dispose()
    {
        this.Dispose(true);
    }

    /// <summary>
    /// Releases unmanaged and - optionally - managed resources
    /// </summary>
    /// <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>true</c> to release only unmanaged resources.</param>
    protected void Dispose(bool disposing)
    {
        if (disposing)
        {
            this._doWorkTimer?.Dispose();
            this._doWorkTimer = null;
        }
    }

    /// <summary>
    /// Loads the configuration for the system.
    /// </summary>
    private void Load()
    {
        var assembly = PlatformUtils.GetAssembly(typeof(System));
        foreach (string resourceName in assembly.GetManifestResourceNames())
        {
            if (resourceName.EndsWith("GeneralConfiguration.xml"))
            {
                try
                {
                    using Stream? istrm = assembly.GetManifestResourceStream(resourceName);
                    if (istrm is null)
                    {
                        _logger.Error("Failed to load configuration for PAC4200 system from resource: {ResourceName}", resourceName);
                        continue;
                    }

                    var serializer = new XmlSerializer(typeof(Configuration));
                    var deserializedObject = serializer.Deserialize(istrm);

                    if (deserializedObject is Configuration config)
                    {
                        this._configuration = config;
                        _logger.Information("PAC4200 configuration loaded successfully from {ResourceName}", resourceName);
                    }
                    else
                    {
                        _logger.Error(
                            "Deserialized object is not of type Configuration. Actual type: {ActualType}",
                            deserializedObject?.GetType().Name ?? "null");
                        throw new InvalidOperationException("Failed to deserialize configuration: unexpected type");
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "Failed to deserialize PAC4200 configuration from {ResourceName}", resourceName);
                    throw;
                }
            }
        }

        if (this._configuration?.Controllers is not null)
        {
            int blockIndex = 0;

            foreach (var controller in this._configuration.Controllers)
            {
                var data = new BlockConfiguration()
                {
                    Address = blockIndex,
                    Name = controller.Name,
                    Type = controller.Type,
                    Properties = new List<BlockProperty>()
                };

                if (controller.Properties is not null)
                {
                    int propertyIndex = 0;

                    foreach (var property in controller.Properties)
                    {
                        var dataTypeId = NodeId.Parse(property.DataType);

                        data.Properties.Add(new BlockProperty()
                        {
                            Offset = propertyIndex,
                            Name = property.Name,
                            DataType = dataTypeId,
                            Writeable = property.Writeable,
                        });

                        this.WriteToRegister(property.Name, property.Value, dataTypeId);

                        propertyIndex++;
                    }
                }

                this._blocks[blockIndex] = data;
                blockIndex++;
            }
        }
    }

    /// <summary>
    /// Writes the specified offset.
    /// </summary>
    /// <param name="name">The name.</param>
    /// <param name="value">The value.</param>
    /// <param name="dataType">The data type.</param>
    private void WriteToRegister(string name, string value, NodeId dataType)
    {
        lock (_lock)
        {
            _registers[name] = new RegisterValue { Value = value, DataType = dataType };
        }
    }

    /// <summary>
    /// Does the simulation.
    /// </summary>
    /// <param name="state">The state.</param>
    private void DoWork(object? state)
    {
        try
        {
            lock (this._lock)
            {
                string? sItem = state as string;
                if (string.IsNullOrEmpty(sItem))
                {
                    _logger.Error("Failed to run simulation. ItemId is null or empty.");
                    return;
                }

                this._realtimeDataObj.PAC4200RealtimeUpdate(sItem);
                foreach (var blockAddress in this._blocks)
                {
                    foreach (var property in blockAddress.Value.Properties)
                    {
                        string channelName = property.Name;
                        string val = this.GetChannelValue(channelName);
                        this.WriteToRegister(channelName, val, DataTypeIds.String);
                    }
                }
            }
        }
        catch (Exception e)
        {
            _logger.Error(e, "Failed run simulation.");
        }
    }

    private string GetChannelValue(string firstName)
    {
        // todo: type check
        return this._realtimeDataObj.GetChannelValue(firstName);
    }
}
