<?xml version="1.0" encoding="utf-8"?>
<UANodeSet xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:uax="http://opcfoundation.org/UA/2008/02/Types.xsd" xmlns="http://opcfoundation.org/UA/2011/03/UANodeSet.xsd" xmlns:s1="http://sentron.org/PanelHistoricalMsg/Types.xsd" xmlns:ua="http://unifiedautomation.com/Configuration/NodeSet.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <NamespaceUris>
        <Uri>http://sentron.org/PanelHistoricalMsg/</Uri>
    </NamespaceUris>
    <Models>
        <Model ModelUri="http://sentron.org/PanelHistoricalMsg/" PublicationDate="2023-08-24T07:15:46Z" Version="1.0.0">
            <RequiredModel ModelUri="http://opcfoundation.org/UA/" PublicationDate="2022-01-24T00:00:00Z" Version="1.05.01"/>
        </Model>
    </Models>
    <Aliases>
        <Alias Alias="Boolean">i=1</Alias>
        <Alias Alias="String">i=12</Alias>
        <Alias Alias="DateTime">i=13</Alias>
        <Alias Alias="HasModellingRule">i=37</Alias>
        <Alias Alias="HasTypeDefinition">i=40</Alias>
        <Alias Alias="HasSubtype">i=45</Alias>
        <Alias Alias="HasProperty">i=46</Alias>
        <Alias Alias="HasComponent">i=47</Alias>
        <Alias Alias="IdType">i=256</Alias>
        <Alias Alias="NumericRange">i=291</Alias>
    </Aliases>
    <Extensions>
        <Extension>
            <ua:ModelInfo Tool="UaModeler" Hash="E/jZjhALOR7/FyAxadtJMg==" Version="1.6.7"/>
        </Extension>
    </Extensions>
    <UAObjectType NodeId="ns=1;i=1003" BrowseName="1:PanelHistoricalMsg">
        <DisplayName>PanelHistoricalMsg</DisplayName>
        <References>
            <Reference ReferenceType="HasComponent">ns=1;i=6001</Reference>
            <Reference ReferenceType="HasSubtype" IsForward="false">i=58</Reference>
        </References>
    </UAObjectType>
    <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6001" BrowseName="1:BaseDataVariable1" AccessLevel="3">
        <DisplayName>BaseDataVariable1</DisplayName>
        <References>
            <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
            <Reference ReferenceType="HasModellingRule">i=78</Reference>
            <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
        </References>
    </UAVariable>
    <UAObject SymbolicName="http___sentron_org_PanelHistoricalMsg_" NodeId="ns=1;i=5001" BrowseName="1:http://sentron.org/PanelHistoricalMsg/">
        <DisplayName>http://sentron.org/PanelHistoricalMsg/</DisplayName>
        <References>
            <Reference ReferenceType="HasTypeDefinition">i=11616</Reference>
            <Reference ReferenceType="HasComponent" IsForward="false">i=11715</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6002</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6003</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6004</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6005</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6006</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6007</Reference>
            <Reference ReferenceType="HasProperty">ns=1;i=6008</Reference>
        </References>
    </UAObject>
    <UAVariable DataType="Boolean" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6002" BrowseName="IsNamespaceSubset">
        <DisplayName>IsNamespaceSubset</DisplayName>
        <References>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
        </References>
        <Value>
            <uax:Boolean>false</uax:Boolean>
        </Value>
    </UAVariable>
    <UAVariable DataType="DateTime" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6003" BrowseName="NamespacePublicationDate">
        <DisplayName>NamespacePublicationDate</DisplayName>
        <References>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
        </References>
        <Value>
            <uax:DateTime>2023-08-24T07:15:46Z</uax:DateTime>
        </Value>
    </UAVariable>
    <UAVariable DataType="String" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6004" BrowseName="NamespaceUri">
        <DisplayName>NamespaceUri</DisplayName>
        <References>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
        </References>
        <Value>
            <uax:String>http://sentron.org/PanelHistoricalMsg/</uax:String>
        </Value>
    </UAVariable>
    <UAVariable DataType="String" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6005" BrowseName="NamespaceVersion">
        <DisplayName>NamespaceVersion</DisplayName>
        <References>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
        </References>
        <Value>
            <uax:String>1.0.0</uax:String>
        </Value>
    </UAVariable>
    <UAVariable DataType="IdType" ParentNodeId="ns=1;i=5001" ValueRank="1" NodeId="ns=1;i=6006" ArrayDimensions="0" BrowseName="StaticNodeIdTypes">
        <DisplayName>StaticNodeIdTypes</DisplayName>
        <References>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
        </References>
    </UAVariable>
    <UAVariable DataType="NumericRange" ParentNodeId="ns=1;i=5001" ValueRank="1" NodeId="ns=1;i=6007" ArrayDimensions="0" BrowseName="StaticNumericNodeIdRange">
        <DisplayName>StaticNumericNodeIdRange</DisplayName>
        <References>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
        </References>
    </UAVariable>
    <UAVariable DataType="String" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6008" BrowseName="StaticStringNodeIdPattern">
        <DisplayName>StaticStringNodeIdPattern</DisplayName>
        <References>
            <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
            <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
        </References>
    </UAVariable>
</UANodeSet>
