namespace PanelHttp;

public class ChannelVal
{
    public string? Name { get; set; }

    public int Id { get; set; }

    public string? Val { get; set; }

    public bool Valid { get; set; }
}

public class UARealtimeData
{
    public string Item_id { get; set; }

    public string Device_Name { get; set; }

    public string IP { get; set; }

    public string TimeStamp { get; set; }

    public string Channel_count { get; set; }

    public List<ChannelVal> ChannelList { get; set; }
}

