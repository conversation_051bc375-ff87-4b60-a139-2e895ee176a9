using UnifiedAutomation.UaBase;

namespace Sentron.Substation;

#region DataType Identifiers
/// <summary>
/// A class that declares constants for all DataTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypes
{
}
#endregion

#region Object Identifiers
/// <summary>
/// A class that declares constants for all Objects in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Objects
{
    /// <summary>
    /// The identifier for the http://sentron.org/Substation/ Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Substation_ = 5001;

}
#endregion

#region ObjectType Identifiers
/// <summary>
/// A class that declares constants for all ObjectTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypes
{
    /// <summary>
    /// The identifier for the Substation ObjectType.
    /// </summary>
    public const uint Substation = 1003;

}
#endregion

#region Method Identifiers
/// <summary>
/// A class that declares constants for all Methods in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Methods
{
}
#endregion

#region ReferenceType Identifiers
/// <summary>
/// A class that declares constants for all ReferenceTyped in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypes
{
}
#endregion

#region Variable Identifiers
/// <summary>
/// A class that declares constants for all Variables in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Variables
{
    /// <summary>
    /// The identifier for the Total_P Variable.
    /// </summary>
    public const uint Substation_Total_P = 6100;

    /// <summary>
    /// The identifier for the Total_Q Variable.
    /// </summary>
    public const uint Substation_Total_Q = 6101;

    /// <summary>
    /// The identifier for the Total_S Variable.
    /// </summary>
    public const uint Substation_Total_S = 6102;

    /// <summary>
    /// The identifier for the IsNamespaceSubset Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Substation__IsNamespaceSubset = 6002;

    /// <summary>
    /// The identifier for the NamespacePublicationDate Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Substation__NamespacePublicationDate = 6003;

    /// <summary>
    /// The identifier for the NamespaceUri Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Substation__NamespaceUri = 6004;

    /// <summary>
    /// The identifier for the NamespaceVersion Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Substation__NamespaceVersion = 6005;

    /// <summary>
    /// The identifier for the StaticNodeIdTypes Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Substation__StaticNodeIdTypes = 6006;

    /// <summary>
    /// The identifier for the StaticNumericNodeIdRange Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Substation__StaticNumericNodeIdRange = 6007;

    /// <summary>
    /// The identifier for the StaticStringNodeIdPattern Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Substation__StaticStringNodeIdPattern = 6008;

}
#endregion

#region VariableTypes Identifiers
/// <summary>
/// A class that declares constants for all VariableTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypes
{
}
#endregion

#region DataType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all DataTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypeIds
{
}
#endregion

#region Method Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Methods in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class MethodIds
{
}
#endregion

#region Object Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectIds
{
    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Substation_ Object.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Substation_ = new ExpandedNodeId(Objects.Namespaces_http___sentron_org_Substation_, Namespaces.Substation);

}
#endregion

#region ObjectType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypeIds
{
    /// <summary>
    /// The identifier for the Substation ObjectType.
    /// </summary>
    public static readonly ExpandedNodeId Substation = new ExpandedNodeId(ObjectTypes.Substation, Namespaces.Substation);

}
#endregion

#region ReferenceType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all ReferenceTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypeIds
{
}
#endregion

#region Variable Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Variables in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableIds
{
    /// <summary>
    /// The identifier for the Substation_Total_P Variable.
    /// </summary>
    public static readonly ExpandedNodeId Substation_Total_P = new ExpandedNodeId(Variables.Substation_Total_P, Namespaces.Substation);

    /// <summary>
    /// The identifier for the Substation_Total_Q Variable.
    /// </summary>
    public static readonly ExpandedNodeId Substation_Total_Q = new ExpandedNodeId(Variables.Substation_Total_Q, Namespaces.Substation);

    /// <summary>
    /// The identifier for the Substation_Total_S Variable.
    /// </summary>
    public static readonly ExpandedNodeId Substation_Total_S = new ExpandedNodeId(Variables.Substation_Total_S, Namespaces.Substation);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Substation__IsNamespaceSubset Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Substation__IsNamespaceSubset = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Substation__IsNamespaceSubset, Namespaces.Substation);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Substation__NamespacePublicationDate Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Substation__NamespacePublicationDate = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Substation__NamespacePublicationDate, Namespaces.Substation);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Substation__NamespaceUri Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Substation__NamespaceUri = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Substation__NamespaceUri, Namespaces.Substation);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Substation__NamespaceVersion Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Substation__NamespaceVersion = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Substation__NamespaceVersion, Namespaces.Substation);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Substation__StaticNodeIdTypes Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Substation__StaticNodeIdTypes = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Substation__StaticNodeIdTypes, Namespaces.Substation);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Substation__StaticNumericNodeIdRange Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Substation__StaticNumericNodeIdRange = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Substation__StaticNumericNodeIdRange, Namespaces.Substation);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Substation__StaticStringNodeIdPattern Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Substation__StaticStringNodeIdPattern = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Substation__StaticStringNodeIdPattern, Namespaces.Substation);

}
#endregion

#region VariableType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all VariableType in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypeIds
{
}
#endregion

#region BrowseName Declarations
/// <summary>
/// Declares all of the BrowseNames used in the Model.
/// </summary>
public static partial class BrowseNames
{
    /// <summary>
    /// The BrowseName for the Substation component.
    /// </summary>
    public const string Substation = "Substation";
    /// <summary>
    /// The BrowseName for the Total_P component.
    /// </summary>
    public const string TotalP = "Total_P";
    /// <summary>
    /// The BrowseName for the Total_Q component.
    /// </summary>
    public const string TotalQ = "Total_Q";
    /// <summary>
    /// The BrowseName for the Total_S component.
    /// </summary>
    public const string TotalS = "Total_S";
    /// <summary>
    /// The BrowseName for the http://sentron.org/Substation/ component.
    /// </summary>
    public const string httpSentronOrgSubstation = "http://sentron.org/Substation/";
}
#endregion

#region Namespace Declarations
/// <summary>
/// Defines constants for all namespaces referenced by the Model.
/// </summary>
public static partial class Namespaces
{
    /// <summary>
    /// The URI for the OpcUa namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUa = "http://opcfoundation.org/UA/";

    /// <summary>
    /// The URI for the OpcUaXsd namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUaXsd = "http://opcfoundation.org/UA/2008/02/Types.xsd";

    /// <summary>
    /// The URI for the Substation namespace.
    /// </summary>
    public const string Substation = "http://sentron.org/Substation/";

    /// <summary>
    /// The URI for the SubstationXsd namespace.
    /// </summary>
    public const string SubstationXsd = "http://sentron.org/Substation/Types.xsd";
}
#endregion

