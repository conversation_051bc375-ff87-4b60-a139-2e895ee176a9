using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaServer;

namespace PanelAlarm;

internal partial class PanelAlarmNodeManager
{
    enum BlockMethod
    {
        Start = 1,
        Stop = 2,
    }

    private void SetMethodUserData(BlockConfiguration block)
    {
        // set addressing information for method nodes that allows them to be called.
        this.SetChildUserData(
            new NodeId(block.Name, this.InstanceNamespaceIndex),
            new QualifiedName(Sentron.PanelAlarm.BrowseNames.Start, this.TypeNamespaceIndex),
            new SystemFunction() { Address = block.Address, Function = BlockMethod.Start });

        this.SetChildUserData(
            new NodeId(block.Name, this.InstanceNamespaceIndex),
            new QualifiedName(Sentron.PanelAlarm.BrowseNames.Stop, this.TypeNamespaceIndex),
            new SystemFunction() { Address = block.Address, Function = BlockMethod.Stop });
    }

    /// <summary>
    /// Gets the method dispatcher.
    /// </summary>
    /// <param name="context">The context.</param>
    /// <param name="methodHandle">The method handle.</param>
    /// <returns></returns>
    /// [Forward Method to Method Handler]
    protected override CallMethodEventHandler GetMethodDispatcher(
        RequestContext context,
        MethodHandle methodHandle)
    {
        if (methodHandle.MethodData is SystemFunction)
        {
            return this.DispatchControllerMethod;
        }

        return null;
    }

    // [Forward Method to Method Handler]

    /// <summary>
    /// Dispatches a method to the controller.
    /// </summary>
    /// [Implement Method]
    private StatusCode DispatchControllerMethod(
        RequestContext context,
        MethodHandle methodHandle,
        IList<Variant> inputArguments,
        List<StatusCode> inputArgumentResults,
        List<Variant> outputArguments)
    {
        SystemFunction data = methodHandle.MethodData as SystemFunction;

        if (data != null)
        {
            switch (data.Function)
            {
                case BlockMethod.Start:
                    {
                        return this.m_system.Start(data.Address);
                    }

                case BlockMethod.Stop:
                    {
                        return this.m_system.Stop(data.Address);
                    }
            }
        }

        return StatusCodes.BadNotImplemented;
    }

    // [Implement Method]

    #region SystemFunction Class

    // [SystemFunction]
    private class SystemFunction
    {
        public int Address;
        public BlockMethod Function;
    }

    // [SystemFunction]
    #endregion
}
