/******************************************************************************
**
** <auto-generated>
**     This code was generated by a tool: UaModeler
**     Runtime Version: 1.6.7, using .NET Server 3.1.0 template (version 0)
**
**     Changes to this file may cause incorrect behavior and will be lost if
**     the code is regenerated.
** </auto-generated>
**
** Copyright (c) 2006-2023 Unified Automation GmbH All rights reserved.
**
** Software License Agreement ("SLA") Version 2.8
**
** Unless explicitly acquired and licensed from Licensor under another
** license, the contents of this file are subject to the Software License
** Agreement ("SLA") Version 2.8, or subsequent versions
** as allowed by the SLA, and You may not copy or use this file in either
** source code or executable form, except in compliance with the terms and
** conditions of the SLA.
**
** All software distributed under the SLA is provided strictly on an
** "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESS OR IMPLIED,
** AND LICENSOR HEREBY DISCLAIMS ALL SUCH WARRANTIES, INCLUDING WITHOUT
** LIMITATION, ANY WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
** PURPOSE, QUIET ENJOYMENT, OR NON-INFRINGEMENT. See the SLA for specific
** language governing rights and limitations under the SLA.
**
** Project: .NET OPC UA SDK information model for namespace http://sentron.org/Panel/
**
** Description: OPC Unified Architecture Software Development Kit.
**
** The complete license agreement can be found here:
** http://unifiedautomation.com/License/SLA/2.8/
**
** Created: 11.08.2023
**
******************************************************************************/

using System;
using System.Collections.Generic;
using System.Text;
using System.Reflection;
using System.Xml;
using System.Runtime.Serialization;
using UnifiedAutomation.UaBase;

namespace Sentron.Panel;

#region DataType Identifiers
/// <summary>
/// A class that declares constants for all DataTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypes
{
}
#endregion

#region Object Identifiers
/// <summary>
/// A class that declares constants for all Objects in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Objects
{
    /// <summary>
    /// The identifier for the http://sentron.org/Panel/ Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Panel_ = 5001;

}
#endregion

#region ObjectType Identifiers
/// <summary>
/// A class that declares constants for all ObjectTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypes
{
    /// <summary>
    /// The identifier for the Panel ObjectType.
    /// </summary>
    public const uint Panel = 1003;

}
#endregion

#region Method Identifiers
/// <summary>
/// A class that declares constants for all Methods in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Methods
{
}
#endregion

#region ReferenceType Identifiers
/// <summary>
/// A class that declares constants for all ReferenceTyped in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypes
{
}
#endregion

#region Variable Identifiers
/// <summary>
/// A class that declares constants for all Variables in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Variables
{
    /// <summary>
    /// The identifier for the AConnector_Left1 Variable.
    /// </summary>
    public const uint Panel_AConnector_Left1 = 6137;

    /// <summary>
    /// The identifier for the AConnector_Left2 Variable.
    /// </summary>
    public const uint Panel_AConnector_Left2 = 6145;

    /// <summary>
    /// The identifier for the AConnector_Right1 Variable.
    /// </summary>
    public const uint Panel_AConnector_Right1 = 6141;

    /// <summary>
    /// The identifier for the AConnector_Right2 Variable.
    /// </summary>
    public const uint Panel_AConnector_Right2 = 6149;

    /// <summary>
    /// The identifier for the AConnectPoint_1 Variable.
    /// </summary>
    public const uint Panel_AConnectPoint_1 = 6129;

    /// <summary>
    /// The identifier for the AConnectPoint_2 Variable.
    /// </summary>
    public const uint Panel_AConnectPoint_2 = 6133;

    /// <summary>
    /// The identifier for the APhaseTemp1 Variable.
    /// </summary>
    public const uint Panel_APhaseTemp1 = 6121;

    /// <summary>
    /// The identifier for the APhaseTemp2 Variable.
    /// </summary>
    public const uint Panel_APhaseTemp2 = 6122;

    /// <summary>
    /// The identifier for the BConnector_Left1 Variable.
    /// </summary>
    public const uint Panel_BConnector_Left1 = 6138;

    /// <summary>
    /// The identifier for the BConnector_Left2 Variable.
    /// </summary>
    public const uint Panel_BConnector_Left2 = 6146;

    /// <summary>
    /// The identifier for the BConnector_Right1 Variable.
    /// </summary>
    public const uint Panel_BConnector_Right1 = 6142;

    /// <summary>
    /// The identifier for the BConnector_Right2 Variable.
    /// </summary>
    public const uint Panel_BConnector_Right2 = 6150;

    /// <summary>
    /// The identifier for the BConnectPoint_1 Variable.
    /// </summary>
    public const uint Panel_BConnectPoint_1 = 6130;

    /// <summary>
    /// The identifier for the BConnectPoint_2 Variable.
    /// </summary>
    public const uint Panel_BConnectPoint_2 = 6134;

    /// <summary>
    /// The identifier for the BPhaseTemp1 Variable.
    /// </summary>
    public const uint Panel_BPhaseTemp1 = 6123;

    /// <summary>
    /// The identifier for the BPhaseTemp1 Variable.
    /// </summary>
    public const uint Panel_BPhaseTemp3 = 6125;

    /// <summary>
    /// The identifier for the BPhaseTemp2 Variable.
    /// </summary>
    public const uint Panel_BPhaseTemp2 = 6124;

    /// <summary>
    /// The identifier for the CConnector_Left1 Variable.
    /// </summary>
    public const uint Panel_CConnector_Left1 = 6139;

    /// <summary>
    /// The identifier for the CConnector_Left2 Variable.
    /// </summary>
    public const uint Panel_CConnector_Left2 = 6147;

    /// <summary>
    /// The identifier for the CConnector_Right1 Variable.
    /// </summary>
    public const uint Panel_CConnector_Right1 = 6143;

    /// <summary>
    /// The identifier for the CConnector_Right2 Variable.
    /// </summary>
    public const uint Panel_CConnector_Right2 = 6151;

    /// <summary>
    /// The identifier for the CConnectPoint_1 Variable.
    /// </summary>
    public const uint Panel_CConnectPoint_1 = 6131;

    /// <summary>
    /// The identifier for the CConnectPoint_2 Variable.
    /// </summary>
    public const uint Panel_CConnectPoint_2 = 6135;

    /// <summary>
    /// The identifier for the CPhaseTemp2 Variable.
    /// </summary>
    public const uint Panel_CPhaseTemp2 = 6126;

    /// <summary>
    /// The identifier for the F Variable.
    /// </summary>
    public const uint Panel_F = 6113;

    /// <summary>
    /// The identifier for the HaveAlarm Variable.
    /// </summary>
    public const uint Panel_HaveAlarm = 6100;

    /// <summary>
    /// The identifier for the Ia Variable.
    /// </summary>
    public const uint Panel_Ia = 6107;

    /// <summary>
    /// The identifier for the Ib Variable.
    /// </summary>
    public const uint Panel_Ib = 6108;

    /// <summary>
    /// The identifier for the Ic Variable.
    /// </summary>
    public const uint Panel_Ic = 6109;

    /// <summary>
    /// The identifier for the NConnector_Left1 Variable.
    /// </summary>
    public const uint Panel_NConnector_Left1 = 6140;

    /// <summary>
    /// The identifier for the NConnector_Left2 Variable.
    /// </summary>
    public const uint Panel_NConnector_Left2 = 6148;

    /// <summary>
    /// The identifier for the NConnector_Right1 Variable.
    /// </summary>
    public const uint Panel_NConnector_Right1 = 6144;

    /// <summary>
    /// The identifier for the NConnector_Right2 Variable.
    /// </summary>
    public const uint Panel_NConnector_Right2 = 6152;

    /// <summary>
    /// The identifier for the NConnectPoint_1 Variable.
    /// </summary>
    public const uint Panel_NConnectPoint_1 = 6132;

    /// <summary>
    /// The identifier for the NConnectPoint_2 Variable.
    /// </summary>
    public const uint Panel_NConnectPoint_2 = 6136;

    /// <summary>
    /// The identifier for the NPhaseTemp1 Variable.
    /// </summary>
    public const uint Panel_NPhaseTemp1 = 6127;

    /// <summary>
    /// The identifier for the NPhaseTemp2 Variable.
    /// </summary>
    public const uint Panel_NPhaseTemp2 = 6128;

    /// <summary>
    /// The identifier for the P Variable.
    /// </summary>
    public const uint Panel_P = 6110;

    /// <summary>
    /// The identifier for the PowFactor Variable.
    /// </summary>
    public const uint Panel_PowFactor = 6114;

    /// <summary>
    /// The identifier for the Q Variable.
    /// </summary>
    public const uint Panel_Q = 6111;

    /// <summary>
    /// The identifier for the S Variable.
    /// </summary>
    public const uint Panel_S = 6112;

    /// <summary>
    /// The identifier for the THD_Ia Variable.
    /// </summary>
    public const uint Panel_THD_Ia = 6118;

    /// <summary>
    /// The identifier for the THD_Ib Variable.
    /// </summary>
    public const uint Panel_THD_Ib = 6119;

    /// <summary>
    /// The identifier for the THD_Ic Variable.
    /// </summary>
    public const uint Panel_THD_Ic = 6120;

    /// <summary>
    /// The identifier for the THD_Ua Variable.
    /// </summary>
    public const uint Panel_THD_Ua = 6115;

    /// <summary>
    /// The identifier for the THD_Ub Variable.
    /// </summary>
    public const uint Panel_THD_Ub = 6116;

    /// <summary>
    /// The identifier for the THD_Uc Variable.
    /// </summary>
    public const uint Panel_THD_Uc = 6117;

    /// <summary>
    /// The identifier for the Ua Variable.
    /// </summary>
    public const uint Panel_Ua = 6101;

    /// <summary>
    /// The identifier for the Uab Variable.
    /// </summary>
    public const uint Panel_Uab = 6104;

    /// <summary>
    /// The identifier for the Ub Variable.
    /// </summary>
    public const uint Panel_Ub = 6102;

    /// <summary>
    /// The identifier for the Ubc Variable.
    /// </summary>
    public const uint Panel_Ubc = 6105;

    /// <summary>
    /// The identifier for the Uc Variable.
    /// </summary>
    public const uint Panel_Uc = 6103;

    /// <summary>
    /// The identifier for the Uca Variable.
    /// </summary>
    public const uint Panel_Uca = 6106;

    /// <summary>
    /// The identifier for the IsNamespaceSubset Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Panel__IsNamespaceSubset = 6002;

    /// <summary>
    /// The identifier for the NamespacePublicationDate Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Panel__NamespacePublicationDate = 6003;

    /// <summary>
    /// The identifier for the NamespaceUri Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Panel__NamespaceUri = 6004;

    /// <summary>
    /// The identifier for the NamespaceVersion Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Panel__NamespaceVersion = 6005;

    /// <summary>
    /// The identifier for the StaticNodeIdTypes Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Panel__StaticNodeIdTypes = 6006;

    /// <summary>
    /// The identifier for the StaticNumericNodeIdRange Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Panel__StaticNumericNodeIdRange = 6007;

    /// <summary>
    /// The identifier for the StaticStringNodeIdPattern Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_Panel__StaticStringNodeIdPattern = 6008;

}
#endregion

#region VariableTypes Identifiers
/// <summary>
/// A class that declares constants for all VariableTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypes
{
}
#endregion

#region DataType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all DataTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypeIds
{
}
#endregion

#region Method Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Methods in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class MethodIds
{
}
#endregion

#region Object Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectIds
{
    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Panel_ Object.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Panel_ = new ExpandedNodeId(Objects.Namespaces_http___sentron_org_Panel_, Namespaces.Panel);

}
#endregion

#region ObjectType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypeIds
{
    /// <summary>
    /// The identifier for the Panel ObjectType.
    /// </summary>
    public static readonly ExpandedNodeId Panel = new ExpandedNodeId(ObjectTypes.Panel, Namespaces.Panel);

}
#endregion

#region ReferenceType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all ReferenceTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypeIds
{
}
#endregion

#region Variable Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Variables in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableIds
{
    /// <summary>
    /// The identifier for the Panel_AConnector_Left1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_AConnector_Left1 = new ExpandedNodeId(Variables.Panel_AConnector_Left1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_AConnector_Left2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_AConnector_Left2 = new ExpandedNodeId(Variables.Panel_AConnector_Left2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_AConnector_Right1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_AConnector_Right1 = new ExpandedNodeId(Variables.Panel_AConnector_Right1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_AConnector_Right2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_AConnector_Right2 = new ExpandedNodeId(Variables.Panel_AConnector_Right2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_AConnectPoint_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_AConnectPoint_1 = new ExpandedNodeId(Variables.Panel_AConnectPoint_1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_AConnectPoint_2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_AConnectPoint_2 = new ExpandedNodeId(Variables.Panel_AConnectPoint_2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_APhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_APhaseTemp1 = new ExpandedNodeId(Variables.Panel_APhaseTemp1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_APhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_APhaseTemp2 = new ExpandedNodeId(Variables.Panel_APhaseTemp2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_BConnector_Left1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_BConnector_Left1 = new ExpandedNodeId(Variables.Panel_BConnector_Left1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_BConnector_Left2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_BConnector_Left2 = new ExpandedNodeId(Variables.Panel_BConnector_Left2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_BConnector_Right1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_BConnector_Right1 = new ExpandedNodeId(Variables.Panel_BConnector_Right1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_BConnector_Right2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_BConnector_Right2 = new ExpandedNodeId(Variables.Panel_BConnector_Right2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_BConnectPoint_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_BConnectPoint_1 = new ExpandedNodeId(Variables.Panel_BConnectPoint_1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_BConnectPoint_2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_BConnectPoint_2 = new ExpandedNodeId(Variables.Panel_BConnectPoint_2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_BPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_BPhaseTemp1 = new ExpandedNodeId(Variables.Panel_BPhaseTemp1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_BPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_BPhaseTemp3 = new ExpandedNodeId(Variables.Panel_BPhaseTemp3, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_BPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_BPhaseTemp2 = new ExpandedNodeId(Variables.Panel_BPhaseTemp2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_CConnector_Left1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_CConnector_Left1 = new ExpandedNodeId(Variables.Panel_CConnector_Left1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_CConnector_Left2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_CConnector_Left2 = new ExpandedNodeId(Variables.Panel_CConnector_Left2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_CConnector_Right1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_CConnector_Right1 = new ExpandedNodeId(Variables.Panel_CConnector_Right1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_CConnector_Right2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_CConnector_Right2 = new ExpandedNodeId(Variables.Panel_CConnector_Right2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_CConnectPoint_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_CConnectPoint_1 = new ExpandedNodeId(Variables.Panel_CConnectPoint_1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_CConnectPoint_2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_CConnectPoint_2 = new ExpandedNodeId(Variables.Panel_CConnectPoint_2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_CPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_CPhaseTemp2 = new ExpandedNodeId(Variables.Panel_CPhaseTemp2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_F Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_F = new ExpandedNodeId(Variables.Panel_F, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_HaveAlarm Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_HaveAlarm = new ExpandedNodeId(Variables.Panel_HaveAlarm, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_Ia Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_Ia = new ExpandedNodeId(Variables.Panel_Ia, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_Ib Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_Ib = new ExpandedNodeId(Variables.Panel_Ib, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_Ic Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_Ic = new ExpandedNodeId(Variables.Panel_Ic, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_NConnector_Left1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_NConnector_Left1 = new ExpandedNodeId(Variables.Panel_NConnector_Left1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_NConnector_Left2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_NConnector_Left2 = new ExpandedNodeId(Variables.Panel_NConnector_Left2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_NConnector_Right1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_NConnector_Right1 = new ExpandedNodeId(Variables.Panel_NConnector_Right1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_NConnector_Right2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_NConnector_Right2 = new ExpandedNodeId(Variables.Panel_NConnector_Right2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_NConnectPoint_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_NConnectPoint_1 = new ExpandedNodeId(Variables.Panel_NConnectPoint_1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_NConnectPoint_2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_NConnectPoint_2 = new ExpandedNodeId(Variables.Panel_NConnectPoint_2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_NPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_NPhaseTemp1 = new ExpandedNodeId(Variables.Panel_NPhaseTemp1, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_NPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_NPhaseTemp2 = new ExpandedNodeId(Variables.Panel_NPhaseTemp2, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_P Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_P = new ExpandedNodeId(Variables.Panel_P, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_PowFactor Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_PowFactor = new ExpandedNodeId(Variables.Panel_PowFactor, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_Q Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_Q = new ExpandedNodeId(Variables.Panel_Q, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_S Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_S = new ExpandedNodeId(Variables.Panel_S, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_THD_Ia Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_THD_Ia = new ExpandedNodeId(Variables.Panel_THD_Ia, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_THD_Ib Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_THD_Ib = new ExpandedNodeId(Variables.Panel_THD_Ib, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_THD_Ic Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_THD_Ic = new ExpandedNodeId(Variables.Panel_THD_Ic, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_THD_Ua Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_THD_Ua = new ExpandedNodeId(Variables.Panel_THD_Ua, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_THD_Ub Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_THD_Ub = new ExpandedNodeId(Variables.Panel_THD_Ub, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_THD_Uc Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_THD_Uc = new ExpandedNodeId(Variables.Panel_THD_Uc, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_Ua Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_Ua = new ExpandedNodeId(Variables.Panel_Ua, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_Uab Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_Uab = new ExpandedNodeId(Variables.Panel_Uab, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_Ub Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_Ub = new ExpandedNodeId(Variables.Panel_Ub, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_Ubc Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_Ubc = new ExpandedNodeId(Variables.Panel_Ubc, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_Uc Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_Uc = new ExpandedNodeId(Variables.Panel_Uc, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Panel_Uca Variable.
    /// </summary>
    public static readonly ExpandedNodeId Panel_Uca = new ExpandedNodeId(Variables.Panel_Uca, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Panel__IsNamespaceSubset Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Panel__IsNamespaceSubset = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Panel__IsNamespaceSubset, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Panel__NamespacePublicationDate Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Panel__NamespacePublicationDate = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Panel__NamespacePublicationDate, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Panel__NamespaceUri Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Panel__NamespaceUri = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Panel__NamespaceUri, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Panel__NamespaceVersion Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Panel__NamespaceVersion = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Panel__NamespaceVersion, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Panel__StaticNodeIdTypes Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Panel__StaticNodeIdTypes = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Panel__StaticNodeIdTypes, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Panel__StaticNumericNodeIdRange Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Panel__StaticNumericNodeIdRange = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Panel__StaticNumericNodeIdRange, Namespaces.Panel);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_Panel__StaticStringNodeIdPattern Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_Panel__StaticStringNodeIdPattern = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_Panel__StaticStringNodeIdPattern, Namespaces.Panel);

}
#endregion

#region VariableType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all VariableType in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypeIds
{
}
#endregion

#region BrowseName Declarations
/// <summary>
/// Declares all of the BrowseNames used in the Model.
/// </summary>
public static partial class BrowseNames
{
    /// <summary>
    /// The BrowseName for the AConnectPoint_1 component.
    /// </summary>
    public const string AConnectPoint_1 = "AConnectPoint_1";
    /// <summary>
    /// The BrowseName for the AConnectPoint_2 component.
    /// </summary>
    public const string AConnectPoint_2 = "AConnectPoint_2";
    /// <summary>
    /// The BrowseName for the AConnector_Left1 component.
    /// </summary>
    public const string AConnector_Left1 = "AConnector_Left1";
    /// <summary>
    /// The BrowseName for the AConnector_Left2 component.
    /// </summary>
    public const string AConnector_Left2 = "AConnector_Left2";
    /// <summary>
    /// The BrowseName for the AConnector_Right1 component.
    /// </summary>
    public const string AConnector_Right1 = "AConnector_Right1";
    /// <summary>
    /// The BrowseName for the AConnector_Right2 component.
    /// </summary>
    public const string AConnector_Right2 = "AConnector_Right2";
    /// <summary>
    /// The BrowseName for the APhaseTemp1 component.
    /// </summary>
    public const string APhaseTemp1 = "APhaseTemp1";
    /// <summary>
    /// The BrowseName for the APhaseTemp2 component.
    /// </summary>
    public const string APhaseTemp2 = "APhaseTemp2";
    /// <summary>
    /// The BrowseName for the BConnectPoint_1 component.
    /// </summary>
    public const string BConnectPoint_1 = "BConnectPoint_1";
    /// <summary>
    /// The BrowseName for the BConnectPoint_2 component.
    /// </summary>
    public const string BConnectPoint_2 = "BConnectPoint_2";
    /// <summary>
    /// The BrowseName for the BConnector_Left1 component.
    /// </summary>
    public const string BConnector_Left1 = "BConnector_Left1";
    /// <summary>
    /// The BrowseName for the BConnector_Left2 component.
    /// </summary>
    public const string BConnector_Left2 = "BConnector_Left2";
    /// <summary>
    /// The BrowseName for the BConnector_Right1 component.
    /// </summary>
    public const string BConnector_Right1 = "BConnector_Right1";
    /// <summary>
    /// The BrowseName for the BConnector_Right2 component.
    /// </summary>
    public const string BConnector_Right2 = "BConnector_Right2";
    /// <summary>
    /// The BrowseName for the BPhaseTemp1 component.
    /// </summary>
    public const string BPhaseTemp1 = "BPhaseTemp1";
    /// <summary>
    /// The BrowseName for the BPhaseTemp2 component.
    /// </summary>
    public const string BPhaseTemp2 = "BPhaseTemp2";
    /// <summary>
    /// The BrowseName for the CConnectPoint_1 component.
    /// </summary>
    public const string CConnectPoint_1 = "CConnectPoint_1";
    /// <summary>
    /// The BrowseName for the CConnectPoint_2 component.
    /// </summary>
    public const string CConnectPoint_2 = "CConnectPoint_2";
    /// <summary>
    /// The BrowseName for the CConnector_Left1 component.
    /// </summary>
    public const string CConnector_Left1 = "CConnector_Left1";
    /// <summary>
    /// The BrowseName for the CConnector_Left2 component.
    /// </summary>
    public const string CConnector_Left2 = "CConnector_Left2";
    /// <summary>
    /// The BrowseName for the CConnector_Right1 component.
    /// </summary>
    public const string CConnector_Right1 = "CConnector_Right1";
    /// <summary>
    /// The BrowseName for the CConnector_Right2 component.
    /// </summary>
    public const string CConnector_Right2 = "CConnector_Right2";
    /// <summary>
    /// The BrowseName for the CPhaseTemp2 component.
    /// </summary>
    public const string CPhaseTemp2 = "CPhaseTemp2";
    /// <summary>
    /// The BrowseName for the F component.
    /// </summary>
    public const string F = "F";
    /// <summary>
    /// The BrowseName for the HaveAlarm component.
    /// </summary>
    public const string HaveAlarm = "HaveAlarm";
    /// <summary>
    /// The BrowseName for the Ia component.
    /// </summary>
    public const string Ia = "Ia";
    /// <summary>
    /// The BrowseName for the Ib component.
    /// </summary>
    public const string Ib = "Ib";
    /// <summary>
    /// The BrowseName for the Ic component.
    /// </summary>
    public const string Ic = "Ic";
    /// <summary>
    /// The BrowseName for the NConnectPoint_1 component.
    /// </summary>
    public const string NConnectPoint_1 = "NConnectPoint_1";
    /// <summary>
    /// The BrowseName for the NConnectPoint_2 component.
    /// </summary>
    public const string NConnectPoint_2 = "NConnectPoint_2";
    /// <summary>
    /// The BrowseName for the NConnector_Left1 component.
    /// </summary>
    public const string NConnector_Left1 = "NConnector_Left1";
    /// <summary>
    /// The BrowseName for the NConnector_Left2 component.
    /// </summary>
    public const string NConnector_Left2 = "NConnector_Left2";
    /// <summary>
    /// The BrowseName for the NConnector_Right1 component.
    /// </summary>
    public const string NConnector_Right1 = "NConnector_Right1";
    /// <summary>
    /// The BrowseName for the NConnector_Right2 component.
    /// </summary>
    public const string NConnector_Right2 = "NConnector_Right2";
    /// <summary>
    /// The BrowseName for the NPhaseTemp1 component.
    /// </summary>
    public const string NPhaseTemp1 = "NPhaseTemp1";
    /// <summary>
    /// The BrowseName for the NPhaseTemp2 component.
    /// </summary>
    public const string NPhaseTemp2 = "NPhaseTemp2";
    /// <summary>
    /// The BrowseName for the P component.
    /// </summary>
    public const string P = "P";
    /// <summary>
    /// The BrowseName for the Panel component.
    /// </summary>
    public const string Panel = "Panel";
    /// <summary>
    /// The BrowseName for the PowFactor component.
    /// </summary>
    public const string PowFactor = "PowFactor";
    /// <summary>
    /// The BrowseName for the Q component.
    /// </summary>
    public const string Q = "Q";
    /// <summary>
    /// The BrowseName for the S component.
    /// </summary>
    public const string S = "S";
    /// <summary>
    /// The BrowseName for the THD_Ia component.
    /// </summary>
    public const string THD_Ia = "THD_Ia";
    /// <summary>
    /// The BrowseName for the THD_Ib component.
    /// </summary>
    public const string THD_Ib = "THD_Ib";
    /// <summary>
    /// The BrowseName for the THD_Ic component.
    /// </summary>
    public const string THD_Ic = "THD_Ic";
    /// <summary>
    /// The BrowseName for the THD_Ua component.
    /// </summary>
    public const string THD_Ua = "THD_Ua";
    /// <summary>
    /// The BrowseName for the THD_Ub component.
    /// </summary>
    public const string THD_Ub = "THD_Ub";
    /// <summary>
    /// The BrowseName for the THD_Uc component.
    /// </summary>
    public const string THD_Uc = "THD_Uc";
    /// <summary>
    /// The BrowseName for the Ua component.
    /// </summary>
    public const string Ua = "Ua";
    /// <summary>
    /// The BrowseName for the Uab component.
    /// </summary>
    public const string Uab = "Uab";
    /// <summary>
    /// The BrowseName for the Ub component.
    /// </summary>
    public const string Ub = "Ub";
    /// <summary>
    /// The BrowseName for the Ubc component.
    /// </summary>
    public const string Ubc = "Ubc";
    /// <summary>
    /// The BrowseName for the Uc component.
    /// </summary>
    public const string Uc = "Uc";
    /// <summary>
    /// The BrowseName for the Uca component.
    /// </summary>
    public const string Uca = "Uca";
    /// <summary>
    /// The BrowseName for the http://sentron.org/Panel/ component.
    /// </summary>
    public const string http___sentron_org_Panel_ = "http://sentron.org/Panel/";
}
#endregion

#region Namespace Declarations
/// <summary>
/// Defines constants for all namespaces referenced by the Model.
/// </summary>
public static partial class Namespaces
{
    /// <summary>
    /// The URI for the OpcUa namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUa = "http://opcfoundation.org/UA/";

    /// <summary>
    /// The URI for the OpcUaXsd namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUaXsd = "http://opcfoundation.org/UA/2008/02/Types.xsd";

    /// <summary>
    /// The URI for the Panel namespace.
    /// </summary>
    public const string Panel = "http://sentron.org/Panel/";

    /// <summary>
    /// The URI for the PanelXsd namespace.
    /// </summary>
    public const string PanelXsd = "http://sentron.org/Panel/Types.xsd";
}
#endregion

