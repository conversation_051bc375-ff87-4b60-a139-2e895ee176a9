﻿<?xml version="1.0" encoding="utf-8"?>
<UANodeSet xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:uax="http://opcfoundation.org/UA/2008/02/Types.xsd" xmlns="http://opcfoundation.org/UA/2011/03/UANodeSet.xsd" xmlns:s1="http://sentron.org/Substation/Types.xsd" xmlns:ua="http://unifiedautomation.com/Configuration/NodeSet.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <NamespaceUris>
    <Uri>http://sentron.org/Substation/</Uri>
  </NamespaceUris>
  <Models>
    <Model ModelUri="http://sentron.org/Substation/" PublicationDate="2023-08-10T07:55:42Z" Version="1.0.0">
      <RequiredModel ModelUri="http://opcfoundation.org/UA/" PublicationDate="2022-01-24T00:00:00Z" Version="1.05.01" />
    </Model>
  </Models>
  <Aliases>
    <Alias <PERSON>="Boolean">i=1</Alias>
    <Alias Alias="String">i=12</Alias>
    <Alias Alias="DateTime">i=13</Alias>
    <Alias Alias="HasModellingRule">i=37</Alias>
    <Alias Alias="HasTypeDefinition">i=40</Alias>
    <Alias Alias="HasSubtype">i=45</Alias>
    <Alias Alias="HasProperty">i=46</Alias>
    <Alias Alias="HasComponent">i=47</Alias>
    <Alias Alias="IdType">i=256</Alias>
    <Alias Alias="NumericRange">i=291</Alias>
  </Aliases>
  <Extensions>
    <Extension>
      <ua:ModelInfo Tool="UaModeler" Hash="3A3qdZt8fb25obI/oZi1Jw==" Version="1.6.7" />
    </Extension>
  </Extensions>
  <UAObjectType NodeId="ns=1;i=1003" BrowseName="1:Substation">
    <DisplayName>Substation</DisplayName>
    <References>
      <Reference ReferenceType="HasComponent">ns=1;i=6100</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6101</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6102</Reference>
      <Reference ReferenceType="HasSubtype" IsForward="false">i=58</Reference>
    </References>
  </UAObjectType>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6100" BrowseName="1:Total_P" AccessLevel="3">
    <DisplayName>Total_P</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6101" BrowseName="1:Total_Q" AccessLevel="3">
    <DisplayName>Total_Q</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6102" BrowseName="1:Total_S" AccessLevel="3">
    <DisplayName>Total_S</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAObject SymbolicName="http___sentron_org_Substation_" NodeId="ns=1;i=5001" BrowseName="1:http://sentron.org/Substation/">
    <DisplayName>http://sentron.org/Substation/</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=11616</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">i=11715</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6002</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6003</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6004</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6005</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6006</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6007</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6008</Reference>
    </References>
  </UAObject>
  <UAVariable DataType="Boolean" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6002" BrowseName="IsNamespaceSubset">
    <DisplayName>IsNamespaceSubset</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
    <Value>
      <uax:Boolean>false</uax:Boolean>
    </Value>
  </UAVariable>
  <UAVariable DataType="DateTime" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6003" BrowseName="NamespacePublicationDate">
    <DisplayName>NamespacePublicationDate</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
    <Value>
      <uax:DateTime>2023-08-10T07:55:42Z</uax:DateTime>
    </Value>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6004" BrowseName="NamespaceUri">
    <DisplayName>NamespaceUri</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
    <Value>
      <uax:String>http://sentron.org/Substation/</uax:String>
    </Value>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6005" BrowseName="NamespaceVersion">
    <DisplayName>NamespaceVersion</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
    <Value>
      <uax:String>1.0.0</uax:String>
    </Value>
  </UAVariable>
  <UAVariable DataType="IdType" ParentNodeId="ns=1;i=5001" ValueRank="1" NodeId="ns=1;i=6006" ArrayDimensions="0" BrowseName="StaticNodeIdTypes">
    <DisplayName>StaticNodeIdTypes</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="NumericRange" ParentNodeId="ns=1;i=5001" ValueRank="1" NodeId="ns=1;i=6007" ArrayDimensions="0" BrowseName="StaticNumericNodeIdRange">
    <DisplayName>StaticNumericNodeIdRange</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6008" BrowseName="StaticStringNodeIdPattern">
    <DisplayName>StaticStringNodeIdPattern</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
  </UAVariable>
</UANodeSet>