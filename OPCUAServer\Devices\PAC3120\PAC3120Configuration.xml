﻿<?xml version="1.0" encoding="utf-8"?>
<PAC3120.Configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://sentron.com/PAC3120">
  <Controllers>
    <Name>PAC3120</Name>
    <Type>1</Type>
    <Properties>
      <Name>IsConnected</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ua</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ub</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Uc</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Uab</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ubc</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Uca</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ia</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ib</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ic</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>In</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>P</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Q</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>S</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>PowFactor_A</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>PowFactor_B</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>PowFactor_C</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>F</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ua</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ub</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Uc</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ia</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ib</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ic</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ForwardActivePower</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ForwardReactivePower</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ReverseActivePower</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ReverseReactivePower</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ForwardActivePower_Tariff1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ForwardReactivePower_Tariff1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ReverseActivePower_Tariff1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ReverseReactivePower_Tariff1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ForwardActivePower_Tariff2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ForwardReactivePower_Tariff2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ReverseActivePower_Tariff2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ReverseReactivePower_Tariff2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>DO_0.0</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>DO_0.1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>DI_0.0</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>DI_0.1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>LimitMonitoring_0</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>LimitMonitoring_1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>LimitMonitoring_2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>LimitMonitoring_3</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>LimitMonitoring_4</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>LimitMonitoring_5</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>OperatingHours</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ActualTariff</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>HarwareWriteProtectionStatus</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>UseVoltageTransformer</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>PrimaryVoltage</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>SecondaryVoltage</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>PrimaryCurrent</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>SecondaryCurrent</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
  </Controllers>
</PAC3120.Configuration>