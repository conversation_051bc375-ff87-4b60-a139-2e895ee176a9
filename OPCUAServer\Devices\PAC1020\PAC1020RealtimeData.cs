﻿using System.Collections.Concurrent;
using PanelHttp;

namespace HTTPInterface;

internal class PAC1020RealtimeData
{
    private const string BLANK = "-99999";
    private ConcurrentDictionary<string, string> _dictPAC1020 = new();

    public void PAC1020RealtimeInit(string itemId)
    {
        this._dictPAC1020 = ReadPanelHttp.InitializeDeviceChannels(itemId);
    }

    public void PAC1020RealtimeUpdate(string itemId)
    {
        ReadPanelHttp.UpdateDeviceChannels(itemId, this._dictPAC1020);
    }

    public string GetChannelValue(string channelName)
    {
        if (string.IsNullOrEmpty(channelName))
        {
            return BLANK;
        }

        return this._dictPAC1020.TryGetValue(channelName, out var value)
            ? value ?? BLANK
            : BLANK;
    }
}
