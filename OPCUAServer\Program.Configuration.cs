using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaSchema;

// [Configuration Options 1]
namespace OPCUAServer;

partial class OpcUAServer
{
    // [Configuration Options 2]
    static void ConfigureOpcUaApplicationFromCode()
    {
        // fill in the application settings in code
        // The configuration settings are typically provided by another module
        // of the application or loaded from a data base. In this example the
        // settings are hardcoded
        var configuration = new ConfigurationInMemory();

        string enviromentPath = "%CommonApplicationData%";

        // override path for net core on non windows platform
        if (!PlatformUtils.IsWindows())
        {
            enviromentPath = "%LocalApplicationData%";
        }

        // ***********************************************************************
        // standard configuration options

        // general application identification settings
        configuration.ApplicationName = "Sentron OPCUAServer";
        configuration.ApplicationUri = "urn:localhost:Sentron:OPCUAServer";
        configuration.ApplicationType = UnifiedAutomation.UaSchema.ApplicationType.Server_0;
        configuration.ProductName = "Sentron OPCUAServer";

        // configure application certificate and paths to the certificate stores
        // configuration.SetSecurity(PlatformUtils.CombinePath(enviromentPath, "unifiedautomation", "UaSdkNetBundleBinary", "pkiserver"), "CN=OPCUAServer/O=Siemens/DC=localhost");
        var pkiRoot = PlatformUtils.CombinePath(enviromentPath, "unifiedautomation", "UaSdkNetBundleBinary", "pkiserver");

        configuration.ApplicationCertificate = new CertificateIdentifier()
        {
            StoreType = "Directory",
            StorePath = PlatformUtils.CombinePath(pkiRoot, "own"),
            // SubjectName = "CN=OPCUAServer/O=Siemens/DC=localhost"
            SubjectName = "CN=GettingStartedServer/O=UnifiedAutomation/DC=localhost"
        };
        configuration.TrustedCertificateStore = new CertificateStoreIdentifier()
        {
            StoreType = "Directory",
            StorePath = PlatformUtils.CombinePath(pkiRoot, "trusted")
        };
        configuration.IssuerCertificateStore = new CertificateStoreIdentifier()
        {
            StoreType = "Directory",
            StorePath = PlatformUtils.CombinePath(pkiRoot, "issuers")
        };
        configuration.RejectedCertificatesStore = new CertificateStoreIdentifier()
        {
            StoreType = "Directory",
            StorePath = PlatformUtils.CombinePath(pkiRoot, "rejected")
        };

        // configure endpoints
        configuration.BaseAddresses = new UnifiedAutomation.UaSchema.ListOfBaseAddresses() { "opc.tcp://localhost:48030" };

        // SecurityProfiles
        configuration.SecurityProfiles = new ListOfSecurityProfiles
        {
            new SecurityProfile() { ProfileUri = SecurityProfiles.Basic256Sha256, Enabled = true },
            new SecurityProfile() { ProfileUri = SecurityProfiles.Aes128Sha256RsaOaep, Enabled = true },
            new SecurityProfile() { ProfileUri = SecurityProfiles.Aes256Sha256RsaPss, Enabled = true },

            // This SecurityProfile is enabled for testing purposes.
            new SecurityProfile() { ProfileUri = SecurityProfiles.None, Enabled = true }
        };

        var endpointSettings = new EndpointSettings();
        var endpointConfig = new UnifiedAutomation.UaSchema.EndpointConfiguration()
        {
            EndpointUrl = "opc.tcp://localhost:48030", // Must match a BaseAddress
            EnableSignOnly = true
        };
        endpointSettings.Endpoint = new UnifiedAutomation.UaSchema.EndpointConfiguration[] { endpointConfig };
        configuration.Set<EndpointSettings>(endpointSettings);

        // ***********************************************************************
        // extended configuration options
        // configuration.SecurityProfiles.Add(new UnifiedAutomation.UaSchema.SecurityMode(MessageSecurityMode.None) );
        // trace settings
        TraceSettings trace = new TraceSettings();
        trace.MasterTraceEnabled = true;
        trace.DefaultTraceLevel = UnifiedAutomation.UaSchema.TraceLevel.Info;
        trace.TraceFile = PlatformUtils.CombinePath(enviromentPath, "Sentron", "Logs", FilePathUtils.MakeValidFileName(configuration.ApplicationName) + ".log.txt");
        trace.MaxLogFileBackups = 3;

        trace.ModuleSettings = new ModuleTraceSettings[]
        {
            new() { ModuleName = "Sentron.Stack", TraceEnabled = true },
            new() { ModuleName = "Sentron.Server", TraceEnabled = true },
        };

        configuration.Set<TraceSettings>(trace);

        // Installation settings
        InstallationSettings installation = new InstallationSettings();
        installation.GenerateCertificateIfNone = true;
        installation.DeleteCertificateOnUninstall = true;

        configuration.Set<InstallationSettings>(installation);

        configuration.ServerSettings = new UnifiedAutomation.UaSchema.ServerSettings()
        {
            ProductName = "Sentron OPCUAServer",
            DiscoveryRegistration = new DiscoveryRegistrationSettings()
            {
                Enabled = true
            }
        };

        // ***********************************************************************
        // set the configuration for the application (must be called before start to have any effect).
        // these settings are discarded if the /configFile flag is specified on the command line.
        ApplicationInstanceBase.Default.SetApplicationSettings(configuration);
    }

    // [Configuration Options 2]

    /// <summary>
    /// Disables the SecurityProfiles.Aes256Sha256RsaPss if the SecurityProvider does not support it.
    /// </summary>
    /// <remarks>
    /// The SecurityProfiles.Aes256Sha256RsaPss is supported in UnifiedAutomation assemblies built
    /// with .NET Framework 4.8 and .NET standard.
    /// </remarks>
    private static void Application_ApplicationSettingsLoaded(object sender, EventArgs e)
    {
        ApplicationInstanceBase application = sender as ApplicationInstanceBase;
        ISecurityProvider securityProvider = application.SecurityProvider;
        try
        {
            using (var cryptoProvider = securityProvider.CreateCryptoProvider(
                new CryptoProviderSettings()
                {
                    SecurityProfileUri = SecurityProfiles.Aes256Sha256RsaPss
                }))
            {
            }
        }
        catch (Exception)
        {
            application.ApplicationSettings.SecurityProfiles[2].Enabled = true;
        }
    }

    static void SetUserIdentityToServerSettings(ApplicationInstanceBase application)
    {
        // var configuration = new ConfigurationInMemory()
        // {
        //     ServerSettings = new UnifiedAutomation.UaSchema.ServerSettings()
        //     {
        //         ProductName = "Sentron OPCUAServer",
        //         DiscoveryRegistration = new DiscoveryRegistrationSettings()
        //         {
        //             Enabled = true
        //         }
        //     }
        // };
        // application.SetApplicationSettings(configuration);
        // var applicationSettings = application.ApplicationSettings;
        application.ApplicationSettings.ServerSettings.UserIdentity = new UserIdentitySettings()
        {
            EnableAnonymous = true,
            EnableUserName = true,
            // EnableCertificate = true,
            UserTrustedCertificateStore = "%CommonApplicationData%\\unifiedautomation\\UaSdkNetBundleBinary\\pkiserver\\trusted",
            UserIssuerCertificateStore = "%CommonApplicationData%\\unifiedautomation\\UaSdkNetBundleBinary\\pkiserver\\issuers",
            UserRejectedCertificateStore = "%CommonApplicationData%\\unifiedautomation\\UaSdkNetBundleBinary\\pkiserver\\rejected"
        };
    }
}
