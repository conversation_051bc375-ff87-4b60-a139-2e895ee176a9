using RolePermissionBasedAccess;
using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaServer;

namespace OPCUAServer;

/// <summary>
/// The security manager for the Panel OPC Server.
/// </summary>
internal partial class OPCServerManager
{
    public event EventHandler<CertificateAddedEventArgs> CertificateAdded
    {
        add
        {
            this.CertificateCalled += value;
        }

        remove
        {
            this.CertificateCalled -= value;
        }
    }

    private event EventHandler<CertificateAddedEventArgs>? CertificateCalled;

    /// <summary>
    /// A dummy implementation that returns an empty set and continuation point.
    /// </summary>
    /// <param name="context">The request context.</param>
    /// <param name="continuationPoint">The continuation point.</param>
    /// <param name="releaseContinuationPoint">Whether to release the continuation point.</param>
    /// <param name="callback">The callback to call when the query is complete.</param>
    /// <param name="callbackData">The callback data.</param>
    /// <returns>StatusCode</returns>
    public StatusCode BeginQuery(RequestContext context, QueryContinuationPoint continuationPoint, bool releaseContinuationPoint, QueryCompletedEventHandler callback, object callbackData)
    {
        var results = new ParsingResultCollection();
        bool errorsExist = false;

        for (int ii = 0; ii < continuationPoint.NodeTypes.Count; ii++)
        {
            var result = new ParsingResult();

            for (int jj = 0; jj < continuationPoint.NodeTypes[ii].DataToReturn.Count; jj++)
            {
                if (continuationPoint.NodeTypes[ii].DataToReturn[jj].AttributeId < 1 || continuationPoint.NodeTypes[ii].DataToReturn[jj].AttributeId > Attributes.Last)
                {
                    result.StatusCode = StatusCodes.BadAttributeIdInvalid;
                    result.DataStatusCodes.Add(StatusCodes.BadAttributeIdInvalid);
                    errorsExist = true;
                }
            }

            results.Add(result);
        }

        ((QueryCompletedEventHandler)callback)(
            continuationPoint.QueryHandle,
            callbackData,
            new QueryDataSetCollection(),
            releaseContinuationPoint ? null : continuationPoint,
            errorsExist ? results : new ParsingResultCollection(),
            new ContentFilterResult(),
            false);

        return StatusCodes.Good;
    }

    public override void OnTrustListAddCertificate(object sender, CertificateAddedEventArgs e)
    {
        base.OnTrustListAddCertificate(sender, e);
        if (e.StatusCode.IsGood())
        {
            if (this.CertificateCalled != null)
            {
                this.CertificateCalled(this, e);
            }
        }
    }

    protected override UnifiedAutomation.UaSchema.RoleConfigurations LoadRoleConfigurations()
    {
        var userDatabase = JsonUserDatabase.GetInstance();

        var roles = userDatabase.GetRoleConfigurations();

        var roleConfig = new UnifiedAutomation.UaSchema.RoleConfigurations()
        {
            NamespaceTable = new string[]
            {
                "http://opcfoundation.org/UA/"
            },

            Roles = roles.ToArray()
        };

        return roleConfig;
    }

    /// <summary>
    /// Returns a handle for the query.
    /// </summary>
    /// <param name="context">The request context.</param>
    /// <param name="viewHandle">The view handle.</param>
    /// <param name="handle">The query handle.</param>
    /// <returns>StatusCode</returns>
    protected override StatusCode GetQueryHandle(RequestContext context, ViewHandle viewHandle, out QueryHandle handle)
    {
        handle = new QueryHandle(this, this.RootNodeManager.CoreNodeManager, this.RootNodeManager.CoreNodeManager);
        return StatusCodes.Good;
    }

    private void SessionManager_ImpersonateUser(Session session, ImpersonateEventArgs args)
    {
        switch (args.NewIdentity)
        {
            case null:
            case AnonymousIdentityToken anonymousToken:
                return;
            case UserNameIdentityToken userNameToken when string.IsNullOrEmpty(userNameToken.UserName):
                args.IdentityValidationError = StatusCodes.BadIdentityTokenInvalid;
                return;
            case UserNameIdentityToken userNameToken:
                var userDatabase = JsonUserDatabase.GetInstance();

                if (!userDatabase.CheckCredentials(userNameToken.UserName, userNameToken.DecryptedPassword))
                {
                    args.IdentityValidationError = StatusCodes.BadUserAccessDenied;
                }

                return;

            case X509IdentityToken certificateToken:
                try
                {
                    this.UserCertificateValidator.Validate(certificateToken.Certificate);
                }
                catch (Exception)
                {
                    args.IdentityValidationError = StatusCodes.BadIdentityTokenRejected;
                }

                break;
            default:
                args.IdentityValidationError = StatusCodes.BadIdentityTokenRejected;
                break;
        }
    }
}
