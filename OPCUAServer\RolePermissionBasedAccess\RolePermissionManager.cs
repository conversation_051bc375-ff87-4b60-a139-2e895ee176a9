using UnifiedAutomation.UaBase;

namespace RolePermissionBasedAccess;

public static class RolePermissionManager
{
    public static readonly RolePermissionTypeCollection RolePermissionTypeCollection = new()
    {
        new RolePermissionType()
        {
            RoleId = ObjectIds.WellKnownRole_Anonymous,
            Permissions = PermissionTypeDataType.Browse | PermissionTypeDataType.ReadRolePermissions
        },
        new RolePermissionType()
        {
            RoleId = ObjectIds.WellKnownRole_Observer,
            Permissions = PermissionTypeDataType.Browse | PermissionTypeDataType.ReadRolePermissions | PermissionTypeDataType.Read
        },
        new RolePermissionType()
        {
            RoleId = ObjectIds.WellKnownRole_Operator,
            Permissions = PermissionTypeDataType.Browse | PermissionTypeDataType.ReadRolePermissions | PermissionTypeDataType.Read | PermissionTypeDataType.Write
        },
        new RolePermissionType()
        {
            RoleId = ObjectIds.WellKnownRole_ConfigureAdmin,
            Permissions = PermissionTypeDataType.Browse | PermissionTypeDataType.ReadRolePermissions | PermissionTypeDataType.Read | PermissionTypeDataType.Write
        }
    };
}