using UnifiedAutomation.UaBase;

namespace Sentron.PanelAlarm;

#region DataType Identifiers
/// <summary>
/// A class that declares constants for all DataTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypes
{
}
#endregion

#region Object Identifiers
/// <summary>
/// A class that declares constants for all Objects in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Objects
{
    /// <summary>
    /// The identifier for the http://yourorganisation.org/PanelAlarm/ Object.
    /// </summary>
    public const uint Namespaces_http___yourorganisation_org_PanelAlarm_ = 5001;

}
#endregion

#region ObjectType Identifiers
/// <summary>
/// A class that declares constants for all ObjectTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypes
{
    /// <summary>
    /// The identifier for the PanelAlarm ObjectType.
    /// </summary>
    public const uint PanelAlarm = 1003;

}
#endregion

#region Method Identifiers
/// <summary>
/// A class that declares constants for all Methods in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Methods
{
}
#endregion

#region ReferenceType Identifiers
/// <summary>
/// A class that declares constants for all ReferenceTyped in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypes
{
}
#endregion

#region Variable Identifiers
/// <summary>
/// A class that declares constants for all Variables in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Variables
{
    /// <summary>
    /// The identifier for the IsNamespaceSubset Object.
    /// </summary>
    public const uint Namespaces_http___yourorganisation_org_PanelAlarm__IsNamespaceSubset = 6001;

    /// <summary>
    /// The identifier for the NamespacePublicationDate Object.
    /// </summary>
    public const uint Namespaces_http___yourorganisation_org_PanelAlarm__NamespacePublicationDate = 6002;

    /// <summary>
    /// The identifier for the NamespaceUri Object.
    /// </summary>
    public const uint Namespaces_http___yourorganisation_org_PanelAlarm__NamespaceUri = 6003;

    /// <summary>
    /// The identifier for the NamespaceVersion Object.
    /// </summary>
    public const uint Namespaces_http___yourorganisation_org_PanelAlarm__NamespaceVersion = 6004;

    /// <summary>
    /// The identifier for the StaticNodeIdTypes Object.
    /// </summary>
    public const uint Namespaces_http___yourorganisation_org_PanelAlarm__StaticNodeIdTypes = 6005;

    /// <summary>
    /// The identifier for the StaticNumericNodeIdRange Object.
    /// </summary>
    public const uint Namespaces_http___yourorganisation_org_PanelAlarm__StaticNumericNodeIdRange = 6006;

    /// <summary>
    /// The identifier for the StaticStringNodeIdPattern Object.
    /// </summary>
    public const uint Namespaces_http___yourorganisation_org_PanelAlarm__StaticStringNodeIdPattern = 6007;

}
#endregion

#region VariableTypes Identifiers
/// <summary>
/// A class that declares constants for all VariableTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypes
{
}
#endregion

#region DataType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all DataTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypeIds
{
}
#endregion

#region Method Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Methods in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class MethodIds
{
}
#endregion

#region Object Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectIds
{
    /// <summary>
    /// The identifier for the Namespaces_http___yourorganisation_org_PanelAlarm_ Object.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___yourorganisation_org_PanelAlarm_ = new ExpandedNodeId(Objects.Namespaces_http___yourorganisation_org_PanelAlarm_, Namespaces.PanelAlarm);

}
#endregion

#region ObjectType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypeIds
{
    /// <summary>
    /// The identifier for the PanelAlarm ObjectType.
    /// </summary>
    public static readonly ExpandedNodeId PanelAlarm = new ExpandedNodeId(ObjectTypes.PanelAlarm, Namespaces.PanelAlarm);

}
#endregion

#region ReferenceType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all ReferenceTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypeIds
{
}
#endregion

#region Variable Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Variables in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableIds
{
    /// <summary>
    /// The identifier for the Namespaces_http___yourorganisation_org_PanelAlarm__IsNamespaceSubset Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___yourorganisation_org_PanelAlarm__IsNamespaceSubset = new ExpandedNodeId(Variables.Namespaces_http___yourorganisation_org_PanelAlarm__IsNamespaceSubset, Namespaces.PanelAlarm);

    /// <summary>
    /// The identifier for the Namespaces_http___yourorganisation_org_PanelAlarm__NamespacePublicationDate Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___yourorganisation_org_PanelAlarm__NamespacePublicationDate = new ExpandedNodeId(Variables.Namespaces_http___yourorganisation_org_PanelAlarm__NamespacePublicationDate, Namespaces.PanelAlarm);

    /// <summary>
    /// The identifier for the Namespaces_http___yourorganisation_org_PanelAlarm__NamespaceUri Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___yourorganisation_org_PanelAlarm__NamespaceUri = new ExpandedNodeId(Variables.Namespaces_http___yourorganisation_org_PanelAlarm__NamespaceUri, Namespaces.PanelAlarm);

    /// <summary>
    /// The identifier for the Namespaces_http___yourorganisation_org_PanelAlarm__NamespaceVersion Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___yourorganisation_org_PanelAlarm__NamespaceVersion = new ExpandedNodeId(Variables.Namespaces_http___yourorganisation_org_PanelAlarm__NamespaceVersion, Namespaces.PanelAlarm);

    /// <summary>
    /// The identifier for the Namespaces_http___yourorganisation_org_PanelAlarm__StaticNodeIdTypes Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___yourorganisation_org_PanelAlarm__StaticNodeIdTypes = new ExpandedNodeId(Variables.Namespaces_http___yourorganisation_org_PanelAlarm__StaticNodeIdTypes, Namespaces.PanelAlarm);

    /// <summary>
    /// The identifier for the Namespaces_http___yourorganisation_org_PanelAlarm__StaticNumericNodeIdRange Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___yourorganisation_org_PanelAlarm__StaticNumericNodeIdRange = new ExpandedNodeId(Variables.Namespaces_http___yourorganisation_org_PanelAlarm__StaticNumericNodeIdRange, Namespaces.PanelAlarm);

    /// <summary>
    /// The identifier for the Namespaces_http___yourorganisation_org_PanelAlarm__StaticStringNodeIdPattern Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___yourorganisation_org_PanelAlarm__StaticStringNodeIdPattern = new ExpandedNodeId(Variables.Namespaces_http___yourorganisation_org_PanelAlarm__StaticStringNodeIdPattern, Namespaces.PanelAlarm);

}
#endregion

#region VariableType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all VariableType in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypeIds
{
}
#endregion

#region BrowseName Declarations
/// <summary>
/// Declares all of the BrowseNames used in the Model.
/// </summary>
public static partial class BrowseNames
{
    /// <summary>
    /// The BrowseName for the PanelAlarm component.
    /// </summary>
    public const string PanelAlarm = "PanelAlarm";
    /// <summary>
    /// The BrowseName for the Start component.
    /// </summary>
    public const string Start = "Start";
    /// <summary>
    /// The BrowseName for the State component.
    /// </summary>
    public const string State = "State";
    /// <summary>
    /// The BrowseName for the StateCondition component.
    /// </summary>
    public const string StateCondition = "StateCondition";
    /// <summary>
    /// The BrowseName for the Stop component.
    /// </summary>
    public const string Stop = "Stop";
    /// <summary>
    /// The BrowseName for the http://yourorganisation.org/PanelAlarm/ component.
    /// </summary>
    public const string httpYourorganisationOrgPanelAlarm = "http://yourorganisation.org/PanelAlarm/";
}
#endregion

#region Namespace Declarations
/// <summary>
/// Defines constants for all namespaces referenced by the Model.
/// </summary>
public static partial class Namespaces
{
    /// <summary>
    /// The URI for the OpcUa namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUa = "http://opcfoundation.org/UA/";

    /// <summary>
    /// The URI for the OpcUaXsd namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUaXsd = "http://opcfoundation.org/UA/2008/02/Types.xsd";

    /// <summary>
    /// The URI for the PanelAlarm namespace.
    /// </summary>
    public const string PanelAlarm = "http://sentron.org/PanelAlarm/";

    /// <summary>
    /// The URI for the PanelAlarmXsd namespace.
    /// </summary>
    public const string PanelAlarmXsd = "http://yourorganisation.org/PanelAlarm/Types.xsd";
}
#endregion

