﻿using System.Reflection;
using OPCUAServer.Common;
using RolePermissionBasedAccess;
using Serilog;
using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaServer;

namespace OPCUAServer.Devices.Substation;

/// <summary>
/// SubstationNodeManager is a class that manages the Substation system.
/// </summary>
internal partial class SubstationNodeManager : BaseNodeManager
{
    private static readonly string SubstationTemplateFileName = "Substation_Modified.xml";
    private static Dictionary<string, SubstationSystem> _dictSubstationSystem = new();
    private readonly ILogger _logger = LoggingHelper.GetLogger<SubstationNodeManager>();

    public ushort InstanceNamespaceIndex { get; private set; }

    public ushort TypeNamespaceIndex { get; private set; }

    public string SubstationItemId { get; set; }

    public string SubstationName { get; set; } = "Substation";

    /// <summary>
    /// Initializes a new instance of the <see cref="SubstationNodeManager"/> class.
    /// </summary>
    /// <param name="server">The server manager.</param>
    public SubstationNodeManager(ServerManager server) 
        : base(server)
    {
        // Namespace initialization moved to Startup() method
    }

    public static void InitialSubstationSystem(int pacNumber, List<string> itemid)
    {
        _dictSubstationSystem = itemid.Take(pacNumber)
                                      .ToDictionary(id => id, id => new SubstationSystem());
    }

    /// <summary>
    /// Called when the node manager is started.
    /// </summary>
    public override void Startup()
    {
        try
        {
            this._logger.Information("Starting SubstationNodeManager.");
            base.Startup();

            // Initialize namespaces
            this.TypeNamespaceIndex = this.AddNamespaceUri(Sentron.Substation.Namespaces.Substation);
            this.InstanceNamespaceIndex = this.AddNamespaceUri("http://sentron.org/Substation/" + this.SubstationName);

            // load the model.
            this._logger.Information($"Loading the Substation Model: {this.SubstationName}.");
            this.ImportUaNodeset(Assembly.GetEntryAssembly(), SubstationTemplateFileName);

            _dictSubstationSystem[this.SubstationItemId].Initialize(this.SubstationItemId);

            try
            {
                var settings = new CreateObjectSettings()
                {
                    ParentNodeId = ObjectIds.ObjectsFolder,
                    ReferenceTypeId = ReferenceTypeIds.Organizes,
                    RequestedNodeId = new NodeId(this.SubstationName, this.InstanceNamespaceIndex),
                    BrowseName = new QualifiedName(this.SubstationName, this.InstanceNamespaceIndex),
                    TypeDefinitionId = ObjectTypeIds.FolderType
                };
                this.CreateObject(this.Server.DefaultRequestContext, settings);
            }
            catch (Exception ex)
            {
                this._logger.Error($"Failed to create SubstationNodeManager: {ex.Message}.");
            }

            // Create controllers from configuration
            foreach (BlockConfiguration block in _dictSubstationSystem[this.SubstationItemId].GetBlocks())
            {
                // set type definition NodeId
                NodeId typeDefinitionId = ObjectTypeIds.BaseObjectType;
                if (block.Type == BlockType.Substation)
                {
                    typeDefinitionId = new NodeId(Sentron.Substation.ObjectTypes.Substation, this.TypeNamespaceIndex);
                }

                // create object.
                var settings = new CreateObjectSettings()
                {
                    ParentNodeId = new NodeId(this.SubstationName, this.InstanceNamespaceIndex),
                    ReferenceTypeId = ReferenceTypeIds.Organizes,
                    RequestedNodeId = new NodeId(block.Name, this.InstanceNamespaceIndex),
                    BrowseName = new QualifiedName(block.Name, this.TypeNamespaceIndex),
                    TypeDefinitionId = typeDefinitionId
                };

                this.CreateObject(this.Server.DefaultRequestContext, settings);

                foreach (BlockProperty property in block.Properties)
                {
                    // the node was already created when the controller object was instantiated.
                    // this call links the node to the underlying system data.
                    VariableNode variable = this.SetVariableConfiguration(
                        new NodeId(block.Name, this.InstanceNamespaceIndex),
                        new QualifiedName(property.Name, this.TypeNamespaceIndex),
                        NodeHandleType.ExternalPolled,
                        new SystemAddress() { Address = block.Address, Offset = property.Offset });

                    if (variable != null)
                    {
                        lock (this.InMemoryNodeLock)
                        {
                            variable.AccessLevel = property.Writeable ? AccessLevels.CurrentReadOrWrite : AccessLevels.CurrentRead;
                        }
                    }

                    this.SetNodePermissions(
                        new NodeId(block.Name, this.InstanceNamespaceIndex),
                        new QualifiedName(property.Name, this.TypeNamespaceIndex),
                        RolePermissionManager.RolePermissionTypeCollection);
                }
            }
        }
        catch (Exception e)
        {
            this._logger.Error($"Failed to start Substation NodeManager: {e.Message}.");
        }
    }

    /// <summary>
    /// Called when the node manager is stopped.
    /// </summary>
    public override void Shutdown()
    {
        try
        {
            this._logger.Information("Stopping SubstationNodeManager.");
            base.Shutdown();
        }
        catch (Exception e)
        {
            this._logger.Error($"Failed to stop Substation NodeManager: {e.Message}.");
        }
    }

    /// <summary>
    /// Releases unmanaged and - optionally - managed resources.
    /// Disposes the Substation system instance associated with this node manager.
    /// </summary>
    /// <param name="disposing">
    /// <c>true</c> to release both managed and unmanaged resources;
    /// <c>false</c> to release only unmanaged resources.
    /// </param>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _dictSubstationSystem[this.SubstationItemId]?.Dispose();
        }
    }

    /// <summary>
    /// Reads the attributes.
    /// </summary>
    /// <param name="context">The context.</param>
    /// <param name="transaction">The transaction.</param>
    /// <param name="operationHandles">The operation handles.</param>
    /// <param name="settings">The settings.</param>
    protected override void Read(
        RequestContext context,
        TransactionHandle transaction,
        IList<NodeAttributeOperationHandle> operationHandles,
        IList<ReadValueId> settings)
    {
        for (int ii = 0; ii < operationHandles.Count; ii++)
        {
            DataValue? dv = null;

            if (operationHandles[ii].NodeHandle.UserData is SystemAddress address)
            {
                if (this.CannotPassNodeAccessChecks(context, operationHandles[ii].NodeHandle, UserAccessMask.Read, out StatusCode statusCode))
                {
                    dv = new DataValue(statusCode);
                }
                else
                {
                    object? value = _dictSubstationSystem[this.SubstationItemId].Read(address.Address, address.Offset);
                    if (value is not null)
                    {
                        dv = new DataValue(new Variant(value, null), DateTime.UtcNow);

                        if (!string.IsNullOrEmpty(settings[ii].IndexRange) || !QualifiedName.IsNull(settings[ii].DataEncoding))
                        {
                            dv = this.ApplyIndexRangeAndEncoding(
                                operationHandles[ii].NodeHandle,
                                dv,
                                settings[ii].IndexRange,
                                settings[ii].DataEncoding);
                        }
                    }
                }
            }

            // set an error if not found.
            dv ??= new DataValue(new StatusCode(StatusCodes.BadNodeIdUnknown));

            ((ReadCompleteEventHandler)transaction.Callback)(
                operationHandles[ii],
                transaction.CallbackData,
                dv,
                true);
        }
    }

    /// <summary>
    /// Write the attributes
    /// </summary>
    /// <param name="context">The context.</param>
    /// <param name="transaction">The transaction.</param>
    /// <param name="operationHandles">The operation handles.</param>
    /// <param name="settings">The settings.</param>
    protected override void Write(
        RequestContext context,
        TransactionHandle transaction,
        IList<NodeAttributeOperationHandle> operationHandles,
        IList<WriteValue> settings)
    {
        for (int ii = 0; ii < operationHandles.Count; ii++)
        {
            StatusCode error = StatusCodes.BadNodeIdUnknown;

            if (operationHandles[ii].NodeHandle.UserData is SystemAddress address)
            {
                if (!string.IsNullOrEmpty(settings[ii].IndexRange))
                {
                    error = StatusCodes.BadIndexRangeInvalid;
                }
                else if (this.CannotPassNodeAccessChecks(context, operationHandles[ii].NodeHandle, UserAccessMask.Write, out StatusCode statusCode))
                {
                    error = statusCode;
                }
                else if (!_dictSubstationSystem[this.SubstationItemId].Write(address.Address, address.Offset, settings[ii].Value.Value))
                {
                    error = StatusCodes.BadUserAccessDenied;
                }
                else
                {
                    error = StatusCodes.Good;
                }
            }

            // return the data to the caller.
            ((WriteCompleteEventHandler)transaction.Callback)(
                operationHandles[ii],
                transaction.CallbackData,
                error,
                true);
        }
    }
}
