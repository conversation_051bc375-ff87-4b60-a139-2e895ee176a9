using System.ComponentModel.DataAnnotations;

namespace OPCUAServer.Utilites;

/// <summary>
/// A class that provides utility methods for validating configuration.
/// </summary>
public static class ConfigValidator
{
    public static void ValidateConfig<T>(T config)
        where T : class
    {
        var validationContext = new ValidationContext(config);
        var validationResults = new List<ValidationResult>();
        if (!Validator.TryValidateObject(config, validationContext, validationResults, true))
        {
            var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
            throw new InvalidOperationException($"Configuration validation failed: {errors}");
        }
    }
}