﻿using System.Collections.Concurrent;
using PanelHttp;

namespace HTTPInterface;

internal class PanelRealtimeData
{
    private const string BLANK = "-99999";
    private ConcurrentDictionary<string, string> _dictPanel = new();

    public void PanelRealtimeInit(string itemId)
    {
        this._dictPanel = ReadPanelHttp.InitializeDeviceChannels(itemId);
    }

    public void PanelRealtimeUpdate(string itemId)
    {
        ReadPanelHttp.UpdateDeviceChannels(itemId, this._dictPanel);
    }

    public string GetChannelValue(string channelName)
    {
        if (string.IsNullOrEmpty(channelName))
        {
            return BLANK;
        }

        return this._dictPanel.TryGetValue(channelName, out var value)
            ? value ?? BLANK
            : BLANK;
    }
}
