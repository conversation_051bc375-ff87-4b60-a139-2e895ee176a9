using UnifiedAutomation.UaBase;

namespace Sentron.ThreeWL;

#region DataType Identifiers
/// <summary>
/// A class that declares constants for all DataTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypes
{
}
#endregion

#region Object Identifiers
/// <summary>
/// A class that declares constants for all Objects in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Objects
{
    /// <summary>
    /// The identifier for the http://sentron.org/ThreeWL/ Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWL_ = 5001;

}
#endregion

#region ObjectType Identifiers
/// <summary>
/// A class that declares constants for all ObjectTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypes
{
    /// <summary>
    /// The identifier for the ThreeWL ObjectType.
    /// </summary>
    public const uint ThreeWL = 1003;

}
#endregion

#region Method Identifiers
/// <summary>
/// A class that declares constants for all Methods in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Methods
{
}
#endregion

#region ReferenceType Identifiers
/// <summary>
/// A class that declares constants for all ReferenceTyped in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypes
{
}
#endregion

#region Variable Identifiers
/// <summary>
/// A class that declares constants for all Variables in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Variables
{
    /// <summary>
    /// The identifier for the APhaseTemp1 Variable.
    /// </summary>
    public const uint ThreeWL_APhaseTemp1 = 6137;

    /// <summary>
    /// The identifier for the APhaseTemp2 Variable.
    /// </summary>
    public const uint ThreeWL_APhaseTemp2 = 6138;

    /// <summary>
    /// The identifier for the BPhaseTemp1 Variable.
    /// </summary>
    public const uint ThreeWL_BPhaseTemp1 = 6139;

    /// <summary>
    /// The identifier for the BPhaseTemp2 Variable.
    /// </summary>
    public const uint ThreeWL_BPhaseTemp2 = 6140;

    /// <summary>
    /// The identifier for the BreakerTemp Variable.
    /// </summary>
    public const uint ThreeWL_BreakerTemp = 6107;

    /// <summary>
    /// The identifier for the ComTemp Variable.
    /// </summary>
    public const uint ThreeWL_ComTemp = 6108;

    /// <summary>
    /// The identifier for the CPhaseTemp1 Variable.
    /// </summary>
    public const uint ThreeWL_CPhaseTemp1 = 6141;

    /// <summary>
    /// The identifier for the CPhaseTemp2 Variable.
    /// </summary>
    public const uint ThreeWL_CPhaseTemp2 = 6142;

    /// <summary>
    /// The identifier for the ElectricalSwitchCycles Variable.
    /// </summary>
    public const uint ThreeWL_ElectricalSwitchCycles = 6105;

    /// <summary>
    /// The identifier for the F Variable.
    /// </summary>
    public const uint ThreeWL_F = 6125;

    /// <summary>
    /// The identifier for the ForwardActivePower Variable.
    /// </summary>
    public const uint ThreeWL_ForwardActivePower = 6128;

    /// <summary>
    /// The identifier for the ForwardReactivePower Variable.
    /// </summary>
    public const uint ThreeWL_ForwardReactivePower = 6129;

    /// <summary>
    /// The identifier for the HaveAlarm Variable.
    /// </summary>
    public const uint ThreeWL_HaveAlarm = 6100;

    /// <summary>
    /// The identifier for the HealthScore Variable.
    /// </summary>
    public const uint ThreeWL_HealthScore = 6145;

    /// <summary>
    /// The identifier for the I_Avg Variable.
    /// </summary>
    public const uint ThreeWL_I_Avg = 6132;

    /// <summary>
    /// The identifier for the Ia Variable.
    /// </summary>
    public const uint ThreeWL_Ia = 6115;

    /// <summary>
    /// The identifier for the Ib Variable.
    /// </summary>
    public const uint ThreeWL_Ib = 6116;

    /// <summary>
    /// The identifier for the Ic Variable.
    /// </summary>
    public const uint ThreeWL_Ic = 6117;

    /// <summary>
    /// The identifier for the INST_II Variable.
    /// </summary>
    public const uint ThreeWL_INST_II = 6162;

    /// <summary>
    /// The identifier for the INST_ONOFF Variable.
    /// </summary>
    public const uint ThreeWL_INST_ONOFF = 6161;

    /// <summary>
    /// The identifier for the IsConnected Variable.
    /// </summary>
    public const uint ThreeWL_IsConnected = 6101;

    /// <summary>
    /// The identifier for the LT_IR Variable.
    /// </summary>
    public const uint ThreeWL_LT_IR = 6149;

    /// <summary>
    /// The identifier for the LT_ONOFF Variable.
    /// </summary>
    public const uint ThreeWL_LT_ONOFF = 6148;

    /// <summary>
    /// The identifier for the LT_PARA_CURVE Variable.
    /// </summary>
    public const uint ThreeWL_LT_PARA_CURVE = 6151;

    /// <summary>
    /// The identifier for the LT_PARA_Phase_Current Variable.
    /// </summary>
    public const uint ThreeWL_LT_PARA_Phase_Current = 6152;

    /// <summary>
    /// The identifier for the LT_TAU Variable.
    /// </summary>
    public const uint ThreeWL_LT_TAU = 6154;

    /// <summary>
    /// The identifier for the LT_THERMAL_MEM_ONOFF Variable.
    /// </summary>
    public const uint ThreeWL_LT_THERMAL_MEM_ONOFF = 6153;

    /// <summary>
    /// The identifier for the LT_TR Variable.
    /// </summary>
    public const uint ThreeWL_LT_TR = 6150;

    /// <summary>
    /// The identifier for the LTN_IN Variable.
    /// </summary>
    public const uint ThreeWL_LTN_IN = 6156;

    /// <summary>
    /// The identifier for the LTN_ONOFF Variable.
    /// </summary>
    public const uint ThreeWL_LTN_ONOFF = 6155;

    /// <summary>
    /// The identifier for the LTTrips Variable.
    /// </summary>
    public const uint ThreeWL_LTTrips = 6135;

    /// <summary>
    /// The identifier for the MainContantHealth Variable.
    /// </summary>
    public const uint ThreeWL_MainContantHealth = 6147;

    /// <summary>
    /// The identifier for the MainContantStatus Variable.
    /// </summary>
    public const uint ThreeWL_MainContantStatus = 6134;

    /// <summary>
    /// The identifier for the MechanicalSwitchCycles Variable.
    /// </summary>
    public const uint ThreeWL_MechanicalSwitchCycles = 6106;

    /// <summary>
    /// The identifier for the NPhaseTemp1 Variable.
    /// </summary>
    public const uint ThreeWL_NPhaseTemp1 = 6143;

    /// <summary>
    /// The identifier for the NPhaseTemp2 Variable.
    /// </summary>
    public const uint ThreeWL_NPhaseTemp2 = 6144;

    /// <summary>
    /// The identifier for the OperatingHours Variable.
    /// </summary>
    public const uint ThreeWL_OperatingHours = 6133;

    /// <summary>
    /// The identifier for the P Variable.
    /// </summary>
    public const uint ThreeWL_P = 6118;

    /// <summary>
    /// The identifier for the PowFactor Variable.
    /// </summary>
    public const uint ThreeWL_PowFactor = 6121;

    /// <summary>
    /// The identifier for the PowFactor_A Variable.
    /// </summary>
    public const uint ThreeWL_PowFactor_A = 6122;

    /// <summary>
    /// The identifier for the PowFactor_B Variable.
    /// </summary>
    public const uint ThreeWL_PowFactor_B = 6123;

    /// <summary>
    /// The identifier for the PowFactor_C Variable.
    /// </summary>
    public const uint ThreeWL_PowFactor_C = 6124;

    /// <summary>
    /// The identifier for the Q Variable.
    /// </summary>
    public const uint ThreeWL_Q = 6119;

    /// <summary>
    /// The identifier for the RemainingLife Variable.
    /// </summary>
    public const uint ThreeWL_RemainingLife = 6146;

    /// <summary>
    /// The identifier for the ReverseActivePower Variable.
    /// </summary>
    public const uint ThreeWL_ReverseActivePower = 6130;

    /// <summary>
    /// The identifier for the ReverseReactivePower Variable.
    /// </summary>
    public const uint ThreeWL_ReverseReactivePower = 6131;

    /// <summary>
    /// The identifier for the S Variable.
    /// </summary>
    public const uint ThreeWL_S = 6120;

    /// <summary>
    /// The identifier for the SpringCharged Variable.
    /// </summary>
    public const uint ThreeWL_SpringCharged = 6103;

    /// <summary>
    /// The identifier for the ST_I2t_ONOFF Variable.
    /// </summary>
    public const uint ThreeWL_ST_I2t_ONOFF = 6160;

    /// <summary>
    /// The identifier for the ST_ISD Variable.
    /// </summary>
    public const uint ThreeWL_ST_ISD = 6158;

    /// <summary>
    /// The identifier for the ST_ONOFF Variable.
    /// </summary>
    public const uint ThreeWL_ST_ONOFF = 6157;

    /// <summary>
    /// The identifier for the ST_TSD Variable.
    /// </summary>
    public const uint ThreeWL_ST_TSD = 6159;

    /// <summary>
    /// The identifier for the STTrips Variable.
    /// </summary>
    public const uint ThreeWL_STTrips = 6136;

    /// <summary>
    /// The identifier for the Switch Variable.
    /// </summary>
    public const uint ThreeWL_Switch = 6102;

    /// <summary>
    /// The identifier for the SwitchReady Variable.
    /// </summary>
    public const uint ThreeWL_SwitchReady = 6104;

    /// <summary>
    /// The identifier for the THD_I Variable.
    /// </summary>
    public const uint ThreeWL_THD_I = 6127;

    /// <summary>
    /// The identifier for the THD_U Variable.
    /// </summary>
    public const uint ThreeWL_THD_U = 6126;

    /// <summary>
    /// The identifier for the Ua Variable.
    /// </summary>
    public const uint ThreeWL_Ua = 6109;

    /// <summary>
    /// The identifier for the Uab Variable.
    /// </summary>
    public const uint ThreeWL_Uab = 6112;

    /// <summary>
    /// The identifier for the Ub Variable.
    /// </summary>
    public const uint ThreeWL_Ub = 6110;

    /// <summary>
    /// The identifier for the Ubc Variable.
    /// </summary>
    public const uint ThreeWL_Ubc = 6113;

    /// <summary>
    /// The identifier for the Uc Variable.
    /// </summary>
    public const uint ThreeWL_Uc = 6111;

    /// <summary>
    /// The identifier for the Uca Variable.
    /// </summary>
    public const uint ThreeWL_Uca = 6114;

    /// <summary>
    /// The identifier for the IsNamespaceSubset Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWL__IsNamespaceSubset = 6002;

    /// <summary>
    /// The identifier for the NamespacePublicationDate Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWL__NamespacePublicationDate = 6003;

    /// <summary>
    /// The identifier for the NamespaceUri Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWL__NamespaceUri = 6004;

    /// <summary>
    /// The identifier for the NamespaceVersion Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWL__NamespaceVersion = 6005;

    /// <summary>
    /// The identifier for the StaticNodeIdTypes Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWL__StaticNodeIdTypes = 6006;

    /// <summary>
    /// The identifier for the StaticNumericNodeIdRange Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWL__StaticNumericNodeIdRange = 6007;

    /// <summary>
    /// The identifier for the StaticStringNodeIdPattern Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeWL__StaticStringNodeIdPattern = 6008;

}
#endregion

#region VariableTypes Identifiers
/// <summary>
/// A class that declares constants for all VariableTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypes
{
}
#endregion

#region DataType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all DataTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypeIds
{
}
#endregion

#region Method Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Methods in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class MethodIds
{
}
#endregion

#region Object Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectIds
{
    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWL_ Object.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWL_ = new ExpandedNodeId(Objects.Namespaces_http___sentron_org_ThreeWL_, Namespaces.ThreeWL);

}
#endregion

#region ObjectType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypeIds
{
    /// <summary>
    /// The identifier for the ThreeWL ObjectType.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL = new ExpandedNodeId(ObjectTypes.ThreeWL, Namespaces.ThreeWL);

}
#endregion

#region ReferenceType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all ReferenceTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypeIds
{
}
#endregion

#region Variable Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Variables in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableIds
{
    /// <summary>
    /// The identifier for the ThreeWL_APhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_APhaseTemp1 = new ExpandedNodeId(Variables.ThreeWL_APhaseTemp1, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_APhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_APhaseTemp2 = new ExpandedNodeId(Variables.ThreeWL_APhaseTemp2, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_BPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_BPhaseTemp1 = new ExpandedNodeId(Variables.ThreeWL_BPhaseTemp1, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_BPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_BPhaseTemp2 = new ExpandedNodeId(Variables.ThreeWL_BPhaseTemp2, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_BreakerTemp Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_BreakerTemp = new ExpandedNodeId(Variables.ThreeWL_BreakerTemp, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_ComTemp Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_ComTemp = new ExpandedNodeId(Variables.ThreeWL_ComTemp, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_CPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_CPhaseTemp1 = new ExpandedNodeId(Variables.ThreeWL_CPhaseTemp1, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_CPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_CPhaseTemp2 = new ExpandedNodeId(Variables.ThreeWL_CPhaseTemp2, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_ElectricalSwitchCycles Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_ElectricalSwitchCycles = new ExpandedNodeId(Variables.ThreeWL_ElectricalSwitchCycles, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_F Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_F = new ExpandedNodeId(Variables.ThreeWL_F, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_ForwardActivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_ForwardActivePower = new ExpandedNodeId(Variables.ThreeWL_ForwardActivePower, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_ForwardReactivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_ForwardReactivePower = new ExpandedNodeId(Variables.ThreeWL_ForwardReactivePower, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_HaveAlarm Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_HaveAlarm = new ExpandedNodeId(Variables.ThreeWL_HaveAlarm, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_HealthScore Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_HealthScore = new ExpandedNodeId(Variables.ThreeWL_HealthScore, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_I_Avg Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_I_Avg = new ExpandedNodeId(Variables.ThreeWL_I_Avg, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_Ia Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_Ia = new ExpandedNodeId(Variables.ThreeWL_Ia, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_Ib Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_Ib = new ExpandedNodeId(Variables.ThreeWL_Ib, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_Ic Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_Ic = new ExpandedNodeId(Variables.ThreeWL_Ic, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_INST_II Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_INST_II = new ExpandedNodeId(Variables.ThreeWL_INST_II, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_INST_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_INST_ONOFF = new ExpandedNodeId(Variables.ThreeWL_INST_ONOFF, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_IsConnected Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_IsConnected = new ExpandedNodeId(Variables.ThreeWL_IsConnected, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_LT_IR Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_LT_IR = new ExpandedNodeId(Variables.ThreeWL_LT_IR, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_LT_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_LT_ONOFF = new ExpandedNodeId(Variables.ThreeWL_LT_ONOFF, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_LT_PARA_CURVE Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_LT_PARA_CURVE = new ExpandedNodeId(Variables.ThreeWL_LT_PARA_CURVE, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_LT_PARA_Phase_Current Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_LT_PARA_Phase_Current = new ExpandedNodeId(Variables.ThreeWL_LT_PARA_Phase_Current, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_LT_TAU Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_LT_TAU = new ExpandedNodeId(Variables.ThreeWL_LT_TAU, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_LT_THERMAL_MEM_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_LT_THERMAL_MEM_ONOFF = new ExpandedNodeId(Variables.ThreeWL_LT_THERMAL_MEM_ONOFF, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_LT_TR Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_LT_TR = new ExpandedNodeId(Variables.ThreeWL_LT_TR, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_LTN_IN Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_LTN_IN = new ExpandedNodeId(Variables.ThreeWL_LTN_IN, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_LTN_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_LTN_ONOFF = new ExpandedNodeId(Variables.ThreeWL_LTN_ONOFF, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_LTTrips Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_LTTrips = new ExpandedNodeId(Variables.ThreeWL_LTTrips, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_MainContantHealth Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_MainContantHealth = new ExpandedNodeId(Variables.ThreeWL_MainContantHealth, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_MainContantStatus Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_MainContantStatus = new ExpandedNodeId(Variables.ThreeWL_MainContantStatus, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_MechanicalSwitchCycles Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_MechanicalSwitchCycles = new ExpandedNodeId(Variables.ThreeWL_MechanicalSwitchCycles, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_NPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_NPhaseTemp1 = new ExpandedNodeId(Variables.ThreeWL_NPhaseTemp1, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_NPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_NPhaseTemp2 = new ExpandedNodeId(Variables.ThreeWL_NPhaseTemp2, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_OperatingHours Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_OperatingHours = new ExpandedNodeId(Variables.ThreeWL_OperatingHours, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_P Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_P = new ExpandedNodeId(Variables.ThreeWL_P, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_PowFactor Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_PowFactor = new ExpandedNodeId(Variables.ThreeWL_PowFactor, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_PowFactor_A Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_PowFactor_A = new ExpandedNodeId(Variables.ThreeWL_PowFactor_A, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_PowFactor_B Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_PowFactor_B = new ExpandedNodeId(Variables.ThreeWL_PowFactor_B, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_PowFactor_C Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_PowFactor_C = new ExpandedNodeId(Variables.ThreeWL_PowFactor_C, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_Q Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_Q = new ExpandedNodeId(Variables.ThreeWL_Q, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_RemainingLife Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_RemainingLife = new ExpandedNodeId(Variables.ThreeWL_RemainingLife, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_ReverseActivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_ReverseActivePower = new ExpandedNodeId(Variables.ThreeWL_ReverseActivePower, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_ReverseReactivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_ReverseReactivePower = new ExpandedNodeId(Variables.ThreeWL_ReverseReactivePower, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_S Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_S = new ExpandedNodeId(Variables.ThreeWL_S, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_SpringCharged Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_SpringCharged = new ExpandedNodeId(Variables.ThreeWL_SpringCharged, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_ST_I2t_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_ST_I2t_ONOFF = new ExpandedNodeId(Variables.ThreeWL_ST_I2t_ONOFF, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_ST_ISD Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_ST_ISD = new ExpandedNodeId(Variables.ThreeWL_ST_ISD, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_ST_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_ST_ONOFF = new ExpandedNodeId(Variables.ThreeWL_ST_ONOFF, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_ST_TSD Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_ST_TSD = new ExpandedNodeId(Variables.ThreeWL_ST_TSD, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_STTrips Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_STTrips = new ExpandedNodeId(Variables.ThreeWL_STTrips, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_Switch Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_Switch = new ExpandedNodeId(Variables.ThreeWL_Switch, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_SwitchReady Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_SwitchReady = new ExpandedNodeId(Variables.ThreeWL_SwitchReady, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_THD_I Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_THD_I = new ExpandedNodeId(Variables.ThreeWL_THD_I, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_THD_U Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_THD_U = new ExpandedNodeId(Variables.ThreeWL_THD_U, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_Ua Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_Ua = new ExpandedNodeId(Variables.ThreeWL_Ua, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_Uab Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_Uab = new ExpandedNodeId(Variables.ThreeWL_Uab, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_Ub Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_Ub = new ExpandedNodeId(Variables.ThreeWL_Ub, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_Ubc Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_Ubc = new ExpandedNodeId(Variables.ThreeWL_Ubc, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_Uc Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_Uc = new ExpandedNodeId(Variables.ThreeWL_Uc, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the ThreeWL_Uca Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeWL_Uca = new ExpandedNodeId(Variables.ThreeWL_Uca, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWL__IsNamespaceSubset Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWL__IsNamespaceSubset = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWL__IsNamespaceSubset, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWL__NamespacePublicationDate Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWL__NamespacePublicationDate = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWL__NamespacePublicationDate, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWL__NamespaceUri Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWL__NamespaceUri = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWL__NamespaceUri, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWL__NamespaceVersion Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWL__NamespaceVersion = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWL__NamespaceVersion, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWL__StaticNodeIdTypes Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWL__StaticNodeIdTypes = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWL__StaticNodeIdTypes, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWL__StaticNumericNodeIdRange Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWL__StaticNumericNodeIdRange = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWL__StaticNumericNodeIdRange, Namespaces.ThreeWL);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeWL__StaticStringNodeIdPattern Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeWL__StaticStringNodeIdPattern = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeWL__StaticStringNodeIdPattern, Namespaces.ThreeWL);

}
#endregion

#region VariableType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all VariableType in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypeIds
{
}
#endregion

#region BrowseName Declarations
/// <summary>
/// Declares all of the BrowseNames used in the Model.
/// </summary>
public static partial class BrowseNames
{
    /// <summary>
    /// The BrowseName for the APhaseTemp1 component.
    /// </summary>
    public const string APhaseTemp1 = "APhaseTemp1";
    /// <summary>
    /// The BrowseName for the APhaseTemp2 component.
    /// </summary>
    public const string APhaseTemp2 = "APhaseTemp2";
    /// <summary>
    /// The BrowseName for the BPhaseTemp1 component.
    /// </summary>
    public const string BPhaseTemp1 = "BPhaseTemp1";
    /// <summary>
    /// The BrowseName for the BPhaseTemp2 component.
    /// </summary>
    public const string BPhaseTemp2 = "BPhaseTemp2";
    /// <summary>
    /// The BrowseName for the BreakerTemp component.
    /// </summary>
    public const string BreakerTemp = "BreakerTemp";
    /// <summary>
    /// The BrowseName for the CPhaseTemp1 component.
    /// </summary>
    public const string CPhaseTemp1 = "CPhaseTemp1";
    /// <summary>
    /// The BrowseName for the CPhaseTemp2 component.
    /// </summary>
    public const string CPhaseTemp2 = "CPhaseTemp2";
    /// <summary>
    /// The BrowseName for the ComTemp component.
    /// </summary>
    public const string ComTemp = "ComTemp";
    /// <summary>
    /// The BrowseName for the ElectricalSwitchCycles component.
    /// </summary>
    public const string ElectricalSwitchCycles = "ElectricalSwitchCycles";
    /// <summary>
    /// The BrowseName for the F component.
    /// </summary>
    public const string F = "F";
    /// <summary>
    /// The BrowseName for the ForwardActivePower component.
    /// </summary>
    public const string ForwardActivePower = "ForwardActivePower";
    /// <summary>
    /// The BrowseName for the ForwardReactivePower component.
    /// </summary>
    public const string ForwardReactivePower = "ForwardReactivePower";
    /// <summary>
    /// The BrowseName for the HaveAlarm component.
    /// </summary>
    public const string HaveAlarm = "HaveAlarm";
    /// <summary>
    /// The BrowseName for the HealthScore component.
    /// </summary>
    public const string HealthScore = "HealthScore";
    /// <summary>
    /// The BrowseName for the INST_II component.
    /// </summary>
    public const string INSTII = "INST_II";
    /// <summary>
    /// The BrowseName for the INST_ONOFF component.
    /// </summary>
    public const string INSTONOFF = "INST_ONOFF";
    /// <summary>
    /// The BrowseName for the I_Avg component.
    /// </summary>
    public const string IAvg = "I_Avg";
    /// <summary>
    /// The BrowseName for the Ia component.
    /// </summary>
    public const string Ia = "Ia";
    /// <summary>
    /// The BrowseName for the Ib component.
    /// </summary>
    public const string Ib = "Ib";
    /// <summary>
    /// The BrowseName for the Ic component.
    /// </summary>
    public const string Ic = "Ic";
    /// <summary>
    /// The BrowseName for the IsConnected component.
    /// </summary>
    public const string IsConnected = "IsConnected";
    /// <summary>
    /// The BrowseName for the LTN_IN component.
    /// </summary>
    public const string LTNIN = "LTN_IN";
    /// <summary>
    /// The BrowseName for the LTN_ONOFF component.
    /// </summary>
    public const string LTNONOFF = "LTN_ONOFF";
    /// <summary>
    /// The BrowseName for the LTTrips component.
    /// </summary>
    public const string LTTrips = "LTTrips";
    /// <summary>
    /// The BrowseName for the LT_IR component.
    /// </summary>
    public const string LTIR = "LT_IR";
    /// <summary>
    /// The BrowseName for the LT_ONOFF component.
    /// </summary>
    public const string LTONOFF = "LT_ONOFF";
    /// <summary>
    /// The BrowseName for the LT_PARA_CURVE component.
    /// </summary>
    public const string LTPARACURVE = "LT_PARA_CURVE";
    /// <summary>
    /// The BrowseName for the LT_PARA_Phase_Current component.
    /// </summary>
    public const string LTPARAPhaseCurrent = "LT_PARA_Phase_Current";
    /// <summary>
    /// The BrowseName for the LT_TAU component.
    /// </summary>
    public const string LTTAU = "LT_TAU";
    /// <summary>
    /// The BrowseName for the LT_THERMAL_MEM_ONOFF component.
    /// </summary>
    public const string LTTHERMALMEMONOFF = "LT_THERMAL_MEM_ONOFF";
    /// <summary>
    /// The BrowseName for the LT_TR component.
    /// </summary>
    public const string LTTR = "LT_TR";
    /// <summary>
    /// The BrowseName for the MainContantHealth component.
    /// </summary>
    public const string MainContantHealth = "MainContantHealth";
    /// <summary>
    /// The BrowseName for the MainContantStatus component.
    /// </summary>
    public const string MainContantStatus = "MainContantStatus";
    /// <summary>
    /// The BrowseName for the MechanicalSwitchCycles component.
    /// </summary>
    public const string MechanicalSwitchCycles = "MechanicalSwitchCycles";
    /// <summary>
    /// The BrowseName for the NPhaseTemp1 component.
    /// </summary>
    public const string NPhaseTemp1 = "NPhaseTemp1";
    /// <summary>
    /// The BrowseName for the NPhaseTemp2 component.
    /// </summary>
    public const string NPhaseTemp2 = "NPhaseTemp2";
    /// <summary>
    /// The BrowseName for the OperatingHours component.
    /// </summary>
    public const string OperatingHours = "OperatingHours";
    /// <summary>
    /// The BrowseName for the P component.
    /// </summary>
    public const string P = "P";
    /// <summary>
    /// The BrowseName for the PowFactor component.
    /// </summary>
    public const string PowFactor = "PowFactor";
    /// <summary>
    /// The BrowseName for the PowFactor_A component.
    /// </summary>
    public const string PowFactorA = "PowFactor_A";
    /// <summary>
    /// The BrowseName for the PowFactor_B component.
    /// </summary>
    public const string PowFactorB = "PowFactor_B";
    /// <summary>
    /// The BrowseName for the PowFactor_C component.
    /// </summary>
    public const string PowFactorC = "PowFactor_C";
    /// <summary>
    /// The BrowseName for the Q component.
    /// </summary>
    public const string Q = "Q";
    /// <summary>
    /// The BrowseName for the RemainingLife component.
    /// </summary>
    public const string RemainingLife = "RemainingLife";
    /// <summary>
    /// The BrowseName for the ReverseActivePower component.
    /// </summary>
    public const string ReverseActivePower = "ReverseActivePower";
    /// <summary>
    /// The BrowseName for the ReverseReactivePower component.
    /// </summary>
    public const string ReverseReactivePower = "ReverseReactivePower";
    /// <summary>
    /// The BrowseName for the S component.
    /// </summary>
    public const string S = "S";
    /// <summary>
    /// The BrowseName for the STTrips component.
    /// </summary>
    public const string STTrips = "STTrips";
    /// <summary>
    /// The BrowseName for the ST_I2t_ONOFF component.
    /// </summary>
    public const string STI2tONOFF = "ST_I2t_ONOFF";
    /// <summary>
    /// The BrowseName for the ST_ISD component.
    /// </summary>
    public const string STISD = "ST_ISD";
    /// <summary>
    /// The BrowseName for the ST_ONOFF component.
    /// </summary>
    public const string STONOFF = "ST_ONOFF";
    /// <summary>
    /// The BrowseName for the ST_TSD component.
    /// </summary>
    public const string STTSD = "ST_TSD";
    /// <summary>
    /// The BrowseName for the SpringCharged component.
    /// </summary>
    public const string SpringCharged = "SpringCharged";
    /// <summary>
    /// The BrowseName for the Switch component.
    /// </summary>
    public const string Switch = "Switch";
    /// <summary>
    /// The BrowseName for the SwitchReady component.
    /// </summary>
    public const string SwitchReady = "SwitchReady";
    /// <summary>
    /// The BrowseName for the THD_I component.
    /// </summary>
    public const string THDI = "THD_I";
    /// <summary>
    /// The BrowseName for the THD_U component.
    /// </summary>
    public const string THDU = "THD_U";
    /// <summary>
    /// The BrowseName for the ThreeWL component.
    /// </summary>
    public const string ThreeWL = "ThreeWL";
    /// <summary>
    /// The BrowseName for the Ua component.
    /// </summary>
    public const string Ua = "Ua";
    /// <summary>
    /// The BrowseName for the Uab component.
    /// </summary>
    public const string Uab = "Uab";
    /// <summary>
    /// The BrowseName for the Ub component.
    /// </summary>
    public const string Ub = "Ub";
    /// <summary>
    /// The BrowseName for the Ubc component.
    /// </summary>
    public const string Ubc = "Ubc";
    /// <summary>
    /// The BrowseName for the Uc component.
    /// </summary>
    public const string Uc = "Uc";
    /// <summary>
    /// The BrowseName for the Uca component.
    /// </summary>
    public const string Uca = "Uca";
    /// <summary>
    /// The BrowseName for the http://sentron.org/ThreeWL/ component.
    /// </summary>
    public const string httpSentronOrgThreeWL = "http://sentron.org/ThreeWL/";
}
#endregion

#region Namespace Declarations
/// <summary>
/// Defines constants for all namespaces referenced by the Model.
/// </summary>
public static partial class Namespaces
{
    /// <summary>
    /// The URI for the OpcUa namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUa = "http://opcfoundation.org/UA/";

    /// <summary>
    /// The URI for the OpcUaXsd namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUaXsd = "http://opcfoundation.org/UA/2008/02/Types.xsd";

    /// <summary>
    /// The URI for the ThreeWL namespace.
    /// </summary>
    public const string ThreeWL = "http://sentron.org/ThreeWL/";

    /// <summary>
    /// The URI for the ThreeWLXsd namespace.
    /// </summary>
    public const string ThreeWLXsd = "http://sentron.org/ThreeWL/Types.xsd";
}
#endregion

