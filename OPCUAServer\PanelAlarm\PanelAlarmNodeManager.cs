using System.Reflection;
using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaServer;

namespace PanelAlarm;

internal partial class PanelAlarmNodeManager : BaseNodeManager
{
    /// <summary>
    /// Called when the node manager is started.
    /// </summary>
    public override void Startup()
    {
        try
        {
            Console.WriteLine("Starting Alarm NodeManager.");

            base.Startup();

            // save the namespaces used by this node manager.
            this.TypeNamespaceIndex = this.AddNamespaceUri(Sentron.PanelAlarm.Namespaces.PanelAlarm);
            this.InstanceNamespaceIndex = this.AddNamespaceUri("http://sentron.org/PanelAlarm");

            // load the model.
            Console.WriteLine("Loading the Controller Model.");
            this.ImportUaNodeset(Assembly.GetEntryAssembly(), "PanelAlarm_Modified.xml");

            // initialize the underlying system.
            this.m_system.Initialize();

            // subscribe to state changed events.
            this.m_system.BlockStateChanged += new BlockStateChangedEventHandler(this.UnderlyingSystem_BlockStateChanged);

            // Create a Folder for Controllers
            CreateObjectSettings settings = new CreateObjectSettings()
            {
                ParentNodeId = ObjectIds.ObjectsFolder,
                ReferenceTypeId = ReferenceTypeIds.Organizes,
                RequestedNodeId = new NodeId("PanelAlarm", this.InstanceNamespaceIndex),
                BrowseName = new QualifiedName("PanelAlarm", this.InstanceNamespaceIndex),
                TypeDefinitionId = ObjectTypeIds.FolderType,

                // need to create a notifier hierarchy for events to propagate.
                NotifierParent = ObjectIds.Server
            };
            this.CreateObject(this.Server.DefaultRequestContext, settings);

            // Create controllers from configuration
            foreach (BlockConfiguration block in this.m_system.GetBlocks())
            {
                // set type definition NodeId
                NodeId typeDefinitionId = ObjectTypeIds.BaseObjectType;
                if (block.Type == BlockType.Alarm)
                {
                    typeDefinitionId = new NodeId(Sentron.PanelAlarm.ObjectTypes.PanelAlarm, this.TypeNamespaceIndex);
                }

                // create object.
                settings = new CreateObjectSettings()
                {
                    ParentNodeId = new NodeId("Controllers", this.InstanceNamespaceIndex),
                    ReferenceTypeId = ReferenceTypeIds.Organizes,
                    RequestedNodeId = new NodeId(block.Name, this.InstanceNamespaceIndex),
                    BrowseName = new QualifiedName(block.Name, this.TypeNamespaceIndex),
                    TypeDefinitionId = typeDefinitionId,

                    // need to create a notifier hierarchy for events to propagate.
                    NotifierParent = new NodeId("Controllers", this.InstanceNamespaceIndex),

                    // add Confirm Method
                    OptionalBrowsePaths = new List<string>()
                    {
                        AbsoluteName.ToString(
                            new QualifiedName(Sentron.PanelAlarm.BrowseNames.StateCondition, this.TypeNamespaceIndex),
                            new QualifiedName(BrowseNames.Confirm))
                    }
                };
                this.CreateObject(this.Server.DefaultRequestContext, settings);

                this.SetMethodUserData(block);

                // [Create Alarm 1]
                // Set alarm condition nodeId
                this.SetAlarmCondition(block);

                // [Create Alarm 1]

                foreach (BlockProperty property in block.Properties)
                {
                    // the node was already created when the controller object was instantiated.
                    // this call links the node to the underlying system data.
                    VariableNode variable = this.SetVariableConfiguration(
                        new NodeId(block.Name, this.InstanceNamespaceIndex),
                        new QualifiedName(property.Name, this.TypeNamespaceIndex),
                        NodeHandleType.ExternalPolled,
                        new SystemAddress() { Address = block.Address, Offset = property.Offset });

                    if (variable != null)
                    {
                        // in-memory nodes must be locked before updates.
                        // reads do not require locks for simple types and references.
                        // value reads require a lock.
                        lock (this.InMemoryNodeLock)
                        {
                            variable.AccessLevel = (property.Writeable) ? AccessLevels.CurrentReadOrWrite : AccessLevels.CurrentRead;
                        }

                        if (property.Range != null)
                        {
                            this.SetVariableDefaultValue(
                                variable.NodeId,
                                new QualifiedName(BrowseNames.EURange),
                                new Variant(property.Range));
                        }
                    }
                }
            }
        }
        catch (Exception e)
        {
            Console.WriteLine("Failed to start Alarm NodeManager. " + e.Message);
        }
    }

    /// <summary>
    /// Called when the node manager is stopped.
    /// </summary>
    public override void Shutdown()
    {
        try
        {
            Console.WriteLine("Stopping Alarm NodeManager.");

            if (this.m_alarmTimer != null)
            {
                this.m_alarmTimer.Dispose();
                this.m_alarmTimer = null;
            }

            base.Shutdown();
        }
        catch (Exception e)
        {
            Console.WriteLine("Failed to stop Alarm NodeManager. " + e.Message);
        }
    }

    /// <summary>
    /// Reads the attributes.
    /// </summary>
    protected override void Read(
        RequestContext context,
        TransactionHandle transaction,
        IList<NodeAttributeOperationHandle> operationHandles,
        IList<ReadValueId> settings)
    {
        for (int ii = 0; ii < operationHandles.Count; ii++)
        {
            DataValue dv = null;

            // the data passed to CreateVariable is returned as the UserData in the handle.
            SystemAddress address = operationHandles[ii].NodeHandle.UserData as SystemAddress;

            if (address != null)
            {
                // read the data from the underlying system.
                object value = this.m_system.Read(address.Address, address.Offset);

                if (value != null)
                {
                    dv = new DataValue(new Variant(value, null), DateTime.UtcNow);

                    // apply any index range or encoding.
                    if (!String.IsNullOrEmpty(settings[ii].IndexRange) || !QualifiedName.IsNull(settings[ii].DataEncoding))
                    {
                        dv = this.ApplyIndexRangeAndEncoding(
                            operationHandles[ii].NodeHandle,
                            dv,
                            settings[ii].IndexRange,
                            settings[ii].DataEncoding);
                    }
                }
            }

            // set an error if not found.
            if (dv == null)
            {
                dv = new DataValue(new StatusCode(StatusCodes.BadNodeIdUnknown));
            }

            // return the data to the caller.
            ((ReadCompleteEventHandler)transaction.Callback)(
                operationHandles[ii],
                transaction.CallbackData,
                dv,
                false);
        }
    }

    /// <summary>
    /// Write the attributes
    /// </summary>
    protected override void Write(
        RequestContext context,
        TransactionHandle transaction,
        IList<NodeAttributeOperationHandle> operationHandles,
        IList<WriteValue> settings)
    {
        for (int ii = 0; ii < operationHandles.Count; ii++)
        {
            StatusCode error = StatusCodes.Good;

            // the data passed to CreateVariable is returned as the UserData in the handle.
            SystemAddress address = operationHandles[ii].NodeHandle.UserData as SystemAddress;

            if (address != null)
            {
                if (!String.IsNullOrEmpty(settings[ii].IndexRange))
                {
                    error = StatusCodes.BadIndexRangeInvalid;
                }
                else if (!this.m_system.Write(address.Address, address.Offset, settings[ii].Value.Value))
                {
                    error = StatusCodes.BadUserAccessDenied;
                }
            }
            else
            {
                error = StatusCodes.BadNodeIdUnknown;
            }

            // return the data to the caller.
            ((WriteCompleteEventHandler)transaction.Callback)(
                operationHandles[ii],
                transaction.CallbackData,
                error,
                false);
        }
    }

    #region Constructor
    /// <summary>
    /// Initializes a new instance of the class.
    /// </summary>
    public PanelAlarmNodeManager(ServerManager server) : base(server)
    {
        this.m_system = new PanelAlarmSystem();
        this.m_alarms = new List<ExclusiveLevelAlarmModel>();
    }
    #endregion

    #region IDisposable
    /// <summary>
    /// An overrideable version of the Dispose.
    /// </summary>
    /// <param name="disposing"></param>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            if (this.m_alarmTimer != null)
            {
                this.m_alarmTimer.Dispose();
                this.m_alarmTimer = null;
            }
        }
    }
    #endregion

    #region Public Properties
    /// <summary>
    /// Gets or sets the index of the instance namespace.
    /// </summary>
    /// <value>
    /// The index of the instance namespace.
    /// </value>
    public ushort InstanceNamespaceIndex { get; set; }

    /// <summary>
    /// Gets or sets the index of the type namespace.
    /// </summary>
    /// <value>
    /// The index of the type namespace.
    /// </value>
    public ushort TypeNamespaceIndex { get; set; }
    #endregion

    #region SystemAddress Class
    private class SystemAddress
    {
        public int Address;
        public int Offset;
    }
    #endregion

    #region Private Methods
    #endregion

    #region Private Fields
    private PanelAlarmSystem m_system;
    #endregion
}
