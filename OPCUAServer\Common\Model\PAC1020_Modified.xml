﻿<?xml version="1.0" encoding="utf-8"?>
<UANodeSet xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:uax="http://opcfoundation.org/UA/2008/02/Types.xsd" xmlns="http://opcfoundation.org/UA/2011/03/UANodeSet.xsd" xmlns:s1="http://sentron.org/PAC1020/Types.xsd" xmlns:ua="http://unifiedautomation.com/Configuration/NodeSet.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <NamespaceUris>
    <Uri>http://sentron.org/PAC1020/</Uri>
  </NamespaceUris>
  <Models>
    <Model ModelUri="http://sentron.org/PAC1020/" PublicationDate="2023-05-01T23:21:12Z" Version="1.0.0">
      <RequiredModel ModelUri="http://opcfoundation.org/UA/" PublicationDate="2022-11-01T00:00:00Z" Version="1.05.02" />
    </Model>
  </Models>
  <Aliases>
    <Alias <PERSON>as="Boolean">i=1</Alias>
    <Alias Alias="String">i=12</Alias>
    <Alias Alias="DateTime">i=13</Alias>
    <Alias Alias="HasModellingRule">i=37</Alias>
    <Alias Alias="HasTypeDefinition">i=40</Alias>
    <Alias Alias="HasSubtype">i=45</Alias>
    <Alias Alias="HasProperty">i=46</Alias>
    <Alias Alias="HasComponent">i=47</Alias>
    <Alias Alias="IdType">i=256</Alias>
    <Alias Alias="NumericRange">i=291</Alias>
  </Aliases>
  <Extensions>
    <Extension>
      <ua:ModelInfo Tool="UaModeler" Hash="S8+m6npHiLC/TJUqwprkdg==" Version="1.6.8" />
    </Extension>
  </Extensions>
  <UAObjectType NodeId="ns=1;i=1003" BrowseName="1:PAC1020">
    <DisplayName>PAC1020</DisplayName>
    <References>
      <Reference ReferenceType="HasComponent">ns=1;i=6100</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6101</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6102</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6103</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6104</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6105</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6106</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6107</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6108</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6109</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6110</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6111</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6112</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6113</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6114</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6115</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6116</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6117</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6118</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6119</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6120</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6121</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6122</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6123</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6124</Reference>
      <Reference ReferenceType="HasComponent">ns=1;i=6125</Reference>
      <Reference ReferenceType="HasSubtype" IsForward="false">i=58</Reference>
    </References>
  </UAObjectType>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6100" BrowseName="1:IsConnected" AccessLevel="3">
    <DisplayName>IsConnected</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6101" BrowseName="1:Ua" AccessLevel="3">
    <DisplayName>Ua</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6102" BrowseName="1:Ub" AccessLevel="3">
    <DisplayName>Ub</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6103" BrowseName="1:Uc" AccessLevel="3">
    <DisplayName>Uc</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6104" BrowseName="1:Uab" AccessLevel="3">
    <DisplayName>Uab</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6105" BrowseName="1:Ubc" AccessLevel="3">
    <DisplayName>Ubc</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6106" BrowseName="1:Uca" AccessLevel="3">
    <DisplayName>Uca</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6107" BrowseName="1:Ia" AccessLevel="3">
    <DisplayName>Ia</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6108" BrowseName="1:Ib" AccessLevel="3">
    <DisplayName>Ib</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6109" BrowseName="1:Ic" AccessLevel="3">
    <DisplayName>Ic</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6110" BrowseName="1:In" AccessLevel="3">
    <DisplayName>In</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6111" BrowseName="1:P" AccessLevel="3">
    <DisplayName>P</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6112" BrowseName="1:Q" AccessLevel="3">
    <DisplayName>Q</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6113" BrowseName="1:PowFactor_A" AccessLevel="3">
    <DisplayName>PowFactor_A</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6114" BrowseName="1:PowFactor_B" AccessLevel="3">
    <DisplayName>PowFactor_B</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6115" BrowseName="1:PowFactor_C" AccessLevel="3">
    <DisplayName>PowFactor_C</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6116" BrowseName="1:F" AccessLevel="3">
    <DisplayName>F</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6117" BrowseName="1:ActiveEnergy" AccessLevel="3">
    <DisplayName>ActiveEnergy</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6118" BrowseName="1:ReactiveEnergy" AccessLevel="3">
    <DisplayName>ReactiveEnergy</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6119" BrowseName="1:DO_0.0" AccessLevel="3">
    <DisplayName>DO_0.0</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6120" BrowseName="1:DI_0.0" AccessLevel="3">
    <DisplayName>DI_0.0</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6121" BrowseName="1:UseVoltageTransformer" AccessLevel="3">
    <DisplayName>UseVoltageTransformer</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6122" BrowseName="1:PrimaryVoltage" AccessLevel="3">
    <DisplayName>PrimaryVoltage</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6123" BrowseName="1:SecondaryVoltage" AccessLevel="3">
    <DisplayName>SecondaryVoltage</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6124" BrowseName="1:PrimaryCurrent" AccessLevel="3">
    <DisplayName>PrimaryCurrent</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=1003" NodeId="ns=1;i=6125" BrowseName="1:SecondaryCurrent" AccessLevel="3">
    <DisplayName>SecondaryCurrent</DisplayName>
    <References>
      <Reference ReferenceType="HasModellingRule">i=78</Reference>
      <Reference ReferenceType="HasTypeDefinition">i=63</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">ns=1;i=1003</Reference>
    </References>
  </UAVariable>
  <UAObject SymbolicName="http___sentron_org_PAC1020_" NodeId="ns=1;i=5001" BrowseName="1:http://sentron.org/PAC1020/">
    <DisplayName>http://sentron.org/PAC1020/</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=11616</Reference>
      <Reference ReferenceType="HasComponent" IsForward="false">i=11715</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6002</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6003</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6004</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6005</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6006</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6007</Reference>
      <Reference ReferenceType="HasProperty">ns=1;i=6008</Reference>
    </References>
  </UAObject>
  <UAVariable DataType="Boolean" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6002" BrowseName="IsNamespaceSubset">
    <DisplayName>IsNamespaceSubset</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
    <Value>
      <uax:Boolean>false</uax:Boolean>
    </Value>
  </UAVariable>
  <UAVariable DataType="DateTime" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6003" BrowseName="NamespacePublicationDate">
    <DisplayName>NamespacePublicationDate</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
    <Value>
      <uax:DateTime>2023-05-01T23:21:12Z</uax:DateTime>
    </Value>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6004" BrowseName="NamespaceUri">
    <DisplayName>NamespaceUri</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
    <Value>
      <uax:String>http://sentron.org/PAC1020/</uax:String>
    </Value>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6005" BrowseName="NamespaceVersion">
    <DisplayName>NamespaceVersion</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
    <Value>
      <uax:String>1.0.0</uax:String>
    </Value>
  </UAVariable>
  <UAVariable DataType="IdType" ParentNodeId="ns=1;i=5001" ValueRank="1" NodeId="ns=1;i=6006" ArrayDimensions="0" BrowseName="StaticNodeIdTypes">
    <DisplayName>StaticNodeIdTypes</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="NumericRange" ParentNodeId="ns=1;i=5001" ValueRank="1" NodeId="ns=1;i=6007" ArrayDimensions="0" BrowseName="StaticNumericNodeIdRange">
    <DisplayName>StaticNumericNodeIdRange</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
  </UAVariable>
  <UAVariable DataType="String" ParentNodeId="ns=1;i=5001" NodeId="ns=1;i=6008" BrowseName="StaticStringNodeIdPattern">
    <DisplayName>StaticStringNodeIdPattern</DisplayName>
    <References>
      <Reference ReferenceType="HasTypeDefinition">i=68</Reference>
      <Reference ReferenceType="HasProperty" IsForward="false">ns=1;i=5001</Reference>
    </References>
  </UAVariable>
</UANodeSet>