/******************************************************************************
** Copyright (c) 2006-2022 Unified Automation GmbH All rights reserved.
**
** Software License Agreement ("SLA") Version 2.8
**
** Unless explicitly acquired and licensed from Licensor under another
** license, the contents of this file are subject to the Software License
** Agreement ("SLA") Version 2.8, or subsequent versions
** as allowed by the SLA, and You may not copy or use this file in either
** source code or executable form, except in compliance with the terms and
** conditions of the SLA.
**
** All software distributed under the SLA is provided strictly on an
** "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESS OR IMPLIED,
** AND LICENSOR HEREBY DISCLAIMS ALL SUCH WARRANTIES, INCLUDING WITHOUT
** LIMITATION, ANY WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
** PURPOSE, QUIET ENJOYMENT, OR NON-INFRINGEMENT. See the SLA for specific
** language governing rights and limitations under the SLA.
**
** Project: .NET based OPC UA Client Server SDK
**
** Description: OPC Unified Architecture Software Development Kit.
**
** The complete license agreement can be found here:
** http://unifiedautomation.com/License/SLA/2.8/
******************************************************************************/

using System.Xml;
using System.Xml.Serialization;
using OPCUAServer.Common;
using RolePermissionBasedAccess;
using Serilog;
using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaServer;

namespace OPCUAServer.UserManagement;

/// <summary>
/// A class that provides access to the user management system.
/// </summary>
public class UserManagementSystem : IDisposable
{
    private static readonly IUserDatabase UserDatabase = JsonUserDatabase.GetInstance();

    private readonly ILogger _logger = LoggingHelper.GetLogger<UserManagementSystem>();

    private int _position;
    private Dictionary<int, BlockConfiguration> _blocks = new();
    private Configuration? _configuration;

    /// <summary>
    /// Initializes a new instance of the <see cref="UserManagementSystem"/> class.
    /// </summary>
    public UserManagementSystem()
    {
    }

    /// <summary>
    /// Initializes this instance.
    /// </summary>
    public void Initialize()
    {
        Load();
    }

    /// <summary>
    /// Gets the blockAddress configurations.
    /// </summary>
    /// <returns>The block configurations.</returns>
    public IEnumerable<BlockConfiguration> GetBlocks()
    {
        return _blocks.Values;
    }

    /// <summary>
    /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
    }

    /// <summary>
    /// Releases unmanaged and - optionally - managed resources
    /// </summary>
    /// <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
    public void Dispose(bool disposing)
    {
        if (disposing)
        {
        }
    }

    /// <summary>
    /// Adds a user to the user management system.
    /// </summary>
    /// <param name="blockAddress">The block address identifier for the user management block.</param>
    /// <param name="username">The unique username for the new user. Must not be null or empty.</param>
    /// <param name="password">The password for the new user. Will be hashed before storage.</param>
    /// <param name="roleName">The role name to assign to the user (e.g., "ConfigureAdmin", "Operator", "Observer"). Default is "Observer".</param>
    /// <returns>StatusCodes.Good if the user was created successfully; otherwise, StatusCodes.Bad.</returns>
    public StatusCode AddUser(int blockAddress, string username, string password, string roleName = "Observer")
    {
        if (UserDatabase.CreateUser(username, password, new List<string> { roleName }))
        {
            _logger.Information($"Create user: {username}, {roleName} successfully.");
            return StatusCodes.Good;
        }

        _logger.Error($"Failed to create user: {username}, {roleName}.");

        return StatusCodes.Bad;
    }

    /// <summary>
    /// Gets all users from the user management system.
    /// </summary>
    /// <param name="blockAddress">The block address identifier for the user management block.</param>
    /// <returns>A list of User objects containing username and role information for all users in the system.</returns>
    public IList<User> GetUsers(int blockAddress)
    {
        return UserDatabase.GetUsers();
    }

    /// <summary>
    /// Stops the specified object id.
    /// </summary>
    /// <param name="blockAddress">The blockAddress.</param>
    /// <param name="userName">The username.</param>
    /// <returns>StatusCodes.Good if the user was removed successfully; otherwise, StatusCodes.Bad.</returns>
    public StatusCode RemoveUser(int blockAddress, string userName)
    {
        if (UserDatabase.DeleteUser(userName))
        {
            this._logger.Information($"Remove user: {userName} successfully.");
            return StatusCodes.Good;
        }

        this._logger.Error($"Failed to remove user: {userName}.");

        return StatusCodes.Bad;
    }

    /// <summary>
    /// Changes the password for a user.
    /// </summary>
    /// <param name="blockAddress">The block address.</param>
    /// <param name="userName">The username.</param>
    /// <param name="oldPassword">The current password.</param>
    /// <param name="newPassword">The new password.</param>
    /// <returns>StatusCodes.Good if the password was changed successfully; otherwise, StatusCodes.Bad.</returns>
    public StatusCode ChangePassword(int blockAddress, string userName, string oldPassword, string newPassword)
    {
        if (UserDatabase.ChangePassword(userName, oldPassword, newPassword))
        {
            this._logger.Information($"Change password for user: {userName} successfully.");
            return StatusCodes.Good;
        }

        this._logger.Error($"Failed to change password for user: {userName}.");

        return StatusCodes.Bad;
    }

    /// <summary>
    /// Resets a user's password to their original password.
    /// </summary>
    /// <param name="blockAddress">The block address.</param>
    /// <param name="userName">The username.</param>
    /// <returns>StatusCodes.Good if the password was reset successfully; otherwise, StatusCodes.Bad.</returns>
    public StatusCode ResetPassword(int blockAddress, string userName)
    {
        if (UserDatabase.ResetPassword(userName))
        {
            this._logger.Information($"Reset password for user: {userName} successfully.");
            return StatusCodes.Good;
        }

        this._logger.Error($"Failed to reset password for user: {userName}.");

        return StatusCodes.Bad;
    }

    /// <summary>
    /// Loads the configuration for the system.
    /// </summary>
    private void Load()
    {
        var assembly = PlatformUtils.GetAssembly(typeof(UserManagementSystem));

        foreach (string resourceName in assembly.GetManifestResourceNames())
        {
            if (resourceName.EndsWith("UserManagementConfiguration.xml"))
            {
                using Stream? istrm = assembly.GetManifestResourceStream(resourceName);
                if (istrm == null)
                {
                    this._logger.Error($"Failed to load configuration for user management system.");
                    return;
                }

                XmlSerializer serializer = new(typeof(Configuration));
                _configuration = serializer.Deserialize(istrm) as Configuration;
            }
        }

        if (_configuration?.Controllers != null)
        {
            for (int ii = 0; ii < _configuration.Controllers.Length; ii++)
            {
                ControllerConfiguration controller = _configuration.Controllers[ii];

                int blockAddress = _position;
                int offset = _position - blockAddress;

                var data = new BlockConfiguration()
                {
                    Address = blockAddress,
                    Name = controller.Name,
                    Type = controller.Type,
                    Properties = new List<BlockProperty>()
                };

                _position += offset;
                _blocks[blockAddress] = data;
            }
        }
    }

    [XmlType(TypeName = "UserManagementSystemControllerProperty", Namespace = "http://sentron.com/usermanagementsystem")]
    public class ControllerProperty
    {
        [XmlElement(Order = 1)]
        public string Name { get; set; }

        [XmlElement(Order = 2)]
        public string DataType { get; set; }

        [XmlElement(Order = 3)]
        public string Value { get; set; }

        [XmlElement(Order = 4)]
        public bool Writeable { get; set; }

        [XmlElement(Order = 5)]
        public string Range { get; set; }

        [XmlElement(Order = 6)]
        public bool Historizing { get; set; }
    }

    [XmlType(TypeName = "UserManagementSystemControllerConfiguration", Namespace = "http://sentron.com/usermanagementsystem")]
    public class ControllerConfiguration
    {
        [XmlElement(Order = 1)]
        public string Name { get; set; }

        [XmlElement(Order = 2)]
        public int Type { get; set; }

        [XmlElement(Order = 3)]
        public ControllerProperty[] Properties;
    }

    [XmlRoot(ElementName = "UserManagementSystemConfiguration", Namespace = "http://sentron.com/usermanagementsystem")]
    public class Configuration
    {
        [XmlElement(Order = 1)]
        public ControllerConfiguration[] Controllers;
    }
}

/// <summary>
/// The configuration for a property of a blockAddress.
/// </summary>
public class BlockProperty
{
    public int Offset;
    public string Name;
    public NodeId DataType;
    public bool Writeable;
    public UnifiedAutomation.UaBase.Range Range;
    public InMemoryHistoryDataSource History;
    public HistoricalDataConfigurationModel HistoryConfiguration;
}

/// <summary>
/// The configuration for a blockAddress.
/// </summary>
public class BlockConfiguration
{
    public int Address;
    public string Name;
    public int Type;
    public List<BlockProperty> Properties;
}

public static class BlockType
{
    public const int UserManagement = 1;
}