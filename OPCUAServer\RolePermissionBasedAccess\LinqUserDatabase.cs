using System.Globalization;
using System.Runtime.Serialization;
using System.Security.Cryptography;
using Newtonsoft.Json;
using Serilog;

namespace RolePermissionBasedAccess;

/// <summary>
/// Implementation of a Serializable User Database using LINQ for querying users
/// </summary>
[Serializable]
public class LinqUserDatabase : IUserDatabase
{
    [NonSerialized]
    protected const int KSaltSize = 16; // 128 bit

    [NonSerialized]
    protected const int KIterations = 10000; // 10k

    [NonSerialized]
    protected const int KKeySize = 32; // 256 bit

    [NonSerialized]
    protected static readonly Dictionary<string, string> RoleNameToNodeIdMapping = new()
    {
        { "observer", "i=15668" },
        { "operator", "i=15680" },
        { "configureadmin", "i=15716" }
    };

    [NonSerialized]
    protected static readonly HashSet<string> CannotDeletedUsers = new()
    {
        "ConfigureAdmin",
        "Operator",
        "Observer"
    };

    [JsonProperty]
    protected ICollection<User> Users = new HashSet<User>();

    [NonSerialized]
    private static object _lock = new();

    [NonSerialized]
    private readonly ILogger _logger = OPCUAServer.Common.LoggingHelper.GetLogger<LinqUserDatabase>();

    [NonSerialized]
    private DateTime _queryCounterResetTime = DateTime.UtcNow;

    /// <summary>
    /// initializes the collection the users database is working with
    /// </summary>
    public virtual void Initialize()
    {
    }

    /// <inheritdoc/>
    public virtual bool CreateUser(string userName, string password, IEnumerable<string> roles)
    {
        if (string.IsNullOrEmpty(userName))
        {
            throw new ArgumentException("UserName cannot be empty.", nameof(userName));
        }

        if (string.IsNullOrEmpty(password))
        {
            throw new ArgumentException("Password cannot be empty.", nameof(password));
        }

        foreach (var role in roles)
        {
            if (!RoleNameToNodeIdMapping.ContainsKey(role))
            {
                throw new ArgumentException("Invalid roles, please check.", nameof(roles));
            }
        }

        // Validate password strength
        if (!ValidatePasswordStrength(password))
        {
            string errorMessage = GetPasswordValidationError(password);
            _logger.Error($"Password validation failed: {errorMessage}");
            throw new ArgumentException(errorMessage, nameof(password));
        }

        if (Users.SingleOrDefault(x => x.UserName == userName) != null)
        {
            _logger.Error($"fail: user exist!");
            return false;
        }

        string hash = Hash(password);

        var user = new User { UserName = userName, Hash = hash, OriginalPassword = hash, Roles = roles };

        Users.Add(user);

        SaveChanges();

        _logger.Information($"User '{userName}' created successfully with strong password.");
        return true;
    }

    /// <inheritdoc/>
    public virtual bool DeleteUser(string userName)
    {
        if (string.IsNullOrEmpty(userName))
        {
            throw new ArgumentException("UserName cannot be empty.", nameof(userName));
        }

        if (CannotDeletedUsers.Contains(userName))
        {
            _logger.Error($"fail: user {userName} cannot be deleted.");
            return false;
        }

        var user = Users.SingleOrDefault(x => x.UserName == userName);

        if (user == null)
        {
            return false;
        }

        Users.Remove(user);

        // Save changes to persist the deletion to the database file
        SaveChanges();

        _logger.Information($"User '{userName}' deleted successfully and changes saved to database.");
        return true;
    }

    /// <inheritdoc/>
    public bool CheckCredentials(string userName, string password)
    {
        if (string.IsNullOrEmpty(userName))
        {
            throw new ArgumentException("UserName cannot be empty.", nameof(userName));
        }

        if (string.IsNullOrEmpty(password))
        {
            throw new ArgumentException("Password cannot be empty.", nameof(password));
        }

        var user = Users.SingleOrDefault(x => string.Equals(x.UserName, userName));

        if (user == null)
        {
            return false;
        }

        return Check(user.Hash, password);
    }

    /// <inheritdoc/>
    public IEnumerable<string> GetUserRoles(string userName)
    {
        if (string.IsNullOrEmpty(userName))
        {
            throw new ArgumentException("UserName cannot be empty.", nameof(userName));
        }

        var user = Users.SingleOrDefault(x => x.UserName == userName);

        if (user == null)
        {
            throw new ArgumentException("No user found with the UserName " + userName);
        }

        return user.Roles;
    }

    /// <inheritdoc/>
    public bool ChangePassword(string userName, string oldPassword, string newPassword)
    {
        if (string.IsNullOrEmpty(userName))
        {
            throw new ArgumentException("UserName cannot be empty.", nameof(userName));
        }

        if (string.IsNullOrEmpty(oldPassword))
        {
            throw new ArgumentException("Current Password cannot be empty.", nameof(oldPassword));
        }

        if (string.IsNullOrEmpty(newPassword))
        {
            throw new ArgumentException("New Password cannot be empty.", nameof(newPassword));
        }

        // Validate new password strength
        if (!ValidatePasswordStrength(newPassword))
        {
            string errorMessage = GetPasswordValidationError(newPassword);
            _logger.Error($"New password validation failed: {errorMessage}");
            throw new ArgumentException(errorMessage, nameof(newPassword));
        }

        var user = Users.SingleOrDefault(x => x.UserName == userName);

        if (user == null)
        {
            return false;
        }

        if (Check(user.Hash, oldPassword))
        {
            var newHash = Hash(newPassword);
            user.Hash = newHash;

            // Save changes to persist the password change to the database file
            SaveChanges();

            _logger.Information($"Password for user '{userName}' changed successfully.");
            return true;
        }

        _logger.Error($"Password change failed for user '{userName}': incorrect old password.");
        return false;
    }

    /// <summary>
    /// Resets a user's password to their original password
    /// </summary>
    /// <param name="userName">The username</param>
    /// <returns>True if the password was successfully reset, false otherwise</returns>
    public virtual bool ResetPassword(string userName)
    {
        if (string.IsNullOrEmpty(userName))
        {
            throw new ArgumentException("UserName cannot be empty.", nameof(userName));
        }

        var user = Users.SingleOrDefault(x => x.UserName == userName);

        if (user == null)
        {
            _logger.Error($"Password reset failed: user '{userName}' not found.");
            return false;
        }

        // Reset password by setting Hash to OriginalPassword
        user.Hash = user.OriginalPassword;

        // Save changes to persist the password reset to the database file
        SaveChanges();

        _logger.Information($"Password for user '{userName}' has been reset to original password.");
        return true;
    }

    /// <inheritdoc/>
    public virtual IList<User> GetUsers()
    {
        return Users.ToList();
    }

    /// <summary>
    /// Persists the changes to the users database
    /// </summary>
    public virtual void Save()
    {
    }

    private void SaveChanges()
    {
        lock (_lock)
        {
            _queryCounterResetTime = DateTime.UtcNow;

            var queryNewUsers = Users.Where(x => x.ID == Guid.Empty);
            if (Users.Count > 0)
            {
                foreach (var user in queryNewUsers)
                {
                    user.ID = Guid.NewGuid();
                }
            }

            Save();
        }
    }

    private string Hash(string password)
    {
#if NETSTANDARD2_0 || NET462
#pragma warning disable CA5379 // Ensure Key Derivation Function algorithm is sufficiently strong
        using (var algorithm = new Rfc2898DeriveBytes(
            password,
            kSaltSize,
            kIterations))
        {
#pragma warning restore CA5379 // Ensure Key Derivation Function algorithm is sufficiently strong
#else
        using (var algorithm = new Rfc2898DeriveBytes(
            password,
            KSaltSize,
            KIterations,
            HashAlgorithmName.SHA512))
        {
#endif
            var key = Convert.ToBase64String(algorithm.GetBytes(KKeySize));
            var salt = Convert.ToBase64String(algorithm.Salt);

            return $"{KIterations}.{salt}.{key}";
        }
    }

    private bool Check(string hash, string password)
    {
        var separator = new Char[] { '.' };
        var parts = hash.Split(separator, 3);

        if (parts.Length != 3)
        {
            throw new FormatException("Unexpected hash format. " +
                "Should be formatted as `{iterations}.{salt}.{hash}`");
        }

        var iterations = Convert.ToInt32(parts[0], CultureInfo.InvariantCulture.NumberFormat);
        var salt = Convert.FromBase64String(parts[1]);
        var key = Convert.FromBase64String(parts[2]);

#if NETSTANDARD2_0 || NET462
#pragma warning disable CA5379 // Ensure Key Derivation Function algorithm is sufficiently strong
        using (var algorithm = new Rfc2898DeriveBytes(
            password,
            salt,
            iterations))
        {
#pragma warning restore CA5379 // Ensure Key Derivation Function algorithm is sufficiently strong
#else
        using (var algorithm = new Rfc2898DeriveBytes(
            password,
            salt,
            iterations,
            HashAlgorithmName.SHA512))
        {
#endif
            var keyToCheck = algorithm.GetBytes(KKeySize);

            var verified = keyToCheck.SequenceEqual(key);
            return verified;
        }
    }

    [OnDeserialized]
    private void OnDeserializedMethod(StreamingContext context)
    {
        _lock = new object();
        _queryCounterResetTime = DateTime.UtcNow;
    }

    /// <summary>
    /// Validates password strength according to security requirements
    /// </summary>
    /// <param name="password">Password to validate</param>
    /// <returns>True if password meets requirements, false otherwise</returns>
    private bool ValidatePasswordStrength(string password)
    {
        if (string.IsNullOrEmpty(password))
        {
            return false;
        }

        // Check minimum length (10 characters)
        if (password.Length < 10)
        {
            return false;
        }

        // Check for at least one digit
        if (!password.Any(char.IsDigit))
        {
            return false;
        }

        // Check for at least one special character
        if (!password.Any(c => !char.IsLetterOrDigit(c)))
        {
            return false;
        }

        // Check for at least one uppercase letter
        if (!password.Any(char.IsUpper))
        {
            return false;
        }

        // Check for at least one lowercase letter
        if (!password.Any(char.IsLower))
        {
            return false;
        }

        return true;
    }

    /// <summary>
    /// Gets detailed password validation error message
    /// </summary>
    /// <param name="password">Password to validate</param>
    /// <returns>Error message describing what requirements are not met</returns>
    private string GetPasswordValidationError(string password)
    {
        var errors = new List<string>();

        if (string.IsNullOrEmpty(password))
        {
            return "Password cannot be empty.";
        }

        if (password.Length < 10)
        {
            errors.Add("minimum 10 characters");
        }

        if (!password.Any(char.IsDigit))
        {
            errors.Add("at least 1 digit");
        }

        if (!password.Any(c => !char.IsLetterOrDigit(c)))
        {
            errors.Add("at least 1 special character");
        }

        if (!password.Any(char.IsUpper))
        {
            errors.Add("at least 1 uppercase letter");
        }

        if (!password.Any(char.IsLower))
        {
            errors.Add("at least 1 lowercase letter");
        }

        if (errors.Count > 0)
        {
            return $"Password must contain: {string.Join(", ", errors)}.";
        }

        return string.Empty;
    }
}




