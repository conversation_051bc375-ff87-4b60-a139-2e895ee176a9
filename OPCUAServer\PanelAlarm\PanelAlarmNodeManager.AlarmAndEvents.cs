using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaServer;

namespace PanelAlarm;

internal partial class PanelAlarmNodeManager
{
    #region Private Fields
    private object m_lock = new object();
    private Timer m_alarmTimer;
    private List<ExclusiveLevelAlarmModel> m_alarms;
    #endregion

    /// <summary>
    /// Called when the state of a blockAddress changes.
    /// </summary>
    private void UnderlyingSystem_BlockStateChanged(int blockAddress, string blockName, int state)
    {
        // create the event.
        GenericEvent e = new GenericEvent(this.Server.FilterManager);

        // initialize base event type fields
        e.Initialize(
            null, // EventId created by SDK if null
            new NodeId(Sentron.PanelAlarm.ObjectTypes.PanelAlarm, this.TypeNamespaceIndex), // EventType
            new NodeId(blockName, this.InstanceNamespaceIndex), // SourceNode
            blockName, // SourceName
            EventSeverity.High, // Severity
            "The 3VA tripped because of an overload trip at Phase L1."); // Message

        // set additional event field State
        e.Set(e.ToPath(new QualifiedName(Sentron.PanelAlarm.BrowseNames.State, this.TypeNamespaceIndex)), state);

        // report the event.
        this.ReportEvent(e.SourceNode, e);

        // [Alarm State]
        NodeId alarmId = new NodeId(blockName + "." + Sentron.PanelAlarm.BrowseNames.StateCondition, this.InstanceNamespaceIndex);
        OffNormalAlarmModel alarm = (OffNormalAlarmModel)this.GetNodeUserData(alarmId);

        // Change state of StateCondition
        lock (alarm)
        {
            alarm.LastSeverity.Value = alarm.Severity;
            alarm.LastSeverity.SourceTimestamp = alarm.Time;
            if (state == 0)
            {
                // Change state to active and unacknowledged
                alarm.Retain = true;
                alarm.Severity = (ushort)EventSeverity.High;
                alarm.AckedState.Value = ConditionStateNames.Unacknowledged;
                alarm.AckedState.Id = false;
                alarm.Message = "The 3VA tripped because of an overload trip at Phase L1. Temperature(��C) = 19 "
                    + " Overload release I = 160";
                alarm.Activate(this.Server.DefaultRequestContext, true);
            }
            else
            {
                // Change state to inactive
                alarm.Severity = (ushort)EventSeverity.Low;
                alarm.Activate(this.Server.DefaultRequestContext, false);
                if (alarm.AckedState.Id == true)
                {
                    alarm.Retain = false;
                }
            }

            e = alarm.CreateEvent(this.Server.FilterManager, true);
        }

        // report alarm status as event.
        this.ReportEvent(alarm.SourceNode, e);

        // [Alarm State]
    }

    // [Create Alarm 2]
    private void SetAlarmCondition(BlockConfiguration block)
    {
        NodeId alarmId = new NodeId(block.Name + "." + Sentron.PanelAlarm.BrowseNames.StateCondition, this.InstanceNamespaceIndex);

        // Create off normal alarm data object
        OffNormalAlarmModel alarm = new OffNormalAlarmModel();
        alarm.NodeId = alarmId;
        alarm.EventType = ObjectTypeIds.OffNormalAlarmType;
        alarm.SourceNode = new NodeId(block.Name, this.InstanceNamespaceIndex);
        alarm.SourceName = block.Name;
        alarm.Severity = (ushort)EventSeverity.Low;
        alarm.ConditionName = "StateCondition";
        alarm.ConditionClassId = ObjectTypeIds.ProcessConditionClassType;
        alarm.ConditionClassName = BrowseNames.ProcessConditionClassType;
        alarm.Retain = false;
        alarm.EnabledState.Value = ConditionStateNames.Enabled;
        alarm.EnabledState.Id = true;
        alarm.AckedState.Value = ConditionStateNames.Acknowledged;
        alarm.AckedState.Id = true;
        alarm.ActiveState.Value = ConditionStateNames.Inactive;
        alarm.ActiveState.Id = false;
        alarm.SuppressedOrShelved = false;

        // Link alarm data object to nodes in address space
        this.LinkModelToNode(alarmId, alarm, null, null, 500);
    }

    /// [Create Alarm 2]
    /// <summary>
    /// Acknowledges an alarm.
    /// </summary>
    /// [Override Acknowledge]
    public override StatusCode Acknowledge(
        RequestContext context,
        AcknowledgeableConditionModel model,
        byte[] eventId,
        LocalizedText comment)
    {
        GenericEvent e = null;

        lock (model)
        {
            StatusCode error = model.Acknowledge(context, eventId, comment);

            if (error.IsBad())
            {
                return error;
            }

            e = model.CreateEvent(this.Server.FilterManager, true);
        }

        this.ReportEvent(model.SourceNode, e);
        return StatusCodes.Good;
    }

    // [Override Acknowledge]

    /// <summary>
    /// Confirms an alarm.
    /// </summary>
    public override StatusCode Confirm(
        RequestContext context,
        AcknowledgeableConditionModel model,
        byte[] eventId,
        LocalizedText comment)
    {
        GenericEvent e = null;

        lock (model)
        {
            StatusCode error = model.Confirm(context, eventId, comment);

            if (error.IsBad())
            {
                return error;
            }

            e = model.CreateEvent(this.Server.FilterManager, true);
        }

        this.ReportEvent(model.SourceNode, e);
        return StatusCodes.Good;
    }
}
