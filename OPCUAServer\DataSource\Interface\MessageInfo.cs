using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace PanelHttp;

public record MessageInfo
{
    [Required]
    public string Oid { get; set; } = string.Empty;

    [Required]
    public Guid DeviceId { get; set; } = Guid.Empty;

    [Required]
    public string MessageText { get; set; } = string.Empty;

    public int Severity { get; set; } = 0;

    public DateTimeOffset Timestamp { get; set; } = DateTimeOffset.UtcNow;

    public static bool ValidateMessageInfo(MessageInfo messageInfo)
    {
        var validationContext = new ValidationContext(messageInfo);
        var validationResults = new List<ValidationResult>();

        bool isValid = Validator.TryValidateObject(messageInfo, validationContext, validationResults, true);

        if (!isValid)
        {
            foreach (var validationResult in validationResults)
            {
                Console.WriteLine($"MessageInfo validation failed: {validationResult.ErrorMessage}");
            }
        }

        return isValid;
    }
}
