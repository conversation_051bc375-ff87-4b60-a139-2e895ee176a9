﻿using System.Xml.Serialization;
using UnifiedAutomation.UaBase;

namespace OPCUAServer.Devices.PAC3120;

#region Configuration File Classes
[XmlType(TypeName = "PAC3120.ControllerProperty", Namespace = "http://sentron.com/PAC3120")]
public class ControllerProperty
{
    [XmlElement(Order = 1)]
    public string Name { get; set; }

    [XmlElement(Order = 2)]
    public string DataType { get; set; }

    [XmlElement(Order = 3)]
    public string Value { get; set; }

    [XmlElement(Order = 4)]
    public bool Writeable { get; set; }

    [XmlElement(Order = 5)]
    public string Range { get; set; }
}

[XmlType(TypeName = "PAC3120.ControllerConfiguration", Namespace = "http://sentron.com/PAC3120")]
public class ControllerConfiguration
{
    [XmlElement(Order = 1)]
    public string Name { get; set; }

    [XmlElement(Order = 2)]
    public int Type { get; set; }

    [XmlElement(Order = 3)]
    public ControllerProperty[] Properties;
}

[XmlRoot(ElementName = "PAC3120.Configuration", Namespace = "http://sentron.com/PAC3120")]
public class Configuration
{
    [XmlElement(Order = 1)]
    public ControllerConfiguration[] Controllers;
}
#endregion
#region BlockProperty Class
/// <summary>
/// The configuration for a property of a blockAddress.
/// </summary>
public class BlockProperty
{
    public int Offset;
    public string Name;
    public NodeId DataType;
    public bool Writeable;
}
#endregion

#region BlockConfiguration Class
/// <summary>
/// The configuration for a blockAddress.
/// </summary>
public class BlockConfiguration
{
    public int Address;
    public string Name;
    public int Type;
    public List<BlockProperty> Properties;
}

#endregion
#region BlockType Class
public static class BlockType
{
    public const int PAC3120 = 1;
}
#endregion
