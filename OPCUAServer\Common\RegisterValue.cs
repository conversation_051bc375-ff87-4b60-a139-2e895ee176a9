using UnifiedAutomation.UaBase;

namespace OPCUAServer.Common;

public class RegisterValue
{
    public object Value { get; set; }

    public NodeId DataType { get; set; } = NodeId.Null;

    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    public T GetValue<T>()
    {
        return (T)Value;
    }

    public bool TryGetValue<T>(out T result)
    {
        try
        {
            result = (T)Value;
            return true;
        }
        catch
        {
            result = default;
            return false;
        }
    }
}