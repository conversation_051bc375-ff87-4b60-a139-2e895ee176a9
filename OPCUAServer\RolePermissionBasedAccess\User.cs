using Newtonsoft.Json;

namespace RolePermissionBasedAccess;

[Serializable]
public class User
{
    [JsonRequired]
    public Guid ID { get; set; }

    [JsonRequired]
    public string UserName { get; set; }

    [JsonRequired]
    public string Hash { get; set; }

    [JsonRequired]
    public string OriginalPassword { get; set; }

    [JsonRequired]
    public IEnumerable<string> Roles { get; set; }

    public override string ToString()
    {
        return JsonConvert.SerializeObject(this);
    }
}




