﻿using System.Collections.Concurrent;
using PanelHttp;

namespace HTTPInterface;

internal class SubstationRealtimeData
{
    private const string BLANK = "-99999";
    private ConcurrentDictionary<string, string> _dictSubstation = new();

    public void SubstationRealtimeInit(string itemId)
    {
        this._dictSubstation = ReadPanelHttp.InitializeDeviceChannels(itemId);
    }

    public void SubstationRealtimeUpdate(string itemId)
    {
        ReadPanelHttp.UpdateDeviceChannels(itemId, this._dictSubstation);
    }

    public string GetChannelValue(string channelName)
    {
        if (string.IsNullOrEmpty(channelName))
        {
            return BLANK;
        }

        return this._dictSubstation.TryGetValue(channelName, out var value)
            ? value ?? BLANK
            : BLANK;
    }
}
