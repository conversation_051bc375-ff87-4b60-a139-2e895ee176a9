using System.Xml.Serialization;
using UnifiedAutomation.UaBase;
using UnifiedAutomation.UaServer;

namespace PanelAlarm;

/// <summary>
/// A class that provides access to the underlying system.
/// </summary>
public class PanelAlarmSystem : IDisposable
{
    const int PUSHINTERVAL = 2000;
    #region Private Fields
    private object m_lock = new object();
    private byte[] m_registers;
    private int m_position;
    private Dictionary<int, BlockConfiguration> m_blocks;
    private Configuration m_configuration;
    private Timer? m_simulationTimer;
    #endregion
    #region Constructors
    /// <summary>
    /// Initializes a new instance of the <see cref="PanelAlarmSystem"/> class.
    /// </summary>
    public PanelAlarmSystem()
    {
        this.m_registers = new byte[4096];
        this.m_blocks = new Dictionary<int, BlockConfiguration>();
        this.m_configuration = new Configuration();
    }
    #endregion

    #region IDisposable Members
    /// <summary>
    /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
    /// </summary>
    public void Dispose()
    {
        this.Dispose(true);
    }

    /// <summary>
    /// Releases unmanaged and - optionally - managed resources
    /// </summary>
    /// <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
    protected void Dispose(bool disposing)
    {
        if (disposing)
        {
            if (this.m_simulationTimer != null)
            {
                this.m_simulationTimer.Dispose();
                this.m_simulationTimer = null;
            }
        }
    }
    #endregion

    #region Public Methods
    /// <summary>
    /// Occurs when [blockAddress state changed].
    /// </summary>
    public event BlockStateChangedEventHandler BlockStateChanged;

    /// <summary>
    /// Initializes this instance.
    /// </summary>
    public void Initialize()
    {
        // load the configuration file.
        this.Load();

        // start the simulation timer.
        this.m_simulationTimer = new Timer(this.DoSimulation, null, PUSHINTERVAL, PUSHINTERVAL);
    }

    /// <summary>
    /// Gets the blockAddress configurations.
    /// </summary>
    /// <returns></returns>
    public IEnumerable<BlockConfiguration> GetBlocks()
    {
        return this.m_blocks.Values;
    }

    /// <summary>
    /// Reads the tag value.
    /// </summary>
    /// <param name="blockAddress">The blockAddress.</param>
    /// <param name="tag">The tag.</param>
    /// <returns>The value. null if no value exists.</returns>
    public object Read(int blockAddress, int tag)
    {
        BlockProperty property = this.FindProperty(blockAddress, tag);

        if (property != null)
        {
            lock (this.m_lock)
            {
                if (property.DataType == DataTypeIds.Double)
                {
                    return (double)BitConverter.ToSingle(this.m_registers, blockAddress + tag);
                }

                if (property.DataType == DataTypeIds.Int32)
                {
                    return BitConverter.ToInt32(this.m_registers, blockAddress + tag);
                }
            }
        }

        return null;
    }

    /// <summary>
    /// Writes the tag value.
    /// </summary>
    /// <param name="blockAddress">The blockAddress.</param>
    /// <param name="tag">The tag.</param>
    /// <param name="value">The value.</param>
    /// <returns>
    /// True if the write was successful.
    /// </returns>
    public bool Write(int blockAddress, int tag, object value)
    {
        BlockProperty property = this.FindProperty(blockAddress, tag);

        if (property != null)
        {
            if (!property.Writeable)
            {
                return false;
            }

            if (property.Name == "State")
            {
                this.RaiseEvent(blockAddress, property.Offset, (int)value);
            }

            lock (this.m_lock)
            {
                if (property.DataType == DataTypeIds.Double)
                {
                    this.Write(blockAddress, tag, (double)value);
                    return true;
                }

                if (property.DataType == DataTypeIds.Int32)
                {
                    this.Write(blockAddress, tag, (int)value);
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// Starts the specified object id.
    /// </summary>
    /// <param name="blockAddress">The blockAddress.</param>
    /// <returns></returns>
    public StatusCode Start(int blockAddress)
    {
        int tag = -1;

        lock (this.m_lock)
        {
            BlockConfiguration controller = null;

            if (!this.m_blocks.TryGetValue(blockAddress, out controller))
            {
                return StatusCodes.BadNodeIdUnknown;
            }

            foreach (BlockProperty property in controller.Properties)
            {
                if (property.Name == "State")
                {
                    tag = property.Offset;
                    break;
                }
            }
        }

        if (tag >= 0)
        {
            this.RaiseEvent(blockAddress, tag, 1);
            this.Write(blockAddress, tag, (int)1);
        }

        return StatusCodes.Good;
    }

    /// <summary>
    /// Stops the specified object id.
    /// </summary>
    /// <param name="blockAddress">The blockAddress.</param>
    /// <returns></returns>
    public StatusCode Stop(int blockAddress)
    {
        int tag = -1;

        lock (this.m_lock)
        {
            BlockConfiguration controller = null;

            if (!this.m_blocks.TryGetValue(blockAddress, out controller))
            {
                return StatusCodes.BadNodeIdUnknown;
            }

            foreach (BlockProperty property in controller.Properties)
            {
                if (property.Name == "State")
                {
                    tag = property.Offset;
                    break;
                }
            }
        }

        if (tag >= 0)
        {
            this.RaiseEvent(blockAddress, tag, 0);
            this.Write(blockAddress, tag, (int)0);
        }

        return StatusCodes.Good;
    }

    /// <summary>
    /// Called when to start the simulation with a set point.
    /// </summary>
    /// <param name="blockAddress">The blockAddress.</param>
    /// <param name="temperatureSetPoint">The temperature set point.</param>
    /// <param name="humditySetPoint">The humidity set point.</param>
    /// <returns></returns>
    public StatusCode StartWithSetPoint(int blockAddress, double temperatureSetPoint, double humditySetPoint)
    {
        int tag = -1;

        lock (this.m_lock)
        {
            BlockConfiguration controller = null;

            if (!this.m_blocks.TryGetValue(blockAddress, out controller))
            {
                return StatusCodes.BadNodeIdUnknown;
            }

            foreach (BlockProperty property in controller.Properties)
            {
                if (property.Name == "TemperatureSetPoint")
                {
                    this.Write(blockAddress, property.Offset, temperatureSetPoint);
                }

                else if (property.Name == "HumiditySetPoint")
                {
                    this.Write(blockAddress, property.Offset, humditySetPoint);
                }

                else if (property.Name == "State")
                {
                    tag = property.Offset;
                }
            }
        }

        if (tag >= 0)
        {
            this.RaiseEvent(blockAddress, tag, 1);
            this.Write(blockAddress, tag, (int)1);
        }

        return StatusCodes.Good;
    }
    #endregion

    #region Private Method
    /// <summary>
    /// Loads the configuration for the system.
    /// </summary>
    private void Load()
    {
        var assembly = PlatformUtils.GetAssembly(typeof(PanelAlarmSystem));
        foreach (string resourceName in assembly.GetManifestResourceNames())
        {
            if (resourceName.EndsWith(".PanelAlarmConfiguration.xml"))
            {
                using (Stream istrm = assembly.GetManifestResourceStream(resourceName))
                {
                    XmlSerializer serializer = new XmlSerializer(typeof(Configuration));
                    this.m_configuration = (Configuration)serializer.Deserialize(istrm);
                }
            }
        }

        if (this.m_configuration.Controllers != null)
        {
            for (int ii = 0; ii < this.m_configuration.Controllers.Length; ii++)
            {
                ControllerConfiguration controller = this.m_configuration.Controllers[ii];

                int blockAddress = this.m_position;
                int offset = this.m_position - blockAddress;

                BlockConfiguration data = new BlockConfiguration()
                {
                    Address = blockAddress,
                    Name = controller.Name,
                    Type = controller.Type,
                    Properties = new List<BlockProperty>()
                };

                if (controller.Properties != null)
                {
                    for (int jj = 0; jj < controller.Properties.Length; jj++)
                    {
                        ControllerProperty property = controller.Properties[jj];
                        NodeId dataTypeId = NodeId.Parse(property.DataType);
                        string value = property.Value;
                        UnifiedAutomation.UaBase.Range range = null;

                        if (!String.IsNullOrEmpty(property.Range))
                        {
                            try
                            {
                                NumericRange nr = NumericRange.Parse(property.Range);
                                range = new UnifiedAutomation.UaBase.Range() { High = nr.End, Low = nr.Begin };
                            }
                            catch (Exception)
                            {
                                range = null;
                            }
                        }

                        data.Properties.Add(new BlockProperty()
                        {
                            Offset = offset,
                            Name = controller.Properties[jj].Name,
                            DataType = dataTypeId,
                            Writeable = controller.Properties[jj].Writeable,
                            Range = range
                        });

                        switch ((uint)dataTypeId.Identifier)
                        {
                            case DataTypes.Int32:
                                {
                                    this.Write(blockAddress, offset, (int)TypeUtils.Cast(value, BuiltInType.Int32));
                                    offset += 4;
                                    break;
                                }

                            case DataTypes.Double:
                                {
                                    this.Write(blockAddress, offset, (double)TypeUtils.Cast(value, BuiltInType.Double));
                                    offset += 4;
                                    break;
                                }
                        }
                    }
                }

                this.m_position += offset;
                this.m_blocks[blockAddress] = data;
            }
        }
    }

    /// <summary>
    /// Writes the specified offset.
    /// </summary>
    /// <param name="offset">The offset.</param>
    /// <param name="value">The value.</param>
    private void Write(int blockAddress, int offset, int value)
    {
        byte[] bytes = BitConverter.GetBytes(value);
        Array.Copy(bytes, 0, this.m_registers, blockAddress + offset, bytes.Length);
    }

    /// <summary>
    /// Writes the specified offset.
    /// </summary>
    /// <param name="offset">The offset.</param>
    /// <param name="value">The value.</param>
    private void Write(int blockAddress, int offset, double value)
    {
        byte[] bytes = BitConverter.GetBytes((float)value);
        Array.Copy(bytes, 0, this.m_registers, blockAddress + offset, bytes.Length);
    }

    /// <summary>
    /// Does the simulation.
    /// </summary>
    /// <param name="state">The state.</param>
    private void DoSimulation(object state)
    {
        try
        {
            lock (this.m_lock)
            {
                foreach (var blockAddress in this.m_blocks)
                {
                    int active = 1;

                    for (int ii = 0; ii < blockAddress.Value.Properties.Count; ii++)
                    {
                        if (blockAddress.Value.Properties[ii].Name == "State")
                        {
                            active = (int)this.Read(blockAddress.Value.Address, blockAddress.Value.Properties[ii].Offset);
                            continue;
                        }
                    }

                    if (active != 0)
                    {
                        for (int ii = 0; ii < blockAddress.Value.Properties.Count - 1; ii++)
                        {
                            string firstName = blockAddress.Value.Properties[ii].Name;
                            string secondName = blockAddress.Value.Properties[ii + 1].Name;

                            if (!secondName.StartsWith(firstName) || !secondName.EndsWith("SetPoint"))
                            {
                                continue;
                            }

                            int valueOffset = blockAddress.Value.Properties[ii].Offset;
                            int setpointOffset = blockAddress.Value.Properties[ii + 1].Offset;

                            double value = (double)this.Read(blockAddress.Key, valueOffset);
                            double setpoint = (double)this.Read(blockAddress.Key, setpointOffset);

                            this.Write(blockAddress.Key, valueOffset, this.Adjust(value, setpoint));
                        }
                    }
                }
            }
        }
        catch (Exception e)
        {
            TraceServer.Error(e, "Failed run simulation.");
        }
    }

    /// <summary>
    /// Adjusts the specified value.
    /// </summary>
    /// <param name="value">The value.</param>
    /// <param name="setPoint">The set point.</param>
    /// <returns></returns>
    private double Adjust(double value, double setPoint)
    {
        Random random = new Random();

        if (Math.Abs(setPoint - value) > 1)
        {
            double delta = (Math.Abs(setPoint - value) + 1) * random.NextDouble();
            return value + ((setPoint > value) ? delta : -delta);
        }
        else
        {
            double delta = Math.Abs(setPoint - value) + 1.1;
            return value + ((random.Next() % 2 == 0) ? delta : -delta);
        }
    }

    /// <summary>
    /// Finds the property.
    /// </summary>
    private BlockProperty FindProperty(int blockAddress, int tag)
    {
        lock (this.m_lock)
        {
            if (blockAddress < 0 || tag < 0)
            {
                return null;
            }

            if (blockAddress + tag > this.m_position - sizeof(int))
            {
                return null;
            }

            BlockConfiguration controller = null;

            if (!this.m_blocks.TryGetValue(blockAddress, out controller))
            {
                return null;
            }

            foreach (BlockProperty property in controller.Properties)
            {
                if (property.Offset == tag)
                {
                    return property;
                }
            }
        }

        return null;
    }

    /// <summary>
    /// Raises a state changed event.
    /// </summary>
    private void RaiseEvent(int blockAddress, int tag, int newState)
    {
        BlockStateChangedEventHandler callback = this.BlockStateChanged;

        if (callback != null)
        {
            string blockName = null;
            int oldState = 0;

            lock (this.m_lock)
            {
                oldState = BitConverter.ToInt32(this.m_registers, blockAddress + tag);
                blockName = this.m_blocks[blockAddress].Name;
            }

            if (oldState != newState)
            {
                try
                {
                    callback(blockAddress, blockName, newState);
                }
                catch (Exception e)
                {
                    TraceServer.Error(e, "Unexpected error raising BlockStateChanged event.");
                }
            }
        }
    }
    #endregion

}

public delegate void BlockStateChangedEventHandler(int blockAddress, string blockName, int state);
