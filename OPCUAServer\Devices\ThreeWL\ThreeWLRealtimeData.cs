﻿using System.Collections.Concurrent;
using PanelHttp;

namespace HTTPInterface;

internal class ThreeWLRealtimeData
{
    private const string BLANK = "-99999";
    private ConcurrentDictionary<string, string> _dictThreeWL = new();

    public void ThreeWLRealtimeInit(string itemId)
    {
        this._dictThreeWL = ReadPanelHttp.InitializeDeviceChannels(itemId);
    }

    public void ThreeWLRealtimeUpdate(string itemId)
    {
        ReadPanelHttp.UpdateDeviceChannels(itemId, this._dictThreeWL);
    }

    public string GetChannelValue(string channelName)
    {
        if (string.IsNullOrEmpty(channelName))
        {
            return BLANK;
        }

        return this._dictThreeWL.TryGetValue(channelName, out var value)
            ? value ?? BLANK
            : BLANK;
    }
}
