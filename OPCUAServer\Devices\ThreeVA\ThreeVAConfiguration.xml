﻿<?xml version="1.0" encoding="utf-8"?>
<ThreeVA.Configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://sentron.com/ThreeVA">
  <Controllers>
    <Name>3VA</Name>
    <Type>1</Type>
    <Properties>
      <Name>HaveAlarm</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>IsConnected</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Switch</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>BreakerTemp</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ua</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ub</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Uc</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Uab</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ubc</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Uca</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ia</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ib</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ic</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>P</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Q</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>S</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>PowFactor</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>PowFactor_A</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>PowFactor_B</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>PowFactor_C</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>F</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ua</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ub</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Uc</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ia</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ib</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ic</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ForwardActivePower</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ForwardReactivePower</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ReverseActivePower</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ReverseReactivePower</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>I_Avg</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>OperatingHours</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>LTTrips</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>STTrips</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>InstTrips</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>GFTrips</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>NTrips</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>AllTrips</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>APhaseTemp1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>APhaseTemp2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>BPhaseTemp1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>BPhaseTemp2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>CPhaseTemp1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>CPhaseTemp2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>NPhaseTemp1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>NPhaseTemp2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>HealthScore</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>RemainingLife</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>LT_IR</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>LT_TR</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>LT_THERMAL_MEM_ONOFF</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>LTN_ONOFF</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>LTN_IN</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ST_ISD</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ST_TSD</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>ST_I2t_ONOFF</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>INST_II</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>GF_ONOFF</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>GF_IG</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>GF_TG</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>GF_PARA_CURVE</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>GF_TYPE</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>GF_ALARM_ONOFF</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>GF_IG_DIRECT_ALARM</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
  </Controllers>
</ThreeVA.Configuration>