using UnifiedAutomation.UaBase;

namespace Sentron.ThreeVA;

#region DataType Identifiers
/// <summary>
/// A class that declares constants for all DataTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypes
{
}
#endregion

#region Object Identifiers
/// <summary>
/// A class that declares constants for all Objects in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Objects
{
    /// <summary>
    /// The identifier for the http://sentron.org/ThreeVA/ Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeVA_ = 5001;

}
#endregion

#region ObjectType Identifiers
/// <summary>
/// A class that declares constants for all ObjectTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypes
{
    /// <summary>
    /// The identifier for the ThreeVA ObjectType.
    /// </summary>
    public const uint ThreeVA = 1003;

}
#endregion

#region Method Identifiers
/// <summary>
/// A class that declares constants for all Methods in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Methods
{
}
#endregion

#region ReferenceType Identifiers
/// <summary>
/// A class that declares constants for all ReferenceTyped in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypes
{
}
#endregion

#region Variable Identifiers
/// <summary>
/// A class that declares constants for all Variables in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Variables
{
    /// <summary>
    /// The identifier for the AllTrips Variable.
    /// </summary>
    public const uint ThreeVA_AllTrips = 6138;

    /// <summary>
    /// The identifier for the APhaseTemp1 Variable.
    /// </summary>
    public const uint ThreeVA_APhaseTemp1 = 6139;

    /// <summary>
    /// The identifier for the APhaseTemp2 Variable.
    /// </summary>
    public const uint ThreeVA_APhaseTemp2 = 6140;

    /// <summary>
    /// The identifier for the BPhaseTemp1 Variable.
    /// </summary>
    public const uint ThreeVA_BPhaseTemp1 = 6141;

    /// <summary>
    /// The identifier for the BPhaseTemp2 Variable.
    /// </summary>
    public const uint ThreeVA_BPhaseTemp2 = 6142;

    /// <summary>
    /// The identifier for the BreakerTemp Variable.
    /// </summary>
    public const uint ThreeVA_BreakerTemp = 6103;

    /// <summary>
    /// The identifier for the CPhaseTemp1 Variable.
    /// </summary>
    public const uint ThreeVA_CPhaseTemp1 = 6143;

    /// <summary>
    /// The identifier for the CPhaseTemp2 Variable.
    /// </summary>
    public const uint ThreeVA_CPhaseTemp2 = 6144;

    /// <summary>
    /// The identifier for the F Variable.
    /// </summary>
    public const uint ThreeVA_F = 6120;

    /// <summary>
    /// The identifier for the ForwardActivePower Variable.
    /// </summary>
    public const uint ThreeVA_ForwardActivePower = 6127;

    /// <summary>
    /// The identifier for the ForwardReactivePower Variable.
    /// </summary>
    public const uint ThreeVA_ForwardReactivePower = 6128;

    /// <summary>
    /// The identifier for the GF_ALARM_ONOFF Variable.
    /// </summary>
    public const uint ThreeVA_GF_ALARM_ONOFF = 6163;

    /// <summary>
    /// The identifier for the GF_IG Variable.
    /// </summary>
    public const uint ThreeVA_GF_IG = 6159;

    /// <summary>
    /// The identifier for the GF_IG_DIRECT_ALARM Variable.
    /// </summary>
    public const uint ThreeVA_GF_IG_DIRECT_ALARM = 6164;

    /// <summary>
    /// The identifier for the GF_ONOFF Variable.
    /// </summary>
    public const uint ThreeVA_GF_ONOFF = 6158;

    /// <summary>
    /// The identifier for the GF_PARA_CURVE Variable.
    /// </summary>
    public const uint ThreeVA_GF_PARA_CURVE = 6161;

    /// <summary>
    /// The identifier for the GF_TG Variable.
    /// </summary>
    public const uint ThreeVA_GF_TG = 6160;

    /// <summary>
    /// The identifier for the GF_TYPE Variable.
    /// </summary>
    public const uint ThreeVA_GF_TYPE = 6162;

    /// <summary>
    /// The identifier for the GFTrips Variable.
    /// </summary>
    public const uint ThreeVA_GFTrips = 6136;

    /// <summary>
    /// The identifier for the HaveAlarm Variable.
    /// </summary>
    public const uint ThreeVA_HaveAlarm = 6100;

    /// <summary>
    /// The identifier for the HealthScore Variable.
    /// </summary>
    public const uint ThreeVA_HealthScore = 6147;

    /// <summary>
    /// The identifier for the I_Avg Variable.
    /// </summary>
    public const uint ThreeVA_I_Avg = 6131;

    /// <summary>
    /// The identifier for the Ia Variable.
    /// </summary>
    public const uint ThreeVA_Ia = 6110;

    /// <summary>
    /// The identifier for the Ib Variable.
    /// </summary>
    public const uint ThreeVA_Ib = 6111;

    /// <summary>
    /// The identifier for the Ic Variable.
    /// </summary>
    public const uint ThreeVA_Ic = 6112;

    /// <summary>
    /// The identifier for the INST_II Variable.
    /// </summary>
    public const uint ThreeVA_INST_II = 6157;

    /// <summary>
    /// The identifier for the InstTrips Variable.
    /// </summary>
    public const uint ThreeVA_InstTrips = 6135;

    /// <summary>
    /// The identifier for the IsConnected Variable.
    /// </summary>
    public const uint ThreeVA_IsConnected = 6101;

    /// <summary>
    /// The identifier for the LT_IR Variable.
    /// </summary>
    public const uint ThreeVA_LT_IR = 6149;

    /// <summary>
    /// The identifier for the LT_THERMAL_MEM_ONOFF Variable.
    /// </summary>
    public const uint ThreeVA_LT_THERMAL_MEM_ONOFF = 6151;

    /// <summary>
    /// The identifier for the LT_TR Variable.
    /// </summary>
    public const uint ThreeVA_LT_TR = 6150;

    /// <summary>
    /// The identifier for the LTN_IN Variable.
    /// </summary>
    public const uint ThreeVA_LTN_IN = 6153;

    /// <summary>
    /// The identifier for the LTN_ONOFF Variable.
    /// </summary>
    public const uint ThreeVA_LTN_ONOFF = 6152;

    /// <summary>
    /// The identifier for the LTTrips Variable.
    /// </summary>
    public const uint ThreeVA_LTTrips = 6133;

    /// <summary>
    /// The identifier for the NPhaseTemp1 Variable.
    /// </summary>
    public const uint ThreeVA_NPhaseTemp1 = 6145;

    /// <summary>
    /// The identifier for the NPhaseTemp2 Variable.
    /// </summary>
    public const uint ThreeVA_NPhaseTemp2 = 6146;

    /// <summary>
    /// The identifier for the NTrips Variable.
    /// </summary>
    public const uint ThreeVA_NTrips = 6137;

    /// <summary>
    /// The identifier for the OperatingHours Variable.
    /// </summary>
    public const uint ThreeVA_OperatingHours = 6132;

    /// <summary>
    /// The identifier for the P Variable.
    /// </summary>
    public const uint ThreeVA_P = 6113;

    /// <summary>
    /// The identifier for the PowFactor Variable.
    /// </summary>
    public const uint ThreeVA_PowFactor = 6116;

    /// <summary>
    /// The identifier for the PowFactor_A Variable.
    /// </summary>
    public const uint ThreeVA_PowFactor_A = 6117;

    /// <summary>
    /// The identifier for the PowFactor_B Variable.
    /// </summary>
    public const uint ThreeVA_PowFactor_B = 6118;

    /// <summary>
    /// The identifier for the PowFactor_C Variable.
    /// </summary>
    public const uint ThreeVA_PowFactor_C = 6119;

    /// <summary>
    /// The identifier for the Q Variable.
    /// </summary>
    public const uint ThreeVA_Q = 6114;

    /// <summary>
    /// The identifier for the RemainingLife Variable.
    /// </summary>
    public const uint ThreeVA_RemainingLife = 6148;

    /// <summary>
    /// The identifier for the ReverseActivePower Variable.
    /// </summary>
    public const uint ThreeVA_ReverseActivePower = 6129;

    /// <summary>
    /// The identifier for the ReverseReactivePower Variable.
    /// </summary>
    public const uint ThreeVA_ReverseReactivePower = 6130;

    /// <summary>
    /// The identifier for the S Variable.
    /// </summary>
    public const uint ThreeVA_S = 6115;

    /// <summary>
    /// The identifier for the ST_I2t_ONOFF Variable.
    /// </summary>
    public const uint ThreeVA_ST_I2t_ONOFF = 6156;

    /// <summary>
    /// The identifier for the ST_ISD Variable.
    /// </summary>
    public const uint ThreeVA_ST_ISD = 6154;

    /// <summary>
    /// The identifier for the ST_TSD Variable.
    /// </summary>
    public const uint ThreeVA_ST_TSD = 6155;

    /// <summary>
    /// The identifier for the STTrips Variable.
    /// </summary>
    public const uint ThreeVA_STTrips = 6134;

    /// <summary>
    /// The identifier for the Switch Variable.
    /// </summary>
    public const uint ThreeVA_Switch = 6102;

    /// <summary>
    /// The identifier for the THD_Ia Variable.
    /// </summary>
    public const uint ThreeVA_THD_Ia = 6124;

    /// <summary>
    /// The identifier for the THD_Ib Variable.
    /// </summary>
    public const uint ThreeVA_THD_Ib = 6125;

    /// <summary>
    /// The identifier for the THD_Ic Variable.
    /// </summary>
    public const uint ThreeVA_THD_Ic = 6126;

    /// <summary>
    /// The identifier for the THD_Ua Variable.
    /// </summary>
    public const uint ThreeVA_THD_Ua = 6121;

    /// <summary>
    /// The identifier for the THD_Ub Variable.
    /// </summary>
    public const uint ThreeVA_THD_Ub = 6122;

    /// <summary>
    /// The identifier for the THD_Uc Variable.
    /// </summary>
    public const uint ThreeVA_THD_Uc = 6123;

    /// <summary>
    /// The identifier for the Ua Variable.
    /// </summary>
    public const uint ThreeVA_Ua = 6104;

    /// <summary>
    /// The identifier for the Uab Variable.
    /// </summary>
    public const uint ThreeVA_Uab = 6107;

    /// <summary>
    /// The identifier for the Ub Variable.
    /// </summary>
    public const uint ThreeVA_Ub = 6105;

    /// <summary>
    /// The identifier for the Ubc Variable.
    /// </summary>
    public const uint ThreeVA_Ubc = 6108;

    /// <summary>
    /// The identifier for the Uc Variable.
    /// </summary>
    public const uint ThreeVA_Uc = 6106;

    /// <summary>
    /// The identifier for the Uca Variable.
    /// </summary>
    public const uint ThreeVA_Uca = 6109;

    /// <summary>
    /// The identifier for the IsNamespaceSubset Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeVA__IsNamespaceSubset = 6002;

    /// <summary>
    /// The identifier for the NamespacePublicationDate Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeVA__NamespacePublicationDate = 6003;

    /// <summary>
    /// The identifier for the NamespaceUri Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeVA__NamespaceUri = 6004;

    /// <summary>
    /// The identifier for the NamespaceVersion Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeVA__NamespaceVersion = 6005;

    /// <summary>
    /// The identifier for the StaticNodeIdTypes Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeVA__StaticNodeIdTypes = 6006;

    /// <summary>
    /// The identifier for the StaticNumericNodeIdRange Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeVA__StaticNumericNodeIdRange = 6007;

    /// <summary>
    /// The identifier for the StaticStringNodeIdPattern Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_ThreeVA__StaticStringNodeIdPattern = 6008;

}
#endregion

#region VariableTypes Identifiers
/// <summary>
/// A class that declares constants for all VariableTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypes
{
}
#endregion

#region DataType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all DataTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypeIds
{
}
#endregion

#region Method Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Methods in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class MethodIds
{
}
#endregion

#region Object Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectIds
{
    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeVA_ Object.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeVA_ = new ExpandedNodeId(Objects.Namespaces_http___sentron_org_ThreeVA_, Namespaces.ThreeVA);

}
#endregion

#region ObjectType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypeIds
{
    /// <summary>
    /// The identifier for the ThreeVA ObjectType.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA = new ExpandedNodeId(ObjectTypes.ThreeVA, Namespaces.ThreeVA);

}
#endregion

#region ReferenceType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all ReferenceTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypeIds
{
}
#endregion

#region Variable Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Variables in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableIds
{
    /// <summary>
    /// The identifier for the ThreeVA_AllTrips Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_AllTrips = new ExpandedNodeId(Variables.ThreeVA_AllTrips, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_APhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_APhaseTemp1 = new ExpandedNodeId(Variables.ThreeVA_APhaseTemp1, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_APhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_APhaseTemp2 = new ExpandedNodeId(Variables.ThreeVA_APhaseTemp2, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_BPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_BPhaseTemp1 = new ExpandedNodeId(Variables.ThreeVA_BPhaseTemp1, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_BPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_BPhaseTemp2 = new ExpandedNodeId(Variables.ThreeVA_BPhaseTemp2, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_BreakerTemp Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_BreakerTemp = new ExpandedNodeId(Variables.ThreeVA_BreakerTemp, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_CPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_CPhaseTemp1 = new ExpandedNodeId(Variables.ThreeVA_CPhaseTemp1, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_CPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_CPhaseTemp2 = new ExpandedNodeId(Variables.ThreeVA_CPhaseTemp2, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_F Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_F = new ExpandedNodeId(Variables.ThreeVA_F, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_ForwardActivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_ForwardActivePower = new ExpandedNodeId(Variables.ThreeVA_ForwardActivePower, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_ForwardReactivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_ForwardReactivePower = new ExpandedNodeId(Variables.ThreeVA_ForwardReactivePower, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_GF_ALARM_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_GF_ALARM_ONOFF = new ExpandedNodeId(Variables.ThreeVA_GF_ALARM_ONOFF, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_GF_IG Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_GF_IG = new ExpandedNodeId(Variables.ThreeVA_GF_IG, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_GF_IG_DIRECT_ALARM Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_GF_IG_DIRECT_ALARM = new ExpandedNodeId(Variables.ThreeVA_GF_IG_DIRECT_ALARM, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_GF_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_GF_ONOFF = new ExpandedNodeId(Variables.ThreeVA_GF_ONOFF, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_GF_PARA_CURVE Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_GF_PARA_CURVE = new ExpandedNodeId(Variables.ThreeVA_GF_PARA_CURVE, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_GF_TG Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_GF_TG = new ExpandedNodeId(Variables.ThreeVA_GF_TG, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_GF_TYPE Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_GF_TYPE = new ExpandedNodeId(Variables.ThreeVA_GF_TYPE, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_GFTrips Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_GFTrips = new ExpandedNodeId(Variables.ThreeVA_GFTrips, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_HaveAlarm Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_HaveAlarm = new ExpandedNodeId(Variables.ThreeVA_HaveAlarm, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_HealthScore Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_HealthScore = new ExpandedNodeId(Variables.ThreeVA_HealthScore, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_I_Avg Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_I_Avg = new ExpandedNodeId(Variables.ThreeVA_I_Avg, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_Ia Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_Ia = new ExpandedNodeId(Variables.ThreeVA_Ia, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_Ib Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_Ib = new ExpandedNodeId(Variables.ThreeVA_Ib, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_Ic Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_Ic = new ExpandedNodeId(Variables.ThreeVA_Ic, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_INST_II Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_INST_II = new ExpandedNodeId(Variables.ThreeVA_INST_II, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_InstTrips Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_InstTrips = new ExpandedNodeId(Variables.ThreeVA_InstTrips, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_IsConnected Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_IsConnected = new ExpandedNodeId(Variables.ThreeVA_IsConnected, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_LT_IR Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_LT_IR = new ExpandedNodeId(Variables.ThreeVA_LT_IR, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_LT_THERMAL_MEM_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_LT_THERMAL_MEM_ONOFF = new ExpandedNodeId(Variables.ThreeVA_LT_THERMAL_MEM_ONOFF, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_LT_TR Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_LT_TR = new ExpandedNodeId(Variables.ThreeVA_LT_TR, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_LTN_IN Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_LTN_IN = new ExpandedNodeId(Variables.ThreeVA_LTN_IN, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_LTN_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_LTN_ONOFF = new ExpandedNodeId(Variables.ThreeVA_LTN_ONOFF, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_LTTrips Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_LTTrips = new ExpandedNodeId(Variables.ThreeVA_LTTrips, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_NPhaseTemp1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_NPhaseTemp1 = new ExpandedNodeId(Variables.ThreeVA_NPhaseTemp1, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_NPhaseTemp2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_NPhaseTemp2 = new ExpandedNodeId(Variables.ThreeVA_NPhaseTemp2, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_NTrips Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_NTrips = new ExpandedNodeId(Variables.ThreeVA_NTrips, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_OperatingHours Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_OperatingHours = new ExpandedNodeId(Variables.ThreeVA_OperatingHours, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_P Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_P = new ExpandedNodeId(Variables.ThreeVA_P, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_PowFactor Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_PowFactor = new ExpandedNodeId(Variables.ThreeVA_PowFactor, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_PowFactor_A Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_PowFactor_A = new ExpandedNodeId(Variables.ThreeVA_PowFactor_A, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_PowFactor_B Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_PowFactor_B = new ExpandedNodeId(Variables.ThreeVA_PowFactor_B, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_PowFactor_C Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_PowFactor_C = new ExpandedNodeId(Variables.ThreeVA_PowFactor_C, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_Q Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_Q = new ExpandedNodeId(Variables.ThreeVA_Q, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_RemainingLife Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_RemainingLife = new ExpandedNodeId(Variables.ThreeVA_RemainingLife, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_ReverseActivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_ReverseActivePower = new ExpandedNodeId(Variables.ThreeVA_ReverseActivePower, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_ReverseReactivePower Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_ReverseReactivePower = new ExpandedNodeId(Variables.ThreeVA_ReverseReactivePower, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_S Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_S = new ExpandedNodeId(Variables.ThreeVA_S, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_ST_I2t_ONOFF Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_ST_I2t_ONOFF = new ExpandedNodeId(Variables.ThreeVA_ST_I2t_ONOFF, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_ST_ISD Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_ST_ISD = new ExpandedNodeId(Variables.ThreeVA_ST_ISD, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_ST_TSD Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_ST_TSD = new ExpandedNodeId(Variables.ThreeVA_ST_TSD, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_STTrips Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_STTrips = new ExpandedNodeId(Variables.ThreeVA_STTrips, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_Switch Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_Switch = new ExpandedNodeId(Variables.ThreeVA_Switch, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_THD_Ia Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_THD_Ia = new ExpandedNodeId(Variables.ThreeVA_THD_Ia, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_THD_Ib Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_THD_Ib = new ExpandedNodeId(Variables.ThreeVA_THD_Ib, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_THD_Ic Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_THD_Ic = new ExpandedNodeId(Variables.ThreeVA_THD_Ic, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_THD_Ua Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_THD_Ua = new ExpandedNodeId(Variables.ThreeVA_THD_Ua, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_THD_Ub Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_THD_Ub = new ExpandedNodeId(Variables.ThreeVA_THD_Ub, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_THD_Uc Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_THD_Uc = new ExpandedNodeId(Variables.ThreeVA_THD_Uc, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_Ua Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_Ua = new ExpandedNodeId(Variables.ThreeVA_Ua, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_Uab Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_Uab = new ExpandedNodeId(Variables.ThreeVA_Uab, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_Ub Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_Ub = new ExpandedNodeId(Variables.ThreeVA_Ub, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_Ubc Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_Ubc = new ExpandedNodeId(Variables.ThreeVA_Ubc, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_Uc Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_Uc = new ExpandedNodeId(Variables.ThreeVA_Uc, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the ThreeVA_Uca Variable.
    /// </summary>
    public static readonly ExpandedNodeId ThreeVA_Uca = new ExpandedNodeId(Variables.ThreeVA_Uca, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeVA__IsNamespaceSubset Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeVA__IsNamespaceSubset = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeVA__IsNamespaceSubset, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeVA__NamespacePublicationDate Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeVA__NamespacePublicationDate = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeVA__NamespacePublicationDate, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeVA__NamespaceUri Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeVA__NamespaceUri = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeVA__NamespaceUri, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeVA__NamespaceVersion Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeVA__NamespaceVersion = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeVA__NamespaceVersion, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeVA__StaticNodeIdTypes Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeVA__StaticNodeIdTypes = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeVA__StaticNodeIdTypes, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeVA__StaticNumericNodeIdRange Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeVA__StaticNumericNodeIdRange = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeVA__StaticNumericNodeIdRange, Namespaces.ThreeVA);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_ThreeVA__StaticStringNodeIdPattern Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_ThreeVA__StaticStringNodeIdPattern = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_ThreeVA__StaticStringNodeIdPattern, Namespaces.ThreeVA);

}
#endregion

#region VariableType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all VariableType in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypeIds
{
}
#endregion

#region BrowseName Declarations
/// <summary>
/// Declares all of the BrowseNames used in the Model.
/// </summary>
public static partial class BrowseNames
{
    /// <summary>
    /// The BrowseName for the APhaseTemp1 component.
    /// </summary>
    public const string APhaseTemp1 = "APhaseTemp1";
    /// <summary>
    /// The BrowseName for the APhaseTemp2 component.
    /// </summary>
    public const string APhaseTemp2 = "APhaseTemp2";
    /// <summary>
    /// The BrowseName for the AllTrips component.
    /// </summary>
    public const string AllTrips = "AllTrips";
    /// <summary>
    /// The BrowseName for the BPhaseTemp1 component.
    /// </summary>
    public const string BPhaseTemp1 = "BPhaseTemp1";
    /// <summary>
    /// The BrowseName for the BPhaseTemp2 component.
    /// </summary>
    public const string BPhaseTemp2 = "BPhaseTemp2";
    /// <summary>
    /// The BrowseName for the BreakerTemp component.
    /// </summary>
    public const string BreakerTemp = "BreakerTemp";
    /// <summary>
    /// The BrowseName for the CPhaseTemp1 component.
    /// </summary>
    public const string CPhaseTemp1 = "CPhaseTemp1";
    /// <summary>
    /// The BrowseName for the CPhaseTemp2 component.
    /// </summary>
    public const string CPhaseTemp2 = "CPhaseTemp2";
    /// <summary>
    /// The BrowseName for the F component.
    /// </summary>
    public const string F = "F";
    /// <summary>
    /// The BrowseName for the ForwardActivePower component.
    /// </summary>
    public const string ForwardActivePower = "ForwardActivePower";
    /// <summary>
    /// The BrowseName for the ForwardReactivePower component.
    /// </summary>
    public const string ForwardReactivePower = "ForwardReactivePower";
    /// <summary>
    /// The BrowseName for the GFTrips component.
    /// </summary>
    public const string GFTrips = "GFTrips";
    /// <summary>
    /// The BrowseName for the GF_ALARM_ONOFF component.
    /// </summary>
    public const string GFALARMONOFF = "GF_ALARM_ONOFF";
    /// <summary>
    /// The BrowseName for the GF_IG component.
    /// </summary>
    public const string GFIG = "GF_IG";
    /// <summary>
    /// The BrowseName for the GF_IG_DIRECT_ALARM component.
    /// </summary>
    public const string GFIGDIRECTALARM = "GF_IG_DIRECT_ALARM";
    /// <summary>
    /// The BrowseName for the GF_ONOFF component.
    /// </summary>
    public const string GFONOFF = "GF_ONOFF";
    /// <summary>
    /// The BrowseName for the GF_PARA_CURVE component.
    /// </summary>
    public const string GFPARACURVE = "GF_PARA_CURVE";
    /// <summary>
    /// The BrowseName for the GF_TG component.
    /// </summary>
    public const string GFTG = "GF_TG";
    /// <summary>
    /// The BrowseName for the GF_TYPE component.
    /// </summary>
    public const string GFTYPE = "GF_TYPE";
    /// <summary>
    /// The BrowseName for the HaveAlarm component.
    /// </summary>
    public const string HaveAlarm = "HaveAlarm";
    /// <summary>
    /// The BrowseName for the HealthScore component.
    /// </summary>
    public const string HealthScore = "HealthScore";
    /// <summary>
    /// The BrowseName for the INST_II component.
    /// </summary>
    public const string INSTII = "INST_II";
    /// <summary>
    /// The BrowseName for the I_Avg component.
    /// </summary>
    public const string IAvg = "I_Avg";
    /// <summary>
    /// The BrowseName for the Ia component.
    /// </summary>
    public const string Ia = "Ia";
    /// <summary>
    /// The BrowseName for the Ib component.
    /// </summary>
    public const string Ib = "Ib";
    /// <summary>
    /// The BrowseName for the Ic component.
    /// </summary>
    public const string Ic = "Ic";
    /// <summary>
    /// The BrowseName for the InstTrips component.
    /// </summary>
    public const string InstTrips = "InstTrips";
    /// <summary>
    /// The BrowseName for the IsConnected component.
    /// </summary>
    public const string IsConnected = "IsConnected";
    /// <summary>
    /// The BrowseName for the LTN_IN component.
    /// </summary>
    public const string LTNIN = "LTN_IN";
    /// <summary>
    /// The BrowseName for the LTN_ONOFF component.
    /// </summary>
    public const string LTNONOFF = "LTN_ONOFF";
    /// <summary>
    /// The BrowseName for the LTTrips component.
    /// </summary>
    public const string LTTrips = "LTTrips";
    /// <summary>
    /// The BrowseName for the LT_IR component.
    /// </summary>
    public const string LTIR = "LT_IR";
    /// <summary>
    /// The BrowseName for the LT_THERMAL_MEM_ONOFF component.
    /// </summary>
    public const string LTTHERMALMEMONOFF = "LT_THERMAL_MEM_ONOFF";
    /// <summary>
    /// The BrowseName for the LT_TR component.
    /// </summary>
    public const string LTTR = "LT_TR";
    /// <summary>
    /// The BrowseName for the NPhaseTemp1 component.
    /// </summary>
    public const string NPhaseTemp1 = "NPhaseTemp1";
    /// <summary>
    /// The BrowseName for the NPhaseTemp2 component.
    /// </summary>
    public const string NPhaseTemp2 = "NPhaseTemp2";
    /// <summary>
    /// The BrowseName for the NTrips component.
    /// </summary>
    public const string NTrips = "NTrips";
    /// <summary>
    /// The BrowseName for the OperatingHours component.
    /// </summary>
    public const string OperatingHours = "OperatingHours";
    /// <summary>
    /// The BrowseName for the P component.
    /// </summary>
    public const string P = "P";
    /// <summary>
    /// The BrowseName for the PowFactor component.
    /// </summary>
    public const string PowFactor = "PowFactor";
    /// <summary>
    /// The BrowseName for the PowFactor_A component.
    /// </summary>
    public const string PowFactorA = "PowFactor_A";
    /// <summary>
    /// The BrowseName for the PowFactor_B component.
    /// </summary>
    public const string PowFactorB = "PowFactor_B";
    /// <summary>
    /// The BrowseName for the PowFactor_C component.
    /// </summary>
    public const string PowFactorC = "PowFactor_C";
    /// <summary>
    /// The BrowseName for the Q component.
    /// </summary>
    public const string Q = "Q";
    /// <summary>
    /// The BrowseName for the RemainingLife component.
    /// </summary>
    public const string RemainingLife = "RemainingLife";
    /// <summary>
    /// The BrowseName for the ReverseActivePower component.
    /// </summary>
    public const string ReverseActivePower = "ReverseActivePower";
    /// <summary>
    /// The BrowseName for the ReverseReactivePower component.
    /// </summary>
    public const string ReverseReactivePower = "ReverseReactivePower";
    /// <summary>
    /// The BrowseName for the S component.
    /// </summary>
    public const string S = "S";
    /// <summary>
    /// The BrowseName for the STTrips component.
    /// </summary>
    public const string STTrips = "STTrips";
    /// <summary>
    /// The BrowseName for the ST_I2t_ONOFF component.
    /// </summary>
    public const string STI2tONOFF = "ST_I2t_ONOFF";
    /// <summary>
    /// The BrowseName for the ST_ISD component.
    /// </summary>
    public const string STISD = "ST_ISD";
    /// <summary>
    /// The BrowseName for the ST_TSD component.
    /// </summary>
    public const string STTSD = "ST_TSD";
    /// <summary>
    /// The BrowseName for the Switch component.
    /// </summary>
    public const string Switch = "Switch";
    /// <summary>
    /// The BrowseName for the THD_Ia component.
    /// </summary>
    public const string THDIa = "THD_Ia";
    /// <summary>
    /// The BrowseName for the THD_Ib component.
    /// </summary>
    public const string THDIb = "THD_Ib";
    /// <summary>
    /// The BrowseName for the THD_Ic component.
    /// </summary>
    public const string THDIc = "THD_Ic";
    /// <summary>
    /// The BrowseName for the THD_Ua component.
    /// </summary>
    public const string THDUa = "THD_Ua";
    /// <summary>
    /// The BrowseName for the THD_Ub component.
    /// </summary>
    public const string THDUb = "THD_Ub";
    /// <summary>
    /// The BrowseName for the THD_Uc component.
    /// </summary>
    public const string THDUc = "THD_Uc";
    /// <summary>
    /// The BrowseName for the ThreeVA component.
    /// </summary>
    public const string ThreeVA = "ThreeVA";
    /// <summary>
    /// The BrowseName for the Ua component.
    /// </summary>
    public const string Ua = "Ua";
    /// <summary>
    /// The BrowseName for the Uab component.
    /// </summary>
    public const string Uab = "Uab";
    /// <summary>
    /// The BrowseName for the Ub component.
    /// </summary>
    public const string Ub = "Ub";
    /// <summary>
    /// The BrowseName for the Ubc component.
    /// </summary>
    public const string Ubc = "Ubc";
    /// <summary>
    /// The BrowseName for the Uc component.
    /// </summary>
    public const string Uc = "Uc";
    /// <summary>
    /// The BrowseName for the Uca component.
    /// </summary>
    public const string Uca = "Uca";
    /// <summary>
    /// The BrowseName for the http://sentron.org/ThreeVA/ component.
    /// </summary>
    public const string httpSentronOrgThreeVA = "http://sentron.org/ThreeVA/";
}
#endregion

#region Namespace Declarations
/// <summary>
/// Defines constants for all namespaces referenced by the Model.
/// </summary>
public static partial class Namespaces
{
    /// <summary>
    /// The URI for the OpcUa namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUa = "http://opcfoundation.org/UA/";

    /// <summary>
    /// The URI for the OpcUaXsd namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUaXsd = "http://opcfoundation.org/UA/2008/02/Types.xsd";

    /// <summary>
    /// The URI for the ThreeVA namespace.
    /// </summary>
    public const string ThreeVA = "http://sentron.org/ThreeVA/";

    /// <summary>
    /// The URI for the ThreeVAXsd namespace.
    /// </summary>
    public const string ThreeVAXsd = "http://sentron.org/ThreeVA/Types.xsd";
}
#endregion

