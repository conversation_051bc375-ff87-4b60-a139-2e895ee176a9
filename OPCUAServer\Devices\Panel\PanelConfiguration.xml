﻿<?xml version="1.0" encoding="utf-8"?>
<Panel.Configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://sentron.com/Panel">
  <Controllers>
    <Name>Panel</Name>
    <Type>1</Type>
    <Properties>
      <Name>HaveAlarm</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ua</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ub</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Uc</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Uab</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ubc</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Uca</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ia</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ib</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Ic</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>P</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>Q</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>S</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>F</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>PowFactor</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ua</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ub</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Uc</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ia</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ib</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>THD_Ic</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>APhaseTemp1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>APhaseTemp2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>BPhaseTemp1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>BPhaseTemp2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>BPhaseTemp1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>CPhaseTemp2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>NPhaseTemp1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>NPhaseTemp2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>AConnectPoint_1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>BConnectPoint_1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>CConnectPoint_1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>NConnectPoint_1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>AConnectPoint_2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>BConnectPoint_2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>CConnectPoint_2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>NConnectPoint_2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>AConnector_Left1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>BConnector_Left1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>CConnector_Left1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>NConnector_Left1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>AConnector_Right1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>BConnector_Right1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>CConnector_Right1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>NConnector_Right1</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>AConnector_Left2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>BConnector_Left2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>CConnector_Left2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>NConnector_Left2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>AConnector_Right2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>BConnector_Right2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>CConnector_Right2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
    <Properties>
      <Name>NConnector_Right2</Name>
      <DataType>i=12</DataType>
      <Value>0</Value>
      <Writeable>true</Writeable>
    </Properties>
  </Controllers>
</Panel.Configuration>