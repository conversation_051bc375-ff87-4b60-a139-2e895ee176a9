root = true

[*.cs]
# C# Code formatting
indent_style = space
indent_size = 4
end_of_line = crlf
charset = utf-8

# StyleCop region rules
dotnet_diagnostic.SA1124.severity = none         # Allow regions - disable "Do not use regions" rule
dotnet_diagnostic.SA1309.severity = none         # Allow underscore prefix - disable "Do not use underscore prefix" rule
dotnet_diagnostic.SA1600.severity = none         # Elements should be documented - disable "Elements should be documented" rule
dotnet_diagnostic.SA1629.severity = none         # Documentation text should end with a period - disable "Documentation text should end with a period" rule
dotnet_diagnostic.SA1413.severity = none         # Use trailing comma in initializer list - disable "Use trailing comma in initializer list" rule
dotnet_diagnostic.SA1101.severity = none         # Prefix local calls with this - disable "Prefix local calls with this" rule
dotnet_diagnostic.SA1025.severity = none         # Code should not contain multiple blank lines in a row - disable "Code should not contain multiple blank lines in a row" rule
dotnet_diagnostic.SA1602.severity = none         # Enumeration items should be documented - disable "Enumeration items should be documented" rule
dotnet_diagnostic.SA1514.severity = none         # Element return value should be documented - disable "Element return value should be documented" rule
# dotnet_diagnostic.SA1201.severity = none         # Elements should appear in the correct order - disable "Elements should appear in the correct order" rule

# Using directive rules
dotnet_sort_system_directives_first = true
dotnet_separate_import_directive_groups = false
csharp_using_directive_placement = outside_namespace:silent

# Specific warning rule configurations
dotnet_diagnostic.IDE0055.severity = suggestion  # Keep other formatting issues as suggestions
dotnet_diagnostic.SA1200.severity = suggestion   # Using placement as suggestion, not error
dotnet_diagnostic.SA1633.severity = none         # File header comment requirements
dotnet_diagnostic.SA1518.severity = suggestion   # File ending newline
; dotnet_diagnostic.SA1309.severity = warning      # Field naming rules

# Using sorting specific rules - SET TO ERROR TO FAIL BUILD
dotnet_diagnostic.SA1208.severity = error        # System using directives should be placed before other using directives
dotnet_diagnostic.SA1210.severity = error        # Using directives should be ordered alphabetically by namespace
dotnet_diagnostic.SA1211.severity = error        # Using alias directives should be ordered alphabetically by alias name

csharp_indent_labels = one_less_than_current
csharp_prefer_simple_using_statement = true:suggestion
csharp_prefer_braces = true:silent
csharp_style_namespace_declarations = block_scoped:silent
csharp_style_prefer_method_group_conversion = true:silent
csharp_style_prefer_top_level_statements = true:silent
csharp_style_prefer_primary_constructors = true:suggestion
csharp_prefer_system_threading_lock = true:suggestion
csharp_style_expression_bodied_methods = false:silent
csharp_style_expression_bodied_constructors = false:silent
csharp_style_expression_bodied_operators = false:silent
csharp_style_expression_bodied_properties = true:silent
csharp_style_expression_bodied_indexers = true:silent
csharp_style_expression_bodied_accessors = true:silent
csharp_style_expression_bodied_lambdas = true:silent
csharp_style_expression_bodied_local_functions = false:silent
csharp_style_throw_expression = true:suggestion
csharp_style_prefer_null_check_over_type_check = true:suggestion
csharp_prefer_simple_default_expression = true:suggestion
csharp_space_around_binary_operators = before_and_after
[*.{cs,vb}]
#### Naming styles ####

# Naming rules

dotnet_naming_rule.interface_should_be_begins_with_i.severity = suggestion
dotnet_naming_rule.interface_should_be_begins_with_i.symbols = interface
dotnet_naming_rule.interface_should_be_begins_with_i.style = begins_with_i

dotnet_naming_rule.types_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.types_should_be_pascal_case.symbols = types
dotnet_naming_rule.types_should_be_pascal_case.style = pascal_case

dotnet_naming_rule.non_field_members_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.non_field_members_should_be_pascal_case.symbols = non_field_members
dotnet_naming_rule.non_field_members_should_be_pascal_case.style = pascal_case

# Symbol specifications

dotnet_naming_symbols.interface.applicable_kinds = interface
dotnet_naming_symbols.interface.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.interface.required_modifiers = 

dotnet_naming_symbols.types.applicable_kinds = class, struct, interface, enum
dotnet_naming_symbols.types.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.types.required_modifiers = 

dotnet_naming_symbols.non_field_members.applicable_kinds = property, event, method
dotnet_naming_symbols.non_field_members.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.non_field_members.required_modifiers = 

# Naming styles

dotnet_naming_style.begins_with_i.required_prefix = I
dotnet_naming_style.begins_with_i.required_suffix = 
dotnet_naming_style.begins_with_i.word_separator = 
dotnet_naming_style.begins_with_i.capitalization = pascal_case

dotnet_naming_style.pascal_case.required_prefix = 
dotnet_naming_style.pascal_case.required_suffix = 
dotnet_naming_style.pascal_case.word_separator = 
dotnet_naming_style.pascal_case.capitalization = pascal_case

dotnet_naming_style.pascal_case.required_prefix = 
dotnet_naming_style.pascal_case.required_suffix = 
dotnet_naming_style.pascal_case.word_separator = 
dotnet_naming_style.pascal_case.capitalization = pascal_case
dotnet_style_operator_placement_when_wrapping = beginning_of_line
tab_width = 4
indent_size = 4
end_of_line = crlf
dotnet_style_coalesce_expression = true:suggestion
dotnet_style_null_propagation = true:suggestion
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:suggestion
dotnet_style_prefer_auto_properties = true:silent
dotnet_style_object_initializer = true:suggestion
dotnet_style_collection_initializer = true:suggestion
dotnet_style_prefer_simplified_boolean_expressions = true:suggestion
dotnet_style_prefer_conditional_expression_over_assignment = true:silent
dotnet_style_prefer_conditional_expression_over_return = true:silent
dotnet_style_explicit_tuple_names = true:suggestion
dotnet_style_prefer_inferred_tuple_names = true:suggestion
dotnet_style_prefer_inferred_anonymous_type_member_names = true:suggestion
dotnet_style_prefer_compound_assignment = true:suggestion
dotnet_style_prefer_simplified_interpolation = true:suggestion
dotnet_style_prefer_collection_expression = when_types_loosely_match:suggestion
dotnet_style_namespace_match_folder = true:suggestion
