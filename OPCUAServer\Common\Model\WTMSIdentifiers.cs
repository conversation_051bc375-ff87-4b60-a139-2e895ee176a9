using UnifiedAutomation.UaBase;

namespace Sentron.WTMS;

#region DataType Identifiers
/// <summary>
/// A class that declares constants for all DataTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypes
{
}
#endregion

#region Object Identifiers
/// <summary>
/// A class that declares constants for all Objects in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Objects
{
    /// <summary>
    /// The identifier for the http://sentron.org/WTMS/ Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_WTMS_ = 5001;

}
#endregion

#region ObjectType Identifiers
/// <summary>
/// A class that declares constants for all ObjectTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypes
{
    /// <summary>
    /// The identifier for the WTMS ObjectType.
    /// </summary>
    public const uint WTMS = 1003;

}
#endregion

#region Method Identifiers
/// <summary>
/// A class that declares constants for all Methods in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Methods
{
}
#endregion

#region ReferenceType Identifiers
/// <summary>
/// A class that declares constants for all ReferenceTyped in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypes
{
}
#endregion

#region Variable Identifiers
/// <summary>
/// A class that declares constants for all Variables in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class Variables
{
    /// <summary>
    /// The identifier for the HaveAlarm Variable.
    /// </summary>
    public const uint WTMS_HaveAlarm = 6100;

    /// <summary>
    /// The identifier for the IsConnected Variable.
    /// </summary>
    public const uint WTMS_IsConnected = 6101;

    /// <summary>
    /// The identifier for the Sensor_1 Variable.
    /// </summary>
    public const uint WTMS_Sensor_1 = 6102;

    /// <summary>
    /// The identifier for the Sensor_10 Variable.
    /// </summary>
    public const uint WTMS_Sensor_10 = 6111;

    /// <summary>
    /// The identifier for the Sensor_100 Variable.
    /// </summary>
    public const uint WTMS_Sensor_100 = 6201;

    /// <summary>
    /// The identifier for the Sensor_100_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_100_connect = 6301;

    /// <summary>
    /// The identifier for the Sensor_100_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_100_offline_time = 6401;

    /// <summary>
    /// The identifier for the Sensor_10_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_10_connect = 6211;

    /// <summary>
    /// The identifier for the Sensor_10_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_10_offline_time = 6311;

    /// <summary>
    /// The identifier for the Sensor_11 Variable.
    /// </summary>
    public const uint WTMS_Sensor_11 = 6112;

    /// <summary>
    /// The identifier for the Sensor_11_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_11_connect = 6212;

    /// <summary>
    /// The identifier for the Sensor_11_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_11_offline_time = 6312;

    /// <summary>
    /// The identifier for the Sensor_12 Variable.
    /// </summary>
    public const uint WTMS_Sensor_12 = 6113;

    /// <summary>
    /// The identifier for the Sensor_12_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_12_connect = 6213;

    /// <summary>
    /// The identifier for the Sensor_12_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_12_offline_time = 6313;

    /// <summary>
    /// The identifier for the Sensor_13 Variable.
    /// </summary>
    public const uint WTMS_Sensor_13 = 6114;

    /// <summary>
    /// The identifier for the Sensor_13_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_13_connect = 6214;

    /// <summary>
    /// The identifier for the Sensor_13_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_13_offline_time = 6314;

    /// <summary>
    /// The identifier for the Sensor_14 Variable.
    /// </summary>
    public const uint WTMS_Sensor_14 = 6115;

    /// <summary>
    /// The identifier for the Sensor_14_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_14_connect = 6215;

    /// <summary>
    /// The identifier for the Sensor_14_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_14_offline_time = 6315;

    /// <summary>
    /// The identifier for the Sensor_15 Variable.
    /// </summary>
    public const uint WTMS_Sensor_15 = 6116;

    /// <summary>
    /// The identifier for the Sensor_15_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_15_connect = 6216;

    /// <summary>
    /// The identifier for the Sensor_15_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_15_offline_time = 6316;

    /// <summary>
    /// The identifier for the Sensor_16 Variable.
    /// </summary>
    public const uint WTMS_Sensor_16 = 6117;

    /// <summary>
    /// The identifier for the Sensor_16_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_16_connect = 6217;

    /// <summary>
    /// The identifier for the Sensor_16_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_16_offline_time = 6317;

    /// <summary>
    /// The identifier for the Sensor_17 Variable.
    /// </summary>
    public const uint WTMS_Sensor_17 = 6118;

    /// <summary>
    /// The identifier for the Sensor_17_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_17_connect = 6218;

    /// <summary>
    /// The identifier for the Sensor_17_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_17_offline_time = 6318;

    /// <summary>
    /// The identifier for the Sensor_18 Variable.
    /// </summary>
    public const uint WTMS_Sensor_18 = 6119;

    /// <summary>
    /// The identifier for the Sensor_18_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_18_connect = 6219;

    /// <summary>
    /// The identifier for the Sensor_18_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_18_offline_time = 6319;

    /// <summary>
    /// The identifier for the Sensor_19 Variable.
    /// </summary>
    public const uint WTMS_Sensor_19 = 6120;

    /// <summary>
    /// The identifier for the Sensor_19_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_19_connect = 6220;

    /// <summary>
    /// The identifier for the Sensor_19_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_19_offline_time = 6320;

    /// <summary>
    /// The identifier for the Sensor_1_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_1_connect = 6202;

    /// <summary>
    /// The identifier for the Sensor_1_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_1_offline_time = 6302;

    /// <summary>
    /// The identifier for the Sensor_2 Variable.
    /// </summary>
    public const uint WTMS_Sensor_2 = 6103;

    /// <summary>
    /// The identifier for the Sensor_20 Variable.
    /// </summary>
    public const uint WTMS_Sensor_20 = 6121;

    /// <summary>
    /// The identifier for the Sensor_20_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_20_connect = 6221;

    /// <summary>
    /// The identifier for the Sensor_20_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_20_offline_time = 6321;

    /// <summary>
    /// The identifier for the Sensor_21 Variable.
    /// </summary>
    public const uint WTMS_Sensor_21 = 6122;

    /// <summary>
    /// The identifier for the Sensor_21_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_21_connect = 6222;

    /// <summary>
    /// The identifier for the Sensor_21_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_21_offline_time = 6322;

    /// <summary>
    /// The identifier for the Sensor_22 Variable.
    /// </summary>
    public const uint WTMS_Sensor_22 = 6123;

    /// <summary>
    /// The identifier for the Sensor_22_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_22_connect = 6223;

    /// <summary>
    /// The identifier for the Sensor_22_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_22_offline_time = 6323;

    /// <summary>
    /// The identifier for the Sensor_23 Variable.
    /// </summary>
    public const uint WTMS_Sensor_23 = 6124;

    /// <summary>
    /// The identifier for the Sensor_23_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_23_connect = 6224;

    /// <summary>
    /// The identifier for the Sensor_23_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_23_offline_time = 6324;

    /// <summary>
    /// The identifier for the Sensor_24 Variable.
    /// </summary>
    public const uint WTMS_Sensor_24 = 6125;

    /// <summary>
    /// The identifier for the Sensor_24_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_24_connect = 6225;

    /// <summary>
    /// The identifier for the Sensor_24_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_24_offline_time = 6325;

    /// <summary>
    /// The identifier for the Sensor_25 Variable.
    /// </summary>
    public const uint WTMS_Sensor_25 = 6126;

    /// <summary>
    /// The identifier for the Sensor_25_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_25_connect = 6226;

    /// <summary>
    /// The identifier for the Sensor_25_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_25_offline_time = 6326;

    /// <summary>
    /// The identifier for the Sensor_26 Variable.
    /// </summary>
    public const uint WTMS_Sensor_26 = 6127;

    /// <summary>
    /// The identifier for the Sensor_26_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_26_connect = 6227;

    /// <summary>
    /// The identifier for the Sensor_26_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_26_offline_time = 6327;

    /// <summary>
    /// The identifier for the Sensor_27 Variable.
    /// </summary>
    public const uint WTMS_Sensor_27 = 6128;

    /// <summary>
    /// The identifier for the Sensor_27_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_27_connect = 6228;

    /// <summary>
    /// The identifier for the Sensor_27_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_27_offline_time = 6328;

    /// <summary>
    /// The identifier for the Sensor_28 Variable.
    /// </summary>
    public const uint WTMS_Sensor_28 = 6129;

    /// <summary>
    /// The identifier for the Sensor_28_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_28_connect = 6229;

    /// <summary>
    /// The identifier for the Sensor_28_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_28_offline_time = 6329;

    /// <summary>
    /// The identifier for the Sensor_29 Variable.
    /// </summary>
    public const uint WTMS_Sensor_29 = 6130;

    /// <summary>
    /// The identifier for the Sensor_29_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_29_connect = 6230;

    /// <summary>
    /// The identifier for the Sensor_29_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_29_offline_time = 6330;

    /// <summary>
    /// The identifier for the Sensor_2_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_2_connect = 6203;

    /// <summary>
    /// The identifier for the Sensor_2_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_2_offline_time = 6303;

    /// <summary>
    /// The identifier for the Sensor_3 Variable.
    /// </summary>
    public const uint WTMS_Sensor_3 = 6104;

    /// <summary>
    /// The identifier for the Sensor_30 Variable.
    /// </summary>
    public const uint WTMS_Sensor_30 = 6131;

    /// <summary>
    /// The identifier for the Sensor_30_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_30_connect = 6231;

    /// <summary>
    /// The identifier for the Sensor_30_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_30_offline_time = 6331;

    /// <summary>
    /// The identifier for the Sensor_31 Variable.
    /// </summary>
    public const uint WTMS_Sensor_31 = 6132;

    /// <summary>
    /// The identifier for the Sensor_31_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_31_connect = 6232;

    /// <summary>
    /// The identifier for the Sensor_31_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_31_offline_time = 6332;

    /// <summary>
    /// The identifier for the Sensor_32 Variable.
    /// </summary>
    public const uint WTMS_Sensor_32 = 6133;

    /// <summary>
    /// The identifier for the Sensor_32_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_32_connect = 6233;

    /// <summary>
    /// The identifier for the Sensor_32_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_32_offline_time = 6333;

    /// <summary>
    /// The identifier for the Sensor_33 Variable.
    /// </summary>
    public const uint WTMS_Sensor_33 = 6134;

    /// <summary>
    /// The identifier for the Sensor_33_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_33_connect = 6234;

    /// <summary>
    /// The identifier for the Sensor_33_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_33_offline_time = 6334;

    /// <summary>
    /// The identifier for the Sensor_34 Variable.
    /// </summary>
    public const uint WTMS_Sensor_34 = 6135;

    /// <summary>
    /// The identifier for the Sensor_34_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_34_connect = 6235;

    /// <summary>
    /// The identifier for the Sensor_34_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_34_offline_time = 6335;

    /// <summary>
    /// The identifier for the Sensor_35 Variable.
    /// </summary>
    public const uint WTMS_Sensor_35 = 6136;

    /// <summary>
    /// The identifier for the Sensor_35_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_35_connect = 6236;

    /// <summary>
    /// The identifier for the Sensor_35_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_35_offline_time = 6336;

    /// <summary>
    /// The identifier for the Sensor_36 Variable.
    /// </summary>
    public const uint WTMS_Sensor_36 = 6137;

    /// <summary>
    /// The identifier for the Sensor_36_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_36_connect = 6237;

    /// <summary>
    /// The identifier for the Sensor_36_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_36_offline_time = 6337;

    /// <summary>
    /// The identifier for the Sensor_37 Variable.
    /// </summary>
    public const uint WTMS_Sensor_37 = 6138;

    /// <summary>
    /// The identifier for the Sensor_37_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_37_connect = 6238;

    /// <summary>
    /// The identifier for the Sensor_37_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_37_offline_time = 6338;

    /// <summary>
    /// The identifier for the Sensor_38 Variable.
    /// </summary>
    public const uint WTMS_Sensor_38 = 6139;

    /// <summary>
    /// The identifier for the Sensor_38_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_38_connect = 6239;

    /// <summary>
    /// The identifier for the Sensor_38_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_38_offline_time = 6339;

    /// <summary>
    /// The identifier for the Sensor_39 Variable.
    /// </summary>
    public const uint WTMS_Sensor_39 = 6140;

    /// <summary>
    /// The identifier for the Sensor_39_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_39_connect = 6240;

    /// <summary>
    /// The identifier for the Sensor_39_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_39_offline_time = 6340;

    /// <summary>
    /// The identifier for the Sensor_3_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_3_connect = 6204;

    /// <summary>
    /// The identifier for the Sensor_3_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_3_offline_time = 6304;

    /// <summary>
    /// The identifier for the Sensor_4 Variable.
    /// </summary>
    public const uint WTMS_Sensor_4 = 6105;

    /// <summary>
    /// The identifier for the Sensor_40 Variable.
    /// </summary>
    public const uint WTMS_Sensor_40 = 6141;

    /// <summary>
    /// The identifier for the Sensor_40_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_40_connect = 6241;

    /// <summary>
    /// The identifier for the Sensor_40_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_40_offline_time = 6341;

    /// <summary>
    /// The identifier for the Sensor_41 Variable.
    /// </summary>
    public const uint WTMS_Sensor_41 = 6142;

    /// <summary>
    /// The identifier for the Sensor_41_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_41_connect = 6242;

    /// <summary>
    /// The identifier for the Sensor_41_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_41_offline_time = 6342;

    /// <summary>
    /// The identifier for the Sensor_42 Variable.
    /// </summary>
    public const uint WTMS_Sensor_42 = 6143;

    /// <summary>
    /// The identifier for the Sensor_42_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_42_connect = 6243;

    /// <summary>
    /// The identifier for the Sensor_42_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_42_offline_time = 6343;

    /// <summary>
    /// The identifier for the Sensor_43 Variable.
    /// </summary>
    public const uint WTMS_Sensor_43 = 6144;

    /// <summary>
    /// The identifier for the Sensor_43_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_43_connect = 6244;

    /// <summary>
    /// The identifier for the Sensor_43_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_43_offline_time = 6344;

    /// <summary>
    /// The identifier for the Sensor_44 Variable.
    /// </summary>
    public const uint WTMS_Sensor_44 = 6145;

    /// <summary>
    /// The identifier for the Sensor_44_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_44_connect = 6245;

    /// <summary>
    /// The identifier for the Sensor_44_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_44_offline_time = 6345;

    /// <summary>
    /// The identifier for the Sensor_45 Variable.
    /// </summary>
    public const uint WTMS_Sensor_45 = 6146;

    /// <summary>
    /// The identifier for the Sensor_45_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_45_connect = 6246;

    /// <summary>
    /// The identifier for the Sensor_45_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_45_offline_time = 6346;

    /// <summary>
    /// The identifier for the Sensor_46 Variable.
    /// </summary>
    public const uint WTMS_Sensor_46 = 6147;

    /// <summary>
    /// The identifier for the Sensor_46_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_46_connect = 6247;

    /// <summary>
    /// The identifier for the Sensor_46_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_46_offline_time = 6347;

    /// <summary>
    /// The identifier for the Sensor_47 Variable.
    /// </summary>
    public const uint WTMS_Sensor_47 = 6148;

    /// <summary>
    /// The identifier for the Sensor_47_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_47_connect = 6248;

    /// <summary>
    /// The identifier for the Sensor_47_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_47_offline_time = 6348;

    /// <summary>
    /// The identifier for the Sensor_48 Variable.
    /// </summary>
    public const uint WTMS_Sensor_48 = 6149;

    /// <summary>
    /// The identifier for the Sensor_48_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_48_connect = 6249;

    /// <summary>
    /// The identifier for the Sensor_48_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_48_offline_time = 6349;

    /// <summary>
    /// The identifier for the Sensor_49 Variable.
    /// </summary>
    public const uint WTMS_Sensor_49 = 6150;

    /// <summary>
    /// The identifier for the Sensor_49_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_49_connect = 6250;

    /// <summary>
    /// The identifier for the Sensor_49_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_49_offline_time = 6350;

    /// <summary>
    /// The identifier for the Sensor_4_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_4_connect = 6205;

    /// <summary>
    /// The identifier for the Sensor_4_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_4_offline_time = 6305;

    /// <summary>
    /// The identifier for the Sensor_5 Variable.
    /// </summary>
    public const uint WTMS_Sensor_5 = 6106;

    /// <summary>
    /// The identifier for the Sensor_50 Variable.
    /// </summary>
    public const uint WTMS_Sensor_50 = 6151;

    /// <summary>
    /// The identifier for the Sensor_50_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_50_connect = 6251;

    /// <summary>
    /// The identifier for the Sensor_50_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_50_offline_time = 6351;

    /// <summary>
    /// The identifier for the Sensor_51 Variable.
    /// </summary>
    public const uint WTMS_Sensor_51 = 6152;

    /// <summary>
    /// The identifier for the Sensor_51_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_51_connect = 6252;

    /// <summary>
    /// The identifier for the Sensor_51_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_51_offline_time = 6352;

    /// <summary>
    /// The identifier for the Sensor_52 Variable.
    /// </summary>
    public const uint WTMS_Sensor_52 = 6153;

    /// <summary>
    /// The identifier for the Sensor_52_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_52_connect = 6253;

    /// <summary>
    /// The identifier for the Sensor_52_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_52_offline_time = 6353;

    /// <summary>
    /// The identifier for the Sensor_53 Variable.
    /// </summary>
    public const uint WTMS_Sensor_53 = 6154;

    /// <summary>
    /// The identifier for the Sensor_53_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_53_connect = 6254;

    /// <summary>
    /// The identifier for the Sensor_53_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_53_offline_time = 6354;

    /// <summary>
    /// The identifier for the Sensor_54 Variable.
    /// </summary>
    public const uint WTMS_Sensor_54 = 6155;

    /// <summary>
    /// The identifier for the Sensor_54_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_54_connect = 6255;

    /// <summary>
    /// The identifier for the Sensor_54_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_54_offline_time = 6355;

    /// <summary>
    /// The identifier for the Sensor_55 Variable.
    /// </summary>
    public const uint WTMS_Sensor_55 = 6156;

    /// <summary>
    /// The identifier for the Sensor_55_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_55_connect = 6256;

    /// <summary>
    /// The identifier for the Sensor_55_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_55_offline_time = 6356;

    /// <summary>
    /// The identifier for the Sensor_56 Variable.
    /// </summary>
    public const uint WTMS_Sensor_56 = 6157;

    /// <summary>
    /// The identifier for the Sensor_56_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_56_connect = 6257;

    /// <summary>
    /// The identifier for the Sensor_56_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_56_offline_time = 6357;

    /// <summary>
    /// The identifier for the Sensor_57 Variable.
    /// </summary>
    public const uint WTMS_Sensor_57 = 6158;

    /// <summary>
    /// The identifier for the Sensor_57_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_57_connect = 6258;

    /// <summary>
    /// The identifier for the Sensor_57_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_57_offline_time = 6358;

    /// <summary>
    /// The identifier for the Sensor_58 Variable.
    /// </summary>
    public const uint WTMS_Sensor_58 = 6159;

    /// <summary>
    /// The identifier for the Sensor_58_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_58_connect = 6259;

    /// <summary>
    /// The identifier for the Sensor_58_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_58_offline_time = 6359;

    /// <summary>
    /// The identifier for the Sensor_59 Variable.
    /// </summary>
    public const uint WTMS_Sensor_59 = 6160;

    /// <summary>
    /// The identifier for the Sensor_59_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_59_connect = 6260;

    /// <summary>
    /// The identifier for the Sensor_59_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_59_offline_time = 6360;

    /// <summary>
    /// The identifier for the Sensor_5_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_5_connect = 6206;

    /// <summary>
    /// The identifier for the Sensor_5_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_5_offline_time = 6306;

    /// <summary>
    /// The identifier for the Sensor_6 Variable.
    /// </summary>
    public const uint WTMS_Sensor_6 = 6107;

    /// <summary>
    /// The identifier for the Sensor_60 Variable.
    /// </summary>
    public const uint WTMS_Sensor_60 = 6161;

    /// <summary>
    /// The identifier for the Sensor_60_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_60_connect = 6261;

    /// <summary>
    /// The identifier for the Sensor_60_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_60_offline_time = 6361;

    /// <summary>
    /// The identifier for the Sensor_61 Variable.
    /// </summary>
    public const uint WTMS_Sensor_61 = 6162;

    /// <summary>
    /// The identifier for the Sensor_61_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_61_connect = 6262;

    /// <summary>
    /// The identifier for the Sensor_61_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_61_offline_time = 6362;

    /// <summary>
    /// The identifier for the Sensor_62 Variable.
    /// </summary>
    public const uint WTMS_Sensor_62 = 6163;

    /// <summary>
    /// The identifier for the Sensor_62_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_62_connect = 6263;

    /// <summary>
    /// The identifier for the Sensor_62_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_62_offline_time = 6363;

    /// <summary>
    /// The identifier for the Sensor_63 Variable.
    /// </summary>
    public const uint WTMS_Sensor_63 = 6164;

    /// <summary>
    /// The identifier for the Sensor_63_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_63_connect = 6264;

    /// <summary>
    /// The identifier for the Sensor_63_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_63_offline_time = 6364;

    /// <summary>
    /// The identifier for the Sensor_64 Variable.
    /// </summary>
    public const uint WTMS_Sensor_64 = 6165;

    /// <summary>
    /// The identifier for the Sensor_64_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_64_connect = 6265;

    /// <summary>
    /// The identifier for the Sensor_64_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_64_offline_time = 6365;

    /// <summary>
    /// The identifier for the Sensor_65 Variable.
    /// </summary>
    public const uint WTMS_Sensor_65 = 6166;

    /// <summary>
    /// The identifier for the Sensor_65_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_65_connect = 6266;

    /// <summary>
    /// The identifier for the Sensor_65_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_65_offline_time = 6366;

    /// <summary>
    /// The identifier for the Sensor_66 Variable.
    /// </summary>
    public const uint WTMS_Sensor_66 = 6167;

    /// <summary>
    /// The identifier for the Sensor_66_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_66_connect = 6267;

    /// <summary>
    /// The identifier for the Sensor_66_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_66_offline_time = 6367;

    /// <summary>
    /// The identifier for the Sensor_67 Variable.
    /// </summary>
    public const uint WTMS_Sensor_67 = 6168;

    /// <summary>
    /// The identifier for the Sensor_67_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_67_connect = 6268;

    /// <summary>
    /// The identifier for the Sensor_67_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_67_offline_time = 6368;

    /// <summary>
    /// The identifier for the Sensor_68 Variable.
    /// </summary>
    public const uint WTMS_Sensor_68 = 6169;

    /// <summary>
    /// The identifier for the Sensor_68_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_68_connect = 6269;

    /// <summary>
    /// The identifier for the Sensor_68_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_68_offline_time = 6369;

    /// <summary>
    /// The identifier for the Sensor_69 Variable.
    /// </summary>
    public const uint WTMS_Sensor_69 = 6170;

    /// <summary>
    /// The identifier for the Sensor_69_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_69_connect = 6270;

    /// <summary>
    /// The identifier for the Sensor_69_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_69_offline_time = 6370;

    /// <summary>
    /// The identifier for the Sensor_6_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_6_connect = 6207;

    /// <summary>
    /// The identifier for the Sensor_6_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_6_offline_time = 6307;

    /// <summary>
    /// The identifier for the Sensor_7 Variable.
    /// </summary>
    public const uint WTMS_Sensor_7 = 6108;

    /// <summary>
    /// The identifier for the Sensor_70 Variable.
    /// </summary>
    public const uint WTMS_Sensor_70 = 6171;

    /// <summary>
    /// The identifier for the Sensor_70_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_70_connect = 6271;

    /// <summary>
    /// The identifier for the Sensor_70_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_70_offline_time = 6371;

    /// <summary>
    /// The identifier for the Sensor_71 Variable.
    /// </summary>
    public const uint WTMS_Sensor_71 = 6172;

    /// <summary>
    /// The identifier for the Sensor_71_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_71_connect = 6272;

    /// <summary>
    /// The identifier for the Sensor_71_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_71_offline_time = 6372;

    /// <summary>
    /// The identifier for the Sensor_72 Variable.
    /// </summary>
    public const uint WTMS_Sensor_72 = 6173;

    /// <summary>
    /// The identifier for the Sensor_72_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_72_connect = 6273;

    /// <summary>
    /// The identifier for the Sensor_72_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_72_offline_time = 6373;

    /// <summary>
    /// The identifier for the Sensor_73 Variable.
    /// </summary>
    public const uint WTMS_Sensor_73 = 6174;

    /// <summary>
    /// The identifier for the Sensor_73_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_73_connect = 6274;

    /// <summary>
    /// The identifier for the Sensor_73_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_73_offline_time = 6374;

    /// <summary>
    /// The identifier for the Sensor_74 Variable.
    /// </summary>
    public const uint WTMS_Sensor_74 = 6175;

    /// <summary>
    /// The identifier for the Sensor_74_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_74_connect = 6275;

    /// <summary>
    /// The identifier for the Sensor_74_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_74_offline_time = 6375;

    /// <summary>
    /// The identifier for the Sensor_75 Variable.
    /// </summary>
    public const uint WTMS_Sensor_75 = 6176;

    /// <summary>
    /// The identifier for the Sensor_75_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_75_connect = 6276;

    /// <summary>
    /// The identifier for the Sensor_75_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_75_offline_time = 6376;

    /// <summary>
    /// The identifier for the Sensor_76 Variable.
    /// </summary>
    public const uint WTMS_Sensor_76 = 6177;

    /// <summary>
    /// The identifier for the Sensor_76_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_76_connect = 6277;

    /// <summary>
    /// The identifier for the Sensor_76_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_76_offline_time = 6377;

    /// <summary>
    /// The identifier for the Sensor_77 Variable.
    /// </summary>
    public const uint WTMS_Sensor_77 = 6178;

    /// <summary>
    /// The identifier for the Sensor_77_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_77_connect = 6278;

    /// <summary>
    /// The identifier for the Sensor_77_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_77_offline_time = 6378;

    /// <summary>
    /// The identifier for the Sensor_78 Variable.
    /// </summary>
    public const uint WTMS_Sensor_78 = 6179;

    /// <summary>
    /// The identifier for the Sensor_78_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_78_connect = 6279;

    /// <summary>
    /// The identifier for the Sensor_78_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_78_offline_time = 6379;

    /// <summary>
    /// The identifier for the Sensor_79 Variable.
    /// </summary>
    public const uint WTMS_Sensor_79 = 6180;

    /// <summary>
    /// The identifier for the Sensor_79_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_79_connect = 6280;

    /// <summary>
    /// The identifier for the Sensor_79_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_79_offline_time = 6380;

    /// <summary>
    /// The identifier for the Sensor_7_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_7_connect = 6208;

    /// <summary>
    /// The identifier for the Sensor_7_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_7_offline_time = 6308;

    /// <summary>
    /// The identifier for the Sensor_8 Variable.
    /// </summary>
    public const uint WTMS_Sensor_8 = 6109;

    /// <summary>
    /// The identifier for the Sensor_80 Variable.
    /// </summary>
    public const uint WTMS_Sensor_80 = 6181;

    /// <summary>
    /// The identifier for the Sensor_80_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_80_connect = 6281;

    /// <summary>
    /// The identifier for the Sensor_80_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_80_offline_time = 6381;

    /// <summary>
    /// The identifier for the Sensor_81 Variable.
    /// </summary>
    public const uint WTMS_Sensor_81 = 6182;

    /// <summary>
    /// The identifier for the Sensor_81_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_81_connect = 6282;

    /// <summary>
    /// The identifier for the Sensor_81_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_81_offline_time = 6382;

    /// <summary>
    /// The identifier for the Sensor_82 Variable.
    /// </summary>
    public const uint WTMS_Sensor_82 = 6183;

    /// <summary>
    /// The identifier for the Sensor_82_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_82_connect = 6283;

    /// <summary>
    /// The identifier for the Sensor_82_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_82_offline_time = 6383;

    /// <summary>
    /// The identifier for the Sensor_83 Variable.
    /// </summary>
    public const uint WTMS_Sensor_83 = 6184;

    /// <summary>
    /// The identifier for the Sensor_83_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_83_connect = 6284;

    /// <summary>
    /// The identifier for the Sensor_83_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_83_offline_time = 6384;

    /// <summary>
    /// The identifier for the Sensor_84 Variable.
    /// </summary>
    public const uint WTMS_Sensor_84 = 6185;

    /// <summary>
    /// The identifier for the Sensor_84_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_84_connect = 6285;

    /// <summary>
    /// The identifier for the Sensor_84_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_84_offline_time = 6385;

    /// <summary>
    /// The identifier for the Sensor_85 Variable.
    /// </summary>
    public const uint WTMS_Sensor_85 = 6186;

    /// <summary>
    /// The identifier for the Sensor_85_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_85_connect = 6286;

    /// <summary>
    /// The identifier for the Sensor_85_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_85_offline_time = 6386;

    /// <summary>
    /// The identifier for the Sensor_86 Variable.
    /// </summary>
    public const uint WTMS_Sensor_86 = 6187;

    /// <summary>
    /// The identifier for the Sensor_86_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_86_connect = 6287;

    /// <summary>
    /// The identifier for the Sensor_86_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_86_offline_time = 6387;

    /// <summary>
    /// The identifier for the Sensor_87 Variable.
    /// </summary>
    public const uint WTMS_Sensor_87 = 6188;

    /// <summary>
    /// The identifier for the Sensor_87_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_87_connect = 6288;

    /// <summary>
    /// The identifier for the Sensor_87_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_87_offline_time = 6388;

    /// <summary>
    /// The identifier for the Sensor_88 Variable.
    /// </summary>
    public const uint WTMS_Sensor_88 = 6189;

    /// <summary>
    /// The identifier for the Sensor_88_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_88_connect = 6289;

    /// <summary>
    /// The identifier for the Sensor_88_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_88_offline_time = 6389;

    /// <summary>
    /// The identifier for the Sensor_89 Variable.
    /// </summary>
    public const uint WTMS_Sensor_89 = 6190;

    /// <summary>
    /// The identifier for the Sensor_89_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_89_connect = 6290;

    /// <summary>
    /// The identifier for the Sensor_89_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_89_offline_time = 6390;

    /// <summary>
    /// The identifier for the Sensor_8_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_8_connect = 6209;

    /// <summary>
    /// The identifier for the Sensor_8_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_8_offline_time = 6309;

    /// <summary>
    /// The identifier for the Sensor_9 Variable.
    /// </summary>
    public const uint WTMS_Sensor_9 = 6110;

    /// <summary>
    /// The identifier for the Sensor_90 Variable.
    /// </summary>
    public const uint WTMS_Sensor_90 = 6191;

    /// <summary>
    /// The identifier for the Sensor_90_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_90_connect = 6291;

    /// <summary>
    /// The identifier for the Sensor_90_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_90_offline_time = 6391;

    /// <summary>
    /// The identifier for the Sensor_91 Variable.
    /// </summary>
    public const uint WTMS_Sensor_91 = 6192;

    /// <summary>
    /// The identifier for the Sensor_91_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_91_connect = 6292;

    /// <summary>
    /// The identifier for the Sensor_91_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_91_offline_time = 6392;

    /// <summary>
    /// The identifier for the Sensor_92 Variable.
    /// </summary>
    public const uint WTMS_Sensor_92 = 6193;

    /// <summary>
    /// The identifier for the Sensor_92_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_92_connect = 6293;

    /// <summary>
    /// The identifier for the Sensor_92_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_92_offline_time = 6393;

    /// <summary>
    /// The identifier for the Sensor_93 Variable.
    /// </summary>
    public const uint WTMS_Sensor_93 = 6194;

    /// <summary>
    /// The identifier for the Sensor_93_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_93_connect = 6294;

    /// <summary>
    /// The identifier for the Sensor_93_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_93_offline_time = 6394;

    /// <summary>
    /// The identifier for the Sensor_94 Variable.
    /// </summary>
    public const uint WTMS_Sensor_94 = 6195;

    /// <summary>
    /// The identifier for the Sensor_94_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_94_connect = 6295;

    /// <summary>
    /// The identifier for the Sensor_94_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_94_offline_time = 6395;

    /// <summary>
    /// The identifier for the Sensor_95 Variable.
    /// </summary>
    public const uint WTMS_Sensor_95 = 6196;

    /// <summary>
    /// The identifier for the Sensor_95_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_95_connect = 6296;

    /// <summary>
    /// The identifier for the Sensor_95_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_95_offline_time = 6396;

    /// <summary>
    /// The identifier for the Sensor_96 Variable.
    /// </summary>
    public const uint WTMS_Sensor_96 = 6197;

    /// <summary>
    /// The identifier for the Sensor_96_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_96_connect = 6297;

    /// <summary>
    /// The identifier for the Sensor_96_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_96_offline_time = 6397;

    /// <summary>
    /// The identifier for the Sensor_97 Variable.
    /// </summary>
    public const uint WTMS_Sensor_97 = 6198;

    /// <summary>
    /// The identifier for the Sensor_97_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_97_connect = 6298;

    /// <summary>
    /// The identifier for the Sensor_97_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_97_offline_time = 6398;

    /// <summary>
    /// The identifier for the Sensor_98 Variable.
    /// </summary>
    public const uint WTMS_Sensor_98 = 6199;

    /// <summary>
    /// The identifier for the Sensor_98_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_98_connect = 6299;

    /// <summary>
    /// The identifier for the Sensor_98_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_98_offline_time = 6399;

    /// <summary>
    /// The identifier for the Sensor_99 Variable.
    /// </summary>
    public const uint WTMS_Sensor_99 = 6200;

    /// <summary>
    /// The identifier for the Sensor_99_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_99_connect = 6300;

    /// <summary>
    /// The identifier for the Sensor_99_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_99_offline_time = 6400;

    /// <summary>
    /// The identifier for the Sensor_9_connect Variable.
    /// </summary>
    public const uint WTMS_Sensor_9_connect = 6210;

    /// <summary>
    /// The identifier for the Sensor_9_offline_time Variable.
    /// </summary>
    public const uint WTMS_Sensor_9_offline_time = 6310;

    /// <summary>
    /// The identifier for the IsNamespaceSubset Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_WTMS__IsNamespaceSubset = 6002;

    /// <summary>
    /// The identifier for the NamespacePublicationDate Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_WTMS__NamespacePublicationDate = 6003;

    /// <summary>
    /// The identifier for the NamespaceUri Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_WTMS__NamespaceUri = 6004;

    /// <summary>
    /// The identifier for the NamespaceVersion Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_WTMS__NamespaceVersion = 6005;

    /// <summary>
    /// The identifier for the StaticNodeIdTypes Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_WTMS__StaticNodeIdTypes = 6006;

    /// <summary>
    /// The identifier for the StaticNumericNodeIdRange Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_WTMS__StaticNumericNodeIdRange = 6007;

    /// <summary>
    /// The identifier for the StaticStringNodeIdPattern Object.
    /// </summary>
    public const uint Namespaces_http___sentron_org_WTMS__StaticStringNodeIdPattern = 6008;

}
#endregion

#region VariableTypes Identifiers
/// <summary>
/// A class that declares constants for all VariableTypes in the Model.
/// </summary>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypes
{
}
#endregion

#region DataType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all DataTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class DataTypeIds
{
}
#endregion

#region Method Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Methods in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class MethodIds
{
}
#endregion

#region Object Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectIds
{
    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_WTMS_ Object.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_WTMS_ = new ExpandedNodeId(Objects.Namespaces_http___sentron_org_WTMS_, Namespaces.WTMS);

}
#endregion

#region ObjectType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Objects in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ObjectTypeIds
{
    /// <summary>
    /// The identifier for the WTMS ObjectType.
    /// </summary>
    public static readonly ExpandedNodeId WTMS = new ExpandedNodeId(ObjectTypes.WTMS, Namespaces.WTMS);

}
#endregion

#region ReferenceType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all ReferenceTypes in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class ReferenceTypeIds
{
}
#endregion

#region Variable Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all Variables in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableIds
{
    /// <summary>
    /// The identifier for the WTMS_HaveAlarm Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_HaveAlarm = new ExpandedNodeId(Variables.WTMS_HaveAlarm, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_IsConnected Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_IsConnected = new ExpandedNodeId(Variables.WTMS_IsConnected, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_1 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_1 = new ExpandedNodeId(Variables.WTMS_Sensor_1, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_10 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_10 = new ExpandedNodeId(Variables.WTMS_Sensor_10, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_100 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_100 = new ExpandedNodeId(Variables.WTMS_Sensor_100, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_100_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_100_connect = new ExpandedNodeId(Variables.WTMS_Sensor_100_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_100_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_100_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_100_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_10_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_10_connect = new ExpandedNodeId(Variables.WTMS_Sensor_10_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_10_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_10_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_10_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_11 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_11 = new ExpandedNodeId(Variables.WTMS_Sensor_11, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_11_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_11_connect = new ExpandedNodeId(Variables.WTMS_Sensor_11_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_11_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_11_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_11_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_12 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_12 = new ExpandedNodeId(Variables.WTMS_Sensor_12, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_12_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_12_connect = new ExpandedNodeId(Variables.WTMS_Sensor_12_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_12_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_12_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_12_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_13 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_13 = new ExpandedNodeId(Variables.WTMS_Sensor_13, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_13_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_13_connect = new ExpandedNodeId(Variables.WTMS_Sensor_13_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_13_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_13_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_13_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_14 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_14 = new ExpandedNodeId(Variables.WTMS_Sensor_14, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_14_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_14_connect = new ExpandedNodeId(Variables.WTMS_Sensor_14_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_14_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_14_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_14_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_15 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_15 = new ExpandedNodeId(Variables.WTMS_Sensor_15, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_15_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_15_connect = new ExpandedNodeId(Variables.WTMS_Sensor_15_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_15_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_15_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_15_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_16 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_16 = new ExpandedNodeId(Variables.WTMS_Sensor_16, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_16_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_16_connect = new ExpandedNodeId(Variables.WTMS_Sensor_16_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_16_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_16_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_16_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_17 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_17 = new ExpandedNodeId(Variables.WTMS_Sensor_17, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_17_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_17_connect = new ExpandedNodeId(Variables.WTMS_Sensor_17_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_17_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_17_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_17_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_18 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_18 = new ExpandedNodeId(Variables.WTMS_Sensor_18, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_18_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_18_connect = new ExpandedNodeId(Variables.WTMS_Sensor_18_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_18_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_18_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_18_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_19 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_19 = new ExpandedNodeId(Variables.WTMS_Sensor_19, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_19_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_19_connect = new ExpandedNodeId(Variables.WTMS_Sensor_19_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_19_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_19_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_19_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_1_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_1_connect = new ExpandedNodeId(Variables.WTMS_Sensor_1_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_1_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_1_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_1_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_2 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_2 = new ExpandedNodeId(Variables.WTMS_Sensor_2, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_20 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_20 = new ExpandedNodeId(Variables.WTMS_Sensor_20, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_20_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_20_connect = new ExpandedNodeId(Variables.WTMS_Sensor_20_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_20_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_20_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_20_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_21 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_21 = new ExpandedNodeId(Variables.WTMS_Sensor_21, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_21_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_21_connect = new ExpandedNodeId(Variables.WTMS_Sensor_21_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_21_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_21_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_21_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_22 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_22 = new ExpandedNodeId(Variables.WTMS_Sensor_22, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_22_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_22_connect = new ExpandedNodeId(Variables.WTMS_Sensor_22_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_22_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_22_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_22_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_23 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_23 = new ExpandedNodeId(Variables.WTMS_Sensor_23, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_23_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_23_connect = new ExpandedNodeId(Variables.WTMS_Sensor_23_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_23_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_23_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_23_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_24 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_24 = new ExpandedNodeId(Variables.WTMS_Sensor_24, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_24_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_24_connect = new ExpandedNodeId(Variables.WTMS_Sensor_24_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_24_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_24_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_24_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_25 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_25 = new ExpandedNodeId(Variables.WTMS_Sensor_25, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_25_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_25_connect = new ExpandedNodeId(Variables.WTMS_Sensor_25_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_25_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_25_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_25_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_26 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_26 = new ExpandedNodeId(Variables.WTMS_Sensor_26, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_26_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_26_connect = new ExpandedNodeId(Variables.WTMS_Sensor_26_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_26_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_26_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_26_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_27 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_27 = new ExpandedNodeId(Variables.WTMS_Sensor_27, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_27_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_27_connect = new ExpandedNodeId(Variables.WTMS_Sensor_27_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_27_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_27_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_27_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_28 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_28 = new ExpandedNodeId(Variables.WTMS_Sensor_28, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_28_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_28_connect = new ExpandedNodeId(Variables.WTMS_Sensor_28_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_28_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_28_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_28_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_29 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_29 = new ExpandedNodeId(Variables.WTMS_Sensor_29, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_29_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_29_connect = new ExpandedNodeId(Variables.WTMS_Sensor_29_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_29_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_29_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_29_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_2_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_2_connect = new ExpandedNodeId(Variables.WTMS_Sensor_2_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_2_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_2_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_2_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_3 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_3 = new ExpandedNodeId(Variables.WTMS_Sensor_3, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_30 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_30 = new ExpandedNodeId(Variables.WTMS_Sensor_30, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_30_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_30_connect = new ExpandedNodeId(Variables.WTMS_Sensor_30_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_30_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_30_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_30_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_31 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_31 = new ExpandedNodeId(Variables.WTMS_Sensor_31, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_31_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_31_connect = new ExpandedNodeId(Variables.WTMS_Sensor_31_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_31_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_31_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_31_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_32 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_32 = new ExpandedNodeId(Variables.WTMS_Sensor_32, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_32_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_32_connect = new ExpandedNodeId(Variables.WTMS_Sensor_32_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_32_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_32_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_32_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_33 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_33 = new ExpandedNodeId(Variables.WTMS_Sensor_33, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_33_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_33_connect = new ExpandedNodeId(Variables.WTMS_Sensor_33_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_33_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_33_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_33_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_34 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_34 = new ExpandedNodeId(Variables.WTMS_Sensor_34, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_34_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_34_connect = new ExpandedNodeId(Variables.WTMS_Sensor_34_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_34_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_34_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_34_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_35 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_35 = new ExpandedNodeId(Variables.WTMS_Sensor_35, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_35_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_35_connect = new ExpandedNodeId(Variables.WTMS_Sensor_35_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_35_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_35_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_35_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_36 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_36 = new ExpandedNodeId(Variables.WTMS_Sensor_36, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_36_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_36_connect = new ExpandedNodeId(Variables.WTMS_Sensor_36_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_36_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_36_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_36_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_37 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_37 = new ExpandedNodeId(Variables.WTMS_Sensor_37, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_37_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_37_connect = new ExpandedNodeId(Variables.WTMS_Sensor_37_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_37_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_37_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_37_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_38 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_38 = new ExpandedNodeId(Variables.WTMS_Sensor_38, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_38_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_38_connect = new ExpandedNodeId(Variables.WTMS_Sensor_38_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_38_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_38_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_38_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_39 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_39 = new ExpandedNodeId(Variables.WTMS_Sensor_39, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_39_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_39_connect = new ExpandedNodeId(Variables.WTMS_Sensor_39_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_39_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_39_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_39_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_3_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_3_connect = new ExpandedNodeId(Variables.WTMS_Sensor_3_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_3_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_3_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_3_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_4 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_4 = new ExpandedNodeId(Variables.WTMS_Sensor_4, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_40 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_40 = new ExpandedNodeId(Variables.WTMS_Sensor_40, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_40_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_40_connect = new ExpandedNodeId(Variables.WTMS_Sensor_40_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_40_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_40_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_40_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_41 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_41 = new ExpandedNodeId(Variables.WTMS_Sensor_41, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_41_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_41_connect = new ExpandedNodeId(Variables.WTMS_Sensor_41_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_41_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_41_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_41_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_42 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_42 = new ExpandedNodeId(Variables.WTMS_Sensor_42, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_42_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_42_connect = new ExpandedNodeId(Variables.WTMS_Sensor_42_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_42_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_42_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_42_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_43 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_43 = new ExpandedNodeId(Variables.WTMS_Sensor_43, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_43_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_43_connect = new ExpandedNodeId(Variables.WTMS_Sensor_43_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_43_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_43_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_43_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_44 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_44 = new ExpandedNodeId(Variables.WTMS_Sensor_44, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_44_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_44_connect = new ExpandedNodeId(Variables.WTMS_Sensor_44_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_44_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_44_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_44_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_45 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_45 = new ExpandedNodeId(Variables.WTMS_Sensor_45, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_45_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_45_connect = new ExpandedNodeId(Variables.WTMS_Sensor_45_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_45_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_45_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_45_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_46 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_46 = new ExpandedNodeId(Variables.WTMS_Sensor_46, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_46_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_46_connect = new ExpandedNodeId(Variables.WTMS_Sensor_46_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_46_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_46_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_46_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_47 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_47 = new ExpandedNodeId(Variables.WTMS_Sensor_47, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_47_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_47_connect = new ExpandedNodeId(Variables.WTMS_Sensor_47_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_47_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_47_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_47_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_48 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_48 = new ExpandedNodeId(Variables.WTMS_Sensor_48, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_48_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_48_connect = new ExpandedNodeId(Variables.WTMS_Sensor_48_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_48_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_48_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_48_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_49 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_49 = new ExpandedNodeId(Variables.WTMS_Sensor_49, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_49_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_49_connect = new ExpandedNodeId(Variables.WTMS_Sensor_49_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_49_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_49_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_49_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_4_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_4_connect = new ExpandedNodeId(Variables.WTMS_Sensor_4_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_4_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_4_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_4_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_5 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_5 = new ExpandedNodeId(Variables.WTMS_Sensor_5, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_50 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_50 = new ExpandedNodeId(Variables.WTMS_Sensor_50, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_50_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_50_connect = new ExpandedNodeId(Variables.WTMS_Sensor_50_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_50_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_50_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_50_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_51 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_51 = new ExpandedNodeId(Variables.WTMS_Sensor_51, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_51_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_51_connect = new ExpandedNodeId(Variables.WTMS_Sensor_51_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_51_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_51_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_51_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_52 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_52 = new ExpandedNodeId(Variables.WTMS_Sensor_52, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_52_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_52_connect = new ExpandedNodeId(Variables.WTMS_Sensor_52_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_52_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_52_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_52_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_53 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_53 = new ExpandedNodeId(Variables.WTMS_Sensor_53, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_53_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_53_connect = new ExpandedNodeId(Variables.WTMS_Sensor_53_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_53_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_53_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_53_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_54 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_54 = new ExpandedNodeId(Variables.WTMS_Sensor_54, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_54_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_54_connect = new ExpandedNodeId(Variables.WTMS_Sensor_54_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_54_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_54_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_54_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_55 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_55 = new ExpandedNodeId(Variables.WTMS_Sensor_55, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_55_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_55_connect = new ExpandedNodeId(Variables.WTMS_Sensor_55_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_55_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_55_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_55_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_56 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_56 = new ExpandedNodeId(Variables.WTMS_Sensor_56, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_56_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_56_connect = new ExpandedNodeId(Variables.WTMS_Sensor_56_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_56_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_56_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_56_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_57 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_57 = new ExpandedNodeId(Variables.WTMS_Sensor_57, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_57_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_57_connect = new ExpandedNodeId(Variables.WTMS_Sensor_57_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_57_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_57_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_57_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_58 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_58 = new ExpandedNodeId(Variables.WTMS_Sensor_58, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_58_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_58_connect = new ExpandedNodeId(Variables.WTMS_Sensor_58_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_58_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_58_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_58_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_59 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_59 = new ExpandedNodeId(Variables.WTMS_Sensor_59, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_59_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_59_connect = new ExpandedNodeId(Variables.WTMS_Sensor_59_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_59_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_59_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_59_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_5_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_5_connect = new ExpandedNodeId(Variables.WTMS_Sensor_5_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_5_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_5_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_5_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_6 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_6 = new ExpandedNodeId(Variables.WTMS_Sensor_6, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_60 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_60 = new ExpandedNodeId(Variables.WTMS_Sensor_60, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_60_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_60_connect = new ExpandedNodeId(Variables.WTMS_Sensor_60_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_60_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_60_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_60_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_61 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_61 = new ExpandedNodeId(Variables.WTMS_Sensor_61, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_61_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_61_connect = new ExpandedNodeId(Variables.WTMS_Sensor_61_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_61_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_61_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_61_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_62 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_62 = new ExpandedNodeId(Variables.WTMS_Sensor_62, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_62_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_62_connect = new ExpandedNodeId(Variables.WTMS_Sensor_62_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_62_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_62_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_62_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_63 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_63 = new ExpandedNodeId(Variables.WTMS_Sensor_63, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_63_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_63_connect = new ExpandedNodeId(Variables.WTMS_Sensor_63_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_63_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_63_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_63_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_64 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_64 = new ExpandedNodeId(Variables.WTMS_Sensor_64, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_64_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_64_connect = new ExpandedNodeId(Variables.WTMS_Sensor_64_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_64_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_64_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_64_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_65 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_65 = new ExpandedNodeId(Variables.WTMS_Sensor_65, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_65_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_65_connect = new ExpandedNodeId(Variables.WTMS_Sensor_65_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_65_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_65_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_65_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_66 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_66 = new ExpandedNodeId(Variables.WTMS_Sensor_66, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_66_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_66_connect = new ExpandedNodeId(Variables.WTMS_Sensor_66_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_66_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_66_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_66_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_67 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_67 = new ExpandedNodeId(Variables.WTMS_Sensor_67, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_67_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_67_connect = new ExpandedNodeId(Variables.WTMS_Sensor_67_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_67_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_67_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_67_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_68 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_68 = new ExpandedNodeId(Variables.WTMS_Sensor_68, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_68_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_68_connect = new ExpandedNodeId(Variables.WTMS_Sensor_68_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_68_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_68_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_68_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_69 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_69 = new ExpandedNodeId(Variables.WTMS_Sensor_69, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_69_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_69_connect = new ExpandedNodeId(Variables.WTMS_Sensor_69_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_69_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_69_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_69_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_6_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_6_connect = new ExpandedNodeId(Variables.WTMS_Sensor_6_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_6_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_6_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_6_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_7 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_7 = new ExpandedNodeId(Variables.WTMS_Sensor_7, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_70 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_70 = new ExpandedNodeId(Variables.WTMS_Sensor_70, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_70_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_70_connect = new ExpandedNodeId(Variables.WTMS_Sensor_70_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_70_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_70_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_70_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_71 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_71 = new ExpandedNodeId(Variables.WTMS_Sensor_71, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_71_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_71_connect = new ExpandedNodeId(Variables.WTMS_Sensor_71_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_71_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_71_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_71_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_72 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_72 = new ExpandedNodeId(Variables.WTMS_Sensor_72, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_72_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_72_connect = new ExpandedNodeId(Variables.WTMS_Sensor_72_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_72_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_72_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_72_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_73 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_73 = new ExpandedNodeId(Variables.WTMS_Sensor_73, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_73_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_73_connect = new ExpandedNodeId(Variables.WTMS_Sensor_73_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_73_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_73_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_73_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_74 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_74 = new ExpandedNodeId(Variables.WTMS_Sensor_74, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_74_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_74_connect = new ExpandedNodeId(Variables.WTMS_Sensor_74_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_74_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_74_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_74_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_75 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_75 = new ExpandedNodeId(Variables.WTMS_Sensor_75, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_75_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_75_connect = new ExpandedNodeId(Variables.WTMS_Sensor_75_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_75_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_75_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_75_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_76 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_76 = new ExpandedNodeId(Variables.WTMS_Sensor_76, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_76_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_76_connect = new ExpandedNodeId(Variables.WTMS_Sensor_76_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_76_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_76_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_76_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_77 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_77 = new ExpandedNodeId(Variables.WTMS_Sensor_77, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_77_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_77_connect = new ExpandedNodeId(Variables.WTMS_Sensor_77_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_77_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_77_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_77_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_78 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_78 = new ExpandedNodeId(Variables.WTMS_Sensor_78, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_78_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_78_connect = new ExpandedNodeId(Variables.WTMS_Sensor_78_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_78_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_78_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_78_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_79 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_79 = new ExpandedNodeId(Variables.WTMS_Sensor_79, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_79_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_79_connect = new ExpandedNodeId(Variables.WTMS_Sensor_79_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_79_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_79_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_79_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_7_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_7_connect = new ExpandedNodeId(Variables.WTMS_Sensor_7_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_7_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_7_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_7_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_8 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_8 = new ExpandedNodeId(Variables.WTMS_Sensor_8, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_80 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_80 = new ExpandedNodeId(Variables.WTMS_Sensor_80, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_80_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_80_connect = new ExpandedNodeId(Variables.WTMS_Sensor_80_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_80_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_80_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_80_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_81 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_81 = new ExpandedNodeId(Variables.WTMS_Sensor_81, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_81_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_81_connect = new ExpandedNodeId(Variables.WTMS_Sensor_81_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_81_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_81_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_81_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_82 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_82 = new ExpandedNodeId(Variables.WTMS_Sensor_82, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_82_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_82_connect = new ExpandedNodeId(Variables.WTMS_Sensor_82_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_82_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_82_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_82_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_83 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_83 = new ExpandedNodeId(Variables.WTMS_Sensor_83, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_83_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_83_connect = new ExpandedNodeId(Variables.WTMS_Sensor_83_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_83_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_83_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_83_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_84 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_84 = new ExpandedNodeId(Variables.WTMS_Sensor_84, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_84_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_84_connect = new ExpandedNodeId(Variables.WTMS_Sensor_84_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_84_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_84_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_84_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_85 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_85 = new ExpandedNodeId(Variables.WTMS_Sensor_85, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_85_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_85_connect = new ExpandedNodeId(Variables.WTMS_Sensor_85_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_85_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_85_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_85_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_86 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_86 = new ExpandedNodeId(Variables.WTMS_Sensor_86, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_86_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_86_connect = new ExpandedNodeId(Variables.WTMS_Sensor_86_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_86_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_86_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_86_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_87 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_87 = new ExpandedNodeId(Variables.WTMS_Sensor_87, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_87_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_87_connect = new ExpandedNodeId(Variables.WTMS_Sensor_87_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_87_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_87_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_87_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_88 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_88 = new ExpandedNodeId(Variables.WTMS_Sensor_88, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_88_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_88_connect = new ExpandedNodeId(Variables.WTMS_Sensor_88_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_88_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_88_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_88_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_89 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_89 = new ExpandedNodeId(Variables.WTMS_Sensor_89, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_89_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_89_connect = new ExpandedNodeId(Variables.WTMS_Sensor_89_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_89_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_89_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_89_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_8_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_8_connect = new ExpandedNodeId(Variables.WTMS_Sensor_8_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_8_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_8_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_8_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_9 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_9 = new ExpandedNodeId(Variables.WTMS_Sensor_9, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_90 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_90 = new ExpandedNodeId(Variables.WTMS_Sensor_90, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_90_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_90_connect = new ExpandedNodeId(Variables.WTMS_Sensor_90_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_90_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_90_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_90_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_91 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_91 = new ExpandedNodeId(Variables.WTMS_Sensor_91, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_91_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_91_connect = new ExpandedNodeId(Variables.WTMS_Sensor_91_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_91_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_91_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_91_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_92 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_92 = new ExpandedNodeId(Variables.WTMS_Sensor_92, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_92_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_92_connect = new ExpandedNodeId(Variables.WTMS_Sensor_92_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_92_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_92_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_92_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_93 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_93 = new ExpandedNodeId(Variables.WTMS_Sensor_93, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_93_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_93_connect = new ExpandedNodeId(Variables.WTMS_Sensor_93_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_93_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_93_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_93_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_94 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_94 = new ExpandedNodeId(Variables.WTMS_Sensor_94, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_94_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_94_connect = new ExpandedNodeId(Variables.WTMS_Sensor_94_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_94_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_94_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_94_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_95 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_95 = new ExpandedNodeId(Variables.WTMS_Sensor_95, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_95_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_95_connect = new ExpandedNodeId(Variables.WTMS_Sensor_95_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_95_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_95_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_95_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_96 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_96 = new ExpandedNodeId(Variables.WTMS_Sensor_96, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_96_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_96_connect = new ExpandedNodeId(Variables.WTMS_Sensor_96_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_96_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_96_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_96_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_97 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_97 = new ExpandedNodeId(Variables.WTMS_Sensor_97, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_97_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_97_connect = new ExpandedNodeId(Variables.WTMS_Sensor_97_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_97_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_97_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_97_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_98 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_98 = new ExpandedNodeId(Variables.WTMS_Sensor_98, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_98_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_98_connect = new ExpandedNodeId(Variables.WTMS_Sensor_98_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_98_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_98_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_98_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_99 Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_99 = new ExpandedNodeId(Variables.WTMS_Sensor_99, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_99_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_99_connect = new ExpandedNodeId(Variables.WTMS_Sensor_99_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_99_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_99_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_99_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_9_connect Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_9_connect = new ExpandedNodeId(Variables.WTMS_Sensor_9_connect, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the WTMS_Sensor_9_offline_time Variable.
    /// </summary>
    public static readonly ExpandedNodeId WTMS_Sensor_9_offline_time = new ExpandedNodeId(Variables.WTMS_Sensor_9_offline_time, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_WTMS__IsNamespaceSubset Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_WTMS__IsNamespaceSubset = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_WTMS__IsNamespaceSubset, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_WTMS__NamespacePublicationDate Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_WTMS__NamespacePublicationDate = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_WTMS__NamespacePublicationDate, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_WTMS__NamespaceUri Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_WTMS__NamespaceUri = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_WTMS__NamespaceUri, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_WTMS__NamespaceVersion Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_WTMS__NamespaceVersion = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_WTMS__NamespaceVersion, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_WTMS__StaticNodeIdTypes Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_WTMS__StaticNodeIdTypes = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_WTMS__StaticNodeIdTypes, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_WTMS__StaticNumericNodeIdRange Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_WTMS__StaticNumericNodeIdRange = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_WTMS__StaticNumericNodeIdRange, Namespaces.WTMS);

    /// <summary>
    /// The identifier for the Namespaces_http___sentron_org_WTMS__StaticStringNodeIdPattern Variable.
    /// </summary>
    public static readonly ExpandedNodeId Namespaces_http___sentron_org_WTMS__StaticStringNodeIdPattern = new ExpandedNodeId(Variables.Namespaces_http___sentron_org_WTMS__StaticStringNodeIdPattern, Namespaces.WTMS);

}
#endregion

#region VariableType Node Identifiers
/// <summary>
/// A class that declares ExpandedNodeIds for all VariableType in the Model.
/// </summary>
/// <remarks>
/// Call ExpandedNodeId.ToNodeId to use these constants.
/// </remarks>
[System.CodeDom.Compiler.GeneratedCodeAttribute("UaModeler", "1.6.7")]
public static partial class VariableTypeIds
{
}
#endregion

#region BrowseName Declarations
/// <summary>
/// Declares all of the BrowseNames used in the Model.
/// </summary>
public static partial class BrowseNames
{
    /// <summary>
    /// The BrowseName for the HaveAlarm component.
    /// </summary>
    public const string HaveAlarm = "HaveAlarm";
    /// <summary>
    /// The BrowseName for the IsConnected component.
    /// </summary>
    public const string IsConnected = "IsConnected";
    /// <summary>
    /// The BrowseName for the Sensor_1 component.
    /// </summary>
    public const string Sensor1 = "Sensor_1";
    /// <summary>
    /// The BrowseName for the Sensor_10 component.
    /// </summary>
    public const string Sensor10 = "Sensor_10";
    /// <summary>
    /// The BrowseName for the Sensor_100 component.
    /// </summary>
    public const string Sensor100 = "Sensor_100";
    /// <summary>
    /// The BrowseName for the Sensor_100_connect component.
    /// </summary>
    public const string Sensor100Connect = "Sensor_100_connect";
    /// <summary>
    /// The BrowseName for the Sensor_100_offline_time component.
    /// </summary>
    public const string Sensor100OfflineTime = "Sensor_100_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_10_connect component.
    /// </summary>
    public const string Sensor10Connect = "Sensor_10_connect";
    /// <summary>
    /// The BrowseName for the Sensor_10_offline_time component.
    /// </summary>
    public const string Sensor10OfflineTime = "Sensor_10_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_11 component.
    /// </summary>
    public const string Sensor11 = "Sensor_11";
    /// <summary>
    /// The BrowseName for the Sensor_11_connect component.
    /// </summary>
    public const string Sensor11Connect = "Sensor_11_connect";
    /// <summary>
    /// The BrowseName for the Sensor_11_offline_time component.
    /// </summary>
    public const string Sensor11OfflineTime = "Sensor_11_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_12 component.
    /// </summary>
    public const string Sensor12 = "Sensor_12";
    /// <summary>
    /// The BrowseName for the Sensor_12_connect component.
    /// </summary>
    public const string Sensor12Connect = "Sensor_12_connect";
    /// <summary>
    /// The BrowseName for the Sensor_12_offline_time component.
    /// </summary>
    public const string Sensor12OfflineTime = "Sensor_12_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_13 component.
    /// </summary>
    public const string Sensor13 = "Sensor_13";
    /// <summary>
    /// The BrowseName for the Sensor_13_connect component.
    /// </summary>
    public const string Sensor13Connect = "Sensor_13_connect";
    /// <summary>
    /// The BrowseName for the Sensor_13_offline_time component.
    /// </summary>
    public const string Sensor13OfflineTime = "Sensor_13_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_14 component.
    /// </summary>
    public const string Sensor14 = "Sensor_14";
    /// <summary>
    /// The BrowseName for the Sensor_14_connect component.
    /// </summary>
    public const string Sensor14Connect = "Sensor_14_connect";
    /// <summary>
    /// The BrowseName for the Sensor_14_offline_time component.
    /// </summary>
    public const string Sensor14OfflineTime = "Sensor_14_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_15 component.
    /// </summary>
    public const string Sensor15 = "Sensor_15";
    /// <summary>
    /// The BrowseName for the Sensor_15_connect component.
    /// </summary>
    public const string Sensor15Connect = "Sensor_15_connect";
    /// <summary>
    /// The BrowseName for the Sensor_15_offline_time component.
    /// </summary>
    public const string Sensor15OfflineTime = "Sensor_15_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_16 component.
    /// </summary>
    public const string Sensor16 = "Sensor_16";
    /// <summary>
    /// The BrowseName for the Sensor_16_connect component.
    /// </summary>
    public const string Sensor16Connect = "Sensor_16_connect";
    /// <summary>
    /// The BrowseName for the Sensor_16_offline_time component.
    /// </summary>
    public const string Sensor16OfflineTime = "Sensor_16_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_17 component.
    /// </summary>
    public const string Sensor17 = "Sensor_17";
    /// <summary>
    /// The BrowseName for the Sensor_17_connect component.
    /// </summary>
    public const string Sensor17Connect = "Sensor_17_connect";
    /// <summary>
    /// The BrowseName for the Sensor_17_offline_time component.
    /// </summary>
    public const string Sensor17OfflineTime = "Sensor_17_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_18 component.
    /// </summary>
    public const string Sensor18 = "Sensor_18";
    /// <summary>
    /// The BrowseName for the Sensor_18_connect component.
    /// </summary>
    public const string Sensor18Connect = "Sensor_18_connect";
    /// <summary>
    /// The BrowseName for the Sensor_18_offline_time component.
    /// </summary>
    public const string Sensor18OfflineTime = "Sensor_18_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_19 component.
    /// </summary>
    public const string Sensor19 = "Sensor_19";
    /// <summary>
    /// The BrowseName for the Sensor_19_connect component.
    /// </summary>
    public const string Sensor19Connect = "Sensor_19_connect";
    /// <summary>
    /// The BrowseName for the Sensor_19_offline_time component.
    /// </summary>
    public const string Sensor19OfflineTime = "Sensor_19_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_1_connect component.
    /// </summary>
    public const string Sensor1Connect = "Sensor_1_connect";
    /// <summary>
    /// The BrowseName for the Sensor_1_offline_time component.
    /// </summary>
    public const string Sensor1OfflineTime = "Sensor_1_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_2 component.
    /// </summary>
    public const string Sensor2 = "Sensor_2";
    /// <summary>
    /// The BrowseName for the Sensor_20 component.
    /// </summary>
    public const string Sensor20 = "Sensor_20";
    /// <summary>
    /// The BrowseName for the Sensor_20_connect component.
    /// </summary>
    public const string Sensor20Connect = "Sensor_20_connect";
    /// <summary>
    /// The BrowseName for the Sensor_20_offline_time component.
    /// </summary>
    public const string Sensor20OfflineTime = "Sensor_20_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_21 component.
    /// </summary>
    public const string Sensor21 = "Sensor_21";
    /// <summary>
    /// The BrowseName for the Sensor_21_connect component.
    /// </summary>
    public const string Sensor21Connect = "Sensor_21_connect";
    /// <summary>
    /// The BrowseName for the Sensor_21_offline_time component.
    /// </summary>
    public const string Sensor21OfflineTime = "Sensor_21_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_22 component.
    /// </summary>
    public const string Sensor22 = "Sensor_22";
    /// <summary>
    /// The BrowseName for the Sensor_22_connect component.
    /// </summary>
    public const string Sensor22Connect = "Sensor_22_connect";
    /// <summary>
    /// The BrowseName for the Sensor_22_offline_time component.
    /// </summary>
    public const string Sensor22OfflineTime = "Sensor_22_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_23 component.
    /// </summary>
    public const string Sensor23 = "Sensor_23";
    /// <summary>
    /// The BrowseName for the Sensor_23_connect component.
    /// </summary>
    public const string Sensor23Connect = "Sensor_23_connect";
    /// <summary>
    /// The BrowseName for the Sensor_23_offline_time component.
    /// </summary>
    public const string Sensor23OfflineTime = "Sensor_23_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_24 component.
    /// </summary>
    public const string Sensor24 = "Sensor_24";
    /// <summary>
    /// The BrowseName for the Sensor_24_connect component.
    /// </summary>
    public const string Sensor24Connect = "Sensor_24_connect";
    /// <summary>
    /// The BrowseName for the Sensor_24_offline_time component.
    /// </summary>
    public const string Sensor24OfflineTime = "Sensor_24_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_25 component.
    /// </summary>
    public const string Sensor25 = "Sensor_25";
    /// <summary>
    /// The BrowseName for the Sensor_25_connect component.
    /// </summary>
    public const string Sensor25Connect = "Sensor_25_connect";
    /// <summary>
    /// The BrowseName for the Sensor_25_offline_time component.
    /// </summary>
    public const string Sensor25OfflineTime = "Sensor_25_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_26 component.
    /// </summary>
    public const string Sensor26 = "Sensor_26";
    /// <summary>
    /// The BrowseName for the Sensor_26_connect component.
    /// </summary>
    public const string Sensor26Connect = "Sensor_26_connect";
    /// <summary>
    /// The BrowseName for the Sensor_26_offline_time component.
    /// </summary>
    public const string Sensor26OfflineTime = "Sensor_26_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_27 component.
    /// </summary>
    public const string Sensor27 = "Sensor_27";
    /// <summary>
    /// The BrowseName for the Sensor_27_connect component.
    /// </summary>
    public const string Sensor27Connect = "Sensor_27_connect";
    /// <summary>
    /// The BrowseName for the Sensor_27_offline_time component.
    /// </summary>
    public const string Sensor27OfflineTime = "Sensor_27_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_28 component.
    /// </summary>
    public const string Sensor28 = "Sensor_28";
    /// <summary>
    /// The BrowseName for the Sensor_28_connect component.
    /// </summary>
    public const string Sensor28Connect = "Sensor_28_connect";
    /// <summary>
    /// The BrowseName for the Sensor_28_offline_time component.
    /// </summary>
    public const string Sensor28OfflineTime = "Sensor_28_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_29 component.
    /// </summary>
    public const string Sensor29 = "Sensor_29";
    /// <summary>
    /// The BrowseName for the Sensor_29_connect component.
    /// </summary>
    public const string Sensor29Connect = "Sensor_29_connect";
    /// <summary>
    /// The BrowseName for the Sensor_29_offline_time component.
    /// </summary>
    public const string Sensor29OfflineTime = "Sensor_29_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_2_connect component.
    /// </summary>
    public const string Sensor2Connect = "Sensor_2_connect";
    /// <summary>
    /// The BrowseName for the Sensor_2_offline_time component.
    /// </summary>
    public const string Sensor2OfflineTime = "Sensor_2_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_3 component.
    /// </summary>
    public const string Sensor3 = "Sensor_3";
    /// <summary>
    /// The BrowseName for the Sensor_30 component.
    /// </summary>
    public const string Sensor30 = "Sensor_30";
    /// <summary>
    /// The BrowseName for the Sensor_30_connect component.
    /// </summary>
    public const string Sensor30Connect = "Sensor_30_connect";
    /// <summary>
    /// The BrowseName for the Sensor_30_offline_time component.
    /// </summary>
    public const string Sensor30OfflineTime = "Sensor_30_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_31 component.
    /// </summary>
    public const string Sensor31 = "Sensor_31";
    /// <summary>
    /// The BrowseName for the Sensor_31_connect component.
    /// </summary>
    public const string Sensor31Connect = "Sensor_31_connect";
    /// <summary>
    /// The BrowseName for the Sensor_31_offline_time component.
    /// </summary>
    public const string Sensor31OfflineTime = "Sensor_31_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_32 component.
    /// </summary>
    public const string Sensor32 = "Sensor_32";
    /// <summary>
    /// The BrowseName for the Sensor_32_connect component.
    /// </summary>
    public const string Sensor32Connect = "Sensor_32_connect";
    /// <summary>
    /// The BrowseName for the Sensor_32_offline_time component.
    /// </summary>
    public const string Sensor32OfflineTime = "Sensor_32_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_33 component.
    /// </summary>
    public const string Sensor33 = "Sensor_33";
    /// <summary>
    /// The BrowseName for the Sensor_33_connect component.
    /// </summary>
    public const string Sensor33Connect = "Sensor_33_connect";
    /// <summary>
    /// The BrowseName for the Sensor_33_offline_time component.
    /// </summary>
    public const string Sensor33OfflineTime = "Sensor_33_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_34 component.
    /// </summary>
    public const string Sensor34 = "Sensor_34";
    /// <summary>
    /// The BrowseName for the Sensor_34_connect component.
    /// </summary>
    public const string Sensor34Connect = "Sensor_34_connect";
    /// <summary>
    /// The BrowseName for the Sensor_34_offline_time component.
    /// </summary>
    public const string Sensor34OfflineTime = "Sensor_34_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_35 component.
    /// </summary>
    public const string Sensor35 = "Sensor_35";
    /// <summary>
    /// The BrowseName for the Sensor_35_connect component.
    /// </summary>
    public const string Sensor35Connect = "Sensor_35_connect";
    /// <summary>
    /// The BrowseName for the Sensor_35_offline_time component.
    /// </summary>
    public const string Sensor35OfflineTime = "Sensor_35_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_36 component.
    /// </summary>
    public const string Sensor36 = "Sensor_36";
    /// <summary>
    /// The BrowseName for the Sensor_36_connect component.
    /// </summary>
    public const string Sensor36Connect = "Sensor_36_connect";
    /// <summary>
    /// The BrowseName for the Sensor_36_offline_time component.
    /// </summary>
    public const string Sensor36OfflineTime = "Sensor_36_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_37 component.
    /// </summary>
    public const string Sensor37 = "Sensor_37";
    /// <summary>
    /// The BrowseName for the Sensor_37_connect component.
    /// </summary>
    public const string Sensor37Connect = "Sensor_37_connect";
    /// <summary>
    /// The BrowseName for the Sensor_37_offline_time component.
    /// </summary>
    public const string Sensor37OfflineTime = "Sensor_37_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_38 component.
    /// </summary>
    public const string Sensor38 = "Sensor_38";
    /// <summary>
    /// The BrowseName for the Sensor_38_connect component.
    /// </summary>
    public const string Sensor38Connect = "Sensor_38_connect";
    /// <summary>
    /// The BrowseName for the Sensor_38_offline_time component.
    /// </summary>
    public const string Sensor38OfflineTime = "Sensor_38_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_39 component.
    /// </summary>
    public const string Sensor39 = "Sensor_39";
    /// <summary>
    /// The BrowseName for the Sensor_39_connect component.
    /// </summary>
    public const string Sensor39Connect = "Sensor_39_connect";
    /// <summary>
    /// The BrowseName for the Sensor_39_offline_time component.
    /// </summary>
    public const string Sensor39OfflineTime = "Sensor_39_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_3_connect component.
    /// </summary>
    public const string Sensor3Connect = "Sensor_3_connect";
    /// <summary>
    /// The BrowseName for the Sensor_3_offline_time component.
    /// </summary>
    public const string Sensor3OfflineTime = "Sensor_3_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_4 component.
    /// </summary>
    public const string Sensor4 = "Sensor_4";
    /// <summary>
    /// The BrowseName for the Sensor_40 component.
    /// </summary>
    public const string Sensor40 = "Sensor_40";
    /// <summary>
    /// The BrowseName for the Sensor_40_connect component.
    /// </summary>
    public const string Sensor40Connect = "Sensor_40_connect";
    /// <summary>
    /// The BrowseName for the Sensor_40_offline_time component.
    /// </summary>
    public const string Sensor40OfflineTime = "Sensor_40_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_41 component.
    /// </summary>
    public const string Sensor41 = "Sensor_41";
    /// <summary>
    /// The BrowseName for the Sensor_41_connect component.
    /// </summary>
    public const string Sensor41Connect = "Sensor_41_connect";
    /// <summary>
    /// The BrowseName for the Sensor_41_offline_time component.
    /// </summary>
    public const string Sensor41OfflineTime = "Sensor_41_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_42 component.
    /// </summary>
    public const string Sensor42 = "Sensor_42";
    /// <summary>
    /// The BrowseName for the Sensor_42_connect component.
    /// </summary>
    public const string Sensor42Connect = "Sensor_42_connect";
    /// <summary>
    /// The BrowseName for the Sensor_42_offline_time component.
    /// </summary>
    public const string Sensor42OfflineTime = "Sensor_42_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_43 component.
    /// </summary>
    public const string Sensor43 = "Sensor_43";
    /// <summary>
    /// The BrowseName for the Sensor_43_connect component.
    /// </summary>
    public const string Sensor43Connect = "Sensor_43_connect";
    /// <summary>
    /// The BrowseName for the Sensor_43_offline_time component.
    /// </summary>
    public const string Sensor43OfflineTime = "Sensor_43_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_44 component.
    /// </summary>
    public const string Sensor44 = "Sensor_44";
    /// <summary>
    /// The BrowseName for the Sensor_44_connect component.
    /// </summary>
    public const string Sensor44Connect = "Sensor_44_connect";
    /// <summary>
    /// The BrowseName for the Sensor_44_offline_time component.
    /// </summary>
    public const string Sensor44OfflineTime = "Sensor_44_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_45 component.
    /// </summary>
    public const string Sensor45 = "Sensor_45";
    /// <summary>
    /// The BrowseName for the Sensor_45_connect component.
    /// </summary>
    public const string Sensor45Connect = "Sensor_45_connect";
    /// <summary>
    /// The BrowseName for the Sensor_45_offline_time component.
    /// </summary>
    public const string Sensor45OfflineTime = "Sensor_45_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_46 component.
    /// </summary>
    public const string Sensor46 = "Sensor_46";
    /// <summary>
    /// The BrowseName for the Sensor_46_connect component.
    /// </summary>
    public const string Sensor46Connect = "Sensor_46_connect";
    /// <summary>
    /// The BrowseName for the Sensor_46_offline_time component.
    /// </summary>
    public const string Sensor46OfflineTime = "Sensor_46_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_47 component.
    /// </summary>
    public const string Sensor47 = "Sensor_47";
    /// <summary>
    /// The BrowseName for the Sensor_47_connect component.
    /// </summary>
    public const string Sensor47Connect = "Sensor_47_connect";
    /// <summary>
    /// The BrowseName for the Sensor_47_offline_time component.
    /// </summary>
    public const string Sensor47OfflineTime = "Sensor_47_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_48 component.
    /// </summary>
    public const string Sensor48 = "Sensor_48";
    /// <summary>
    /// The BrowseName for the Sensor_48_connect component.
    /// </summary>
    public const string Sensor48Connect = "Sensor_48_connect";
    /// <summary>
    /// The BrowseName for the Sensor_48_offline_time component.
    /// </summary>
    public const string Sensor48OfflineTime = "Sensor_48_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_49 component.
    /// </summary>
    public const string Sensor49 = "Sensor_49";
    /// <summary>
    /// The BrowseName for the Sensor_49_connect component.
    /// </summary>
    public const string Sensor49Connect = "Sensor_49_connect";
    /// <summary>
    /// The BrowseName for the Sensor_49_offline_time component.
    /// </summary>
    public const string Sensor49OfflineTime = "Sensor_49_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_4_connect component.
    /// </summary>
    public const string Sensor4Connect = "Sensor_4_connect";
    /// <summary>
    /// The BrowseName for the Sensor_4_offline_time component.
    /// </summary>
    public const string Sensor4OfflineTime = "Sensor_4_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_5 component.
    /// </summary>
    public const string Sensor5 = "Sensor_5";
    /// <summary>
    /// The BrowseName for the Sensor_50 component.
    /// </summary>
    public const string Sensor50 = "Sensor_50";
    /// <summary>
    /// The BrowseName for the Sensor_50_connect component.
    /// </summary>
    public const string Sensor50Connect = "Sensor_50_connect";
    /// <summary>
    /// The BrowseName for the Sensor_50_offline_time component.
    /// </summary>
    public const string Sensor50OfflineTime = "Sensor_50_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_51 component.
    /// </summary>
    public const string Sensor51 = "Sensor_51";
    /// <summary>
    /// The BrowseName for the Sensor_51_connect component.
    /// </summary>
    public const string Sensor51Connect = "Sensor_51_connect";
    /// <summary>
    /// The BrowseName for the Sensor_51_offline_time component.
    /// </summary>
    public const string Sensor51OfflineTime = "Sensor_51_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_52 component.
    /// </summary>
    public const string Sensor52 = "Sensor_52";
    /// <summary>
    /// The BrowseName for the Sensor_52_connect component.
    /// </summary>
    public const string Sensor52Connect = "Sensor_52_connect";
    /// <summary>
    /// The BrowseName for the Sensor_52_offline_time component.
    /// </summary>
    public const string Sensor52OfflineTime = "Sensor_52_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_53 component.
    /// </summary>
    public const string Sensor53 = "Sensor_53";
    /// <summary>
    /// The BrowseName for the Sensor_53_connect component.
    /// </summary>
    public const string Sensor53Connect = "Sensor_53_connect";
    /// <summary>
    /// The BrowseName for the Sensor_53_offline_time component.
    /// </summary>
    public const string Sensor53OfflineTime = "Sensor_53_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_54 component.
    /// </summary>
    public const string Sensor54 = "Sensor_54";
    /// <summary>
    /// The BrowseName for the Sensor_54_connect component.
    /// </summary>
    public const string Sensor54Connect = "Sensor_54_connect";
    /// <summary>
    /// The BrowseName for the Sensor_54_offline_time component.
    /// </summary>
    public const string Sensor54OfflineTime = "Sensor_54_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_55 component.
    /// </summary>
    public const string Sensor55 = "Sensor_55";
    /// <summary>
    /// The BrowseName for the Sensor_55_connect component.
    /// </summary>
    public const string Sensor55Connect = "Sensor_55_connect";
    /// <summary>
    /// The BrowseName for the Sensor_55_offline_time component.
    /// </summary>
    public const string Sensor55OfflineTime = "Sensor_55_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_56 component.
    /// </summary>
    public const string Sensor56 = "Sensor_56";
    /// <summary>
    /// The BrowseName for the Sensor_56_connect component.
    /// </summary>
    public const string Sensor56Connect = "Sensor_56_connect";
    /// <summary>
    /// The BrowseName for the Sensor_56_offline_time component.
    /// </summary>
    public const string Sensor56OfflineTime = "Sensor_56_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_57 component.
    /// </summary>
    public const string Sensor57 = "Sensor_57";
    /// <summary>
    /// The BrowseName for the Sensor_57_connect component.
    /// </summary>
    public const string Sensor57Connect = "Sensor_57_connect";
    /// <summary>
    /// The BrowseName for the Sensor_57_offline_time component.
    /// </summary>
    public const string Sensor57OfflineTime = "Sensor_57_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_58 component.
    /// </summary>
    public const string Sensor58 = "Sensor_58";
    /// <summary>
    /// The BrowseName for the Sensor_58_connect component.
    /// </summary>
    public const string Sensor58Connect = "Sensor_58_connect";
    /// <summary>
    /// The BrowseName for the Sensor_58_offline_time component.
    /// </summary>
    public const string Sensor58OfflineTime = "Sensor_58_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_59 component.
    /// </summary>
    public const string Sensor59 = "Sensor_59";
    /// <summary>
    /// The BrowseName for the Sensor_59_connect component.
    /// </summary>
    public const string Sensor59Connect = "Sensor_59_connect";
    /// <summary>
    /// The BrowseName for the Sensor_59_offline_time component.
    /// </summary>
    public const string Sensor59OfflineTime = "Sensor_59_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_5_connect component.
    /// </summary>
    public const string Sensor5Connect = "Sensor_5_connect";
    /// <summary>
    /// The BrowseName for the Sensor_5_offline_time component.
    /// </summary>
    public const string Sensor5OfflineTime = "Sensor_5_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_6 component.
    /// </summary>
    public const string Sensor6 = "Sensor_6";
    /// <summary>
    /// The BrowseName for the Sensor_60 component.
    /// </summary>
    public const string Sensor60 = "Sensor_60";
    /// <summary>
    /// The BrowseName for the Sensor_60_connect component.
    /// </summary>
    public const string Sensor60Connect = "Sensor_60_connect";
    /// <summary>
    /// The BrowseName for the Sensor_60_offline_time component.
    /// </summary>
    public const string Sensor60OfflineTime = "Sensor_60_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_61 component.
    /// </summary>
    public const string Sensor61 = "Sensor_61";
    /// <summary>
    /// The BrowseName for the Sensor_61_connect component.
    /// </summary>
    public const string Sensor61Connect = "Sensor_61_connect";
    /// <summary>
    /// The BrowseName for the Sensor_61_offline_time component.
    /// </summary>
    public const string Sensor61OfflineTime = "Sensor_61_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_62 component.
    /// </summary>
    public const string Sensor62 = "Sensor_62";
    /// <summary>
    /// The BrowseName for the Sensor_62_connect component.
    /// </summary>
    public const string Sensor62Connect = "Sensor_62_connect";
    /// <summary>
    /// The BrowseName for the Sensor_62_offline_time component.
    /// </summary>
    public const string Sensor62OfflineTime = "Sensor_62_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_63 component.
    /// </summary>
    public const string Sensor63 = "Sensor_63";
    /// <summary>
    /// The BrowseName for the Sensor_63_connect component.
    /// </summary>
    public const string Sensor63Connect = "Sensor_63_connect";
    /// <summary>
    /// The BrowseName for the Sensor_63_offline_time component.
    /// </summary>
    public const string Sensor63OfflineTime = "Sensor_63_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_64 component.
    /// </summary>
    public const string Sensor64 = "Sensor_64";
    /// <summary>
    /// The BrowseName for the Sensor_64_connect component.
    /// </summary>
    public const string Sensor64Connect = "Sensor_64_connect";
    /// <summary>
    /// The BrowseName for the Sensor_64_offline_time component.
    /// </summary>
    public const string Sensor64OfflineTime = "Sensor_64_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_65 component.
    /// </summary>
    public const string Sensor65 = "Sensor_65";
    /// <summary>
    /// The BrowseName for the Sensor_65_connect component.
    /// </summary>
    public const string Sensor65Connect = "Sensor_65_connect";
    /// <summary>
    /// The BrowseName for the Sensor_65_offline_time component.
    /// </summary>
    public const string Sensor65OfflineTime = "Sensor_65_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_66 component.
    /// </summary>
    public const string Sensor66 = "Sensor_66";
    /// <summary>
    /// The BrowseName for the Sensor_66_connect component.
    /// </summary>
    public const string Sensor66Connect = "Sensor_66_connect";
    /// <summary>
    /// The BrowseName for the Sensor_66_offline_time component.
    /// </summary>
    public const string Sensor66OfflineTime = "Sensor_66_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_67 component.
    /// </summary>
    public const string Sensor67 = "Sensor_67";
    /// <summary>
    /// The BrowseName for the Sensor_67_connect component.
    /// </summary>
    public const string Sensor67Connect = "Sensor_67_connect";
    /// <summary>
    /// The BrowseName for the Sensor_67_offline_time component.
    /// </summary>
    public const string Sensor67OfflineTime = "Sensor_67_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_68 component.
    /// </summary>
    public const string Sensor68 = "Sensor_68";
    /// <summary>
    /// The BrowseName for the Sensor_68_connect component.
    /// </summary>
    public const string Sensor68Connect = "Sensor_68_connect";
    /// <summary>
    /// The BrowseName for the Sensor_68_offline_time component.
    /// </summary>
    public const string Sensor68OfflineTime = "Sensor_68_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_69 component.
    /// </summary>
    public const string Sensor69 = "Sensor_69";
    /// <summary>
    /// The BrowseName for the Sensor_69_connect component.
    /// </summary>
    public const string Sensor69Connect = "Sensor_69_connect";
    /// <summary>
    /// The BrowseName for the Sensor_69_offline_time component.
    /// </summary>
    public const string Sensor69OfflineTime = "Sensor_69_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_6_connect component.
    /// </summary>
    public const string Sensor6Connect = "Sensor_6_connect";
    /// <summary>
    /// The BrowseName for the Sensor_6_offline_time component.
    /// </summary>
    public const string Sensor6OfflineTime = "Sensor_6_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_7 component.
    /// </summary>
    public const string Sensor7 = "Sensor_7";
    /// <summary>
    /// The BrowseName for the Sensor_70 component.
    /// </summary>
    public const string Sensor70 = "Sensor_70";
    /// <summary>
    /// The BrowseName for the Sensor_70_connect component.
    /// </summary>
    public const string Sensor70Connect = "Sensor_70_connect";
    /// <summary>
    /// The BrowseName for the Sensor_70_offline_time component.
    /// </summary>
    public const string Sensor70OfflineTime = "Sensor_70_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_71 component.
    /// </summary>
    public const string Sensor71 = "Sensor_71";
    /// <summary>
    /// The BrowseName for the Sensor_71_connect component.
    /// </summary>
    public const string Sensor71Connect = "Sensor_71_connect";
    /// <summary>
    /// The BrowseName for the Sensor_71_offline_time component.
    /// </summary>
    public const string Sensor71OfflineTime = "Sensor_71_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_72 component.
    /// </summary>
    public const string Sensor72 = "Sensor_72";
    /// <summary>
    /// The BrowseName for the Sensor_72_connect component.
    /// </summary>
    public const string Sensor72Connect = "Sensor_72_connect";
    /// <summary>
    /// The BrowseName for the Sensor_72_offline_time component.
    /// </summary>
    public const string Sensor72OfflineTime = "Sensor_72_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_73 component.
    /// </summary>
    public const string Sensor73 = "Sensor_73";
    /// <summary>
    /// The BrowseName for the Sensor_73_connect component.
    /// </summary>
    public const string Sensor73Connect = "Sensor_73_connect";
    /// <summary>
    /// The BrowseName for the Sensor_73_offline_time component.
    /// </summary>
    public const string Sensor73OfflineTime = "Sensor_73_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_74 component.
    /// </summary>
    public const string Sensor74 = "Sensor_74";
    /// <summary>
    /// The BrowseName for the Sensor_74_connect component.
    /// </summary>
    public const string Sensor74Connect = "Sensor_74_connect";
    /// <summary>
    /// The BrowseName for the Sensor_74_offline_time component.
    /// </summary>
    public const string Sensor74OfflineTime = "Sensor_74_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_75 component.
    /// </summary>
    public const string Sensor75 = "Sensor_75";
    /// <summary>
    /// The BrowseName for the Sensor_75_connect component.
    /// </summary>
    public const string Sensor75Connect = "Sensor_75_connect";
    /// <summary>
    /// The BrowseName for the Sensor_75_offline_time component.
    /// </summary>
    public const string Sensor75OfflineTime = "Sensor_75_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_76 component.
    /// </summary>
    public const string Sensor76 = "Sensor_76";
    /// <summary>
    /// The BrowseName for the Sensor_76_connect component.
    /// </summary>
    public const string Sensor76Connect = "Sensor_76_connect";
    /// <summary>
    /// The BrowseName for the Sensor_76_offline_time component.
    /// </summary>
    public const string Sensor76OfflineTime = "Sensor_76_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_77 component.
    /// </summary>
    public const string Sensor77 = "Sensor_77";
    /// <summary>
    /// The BrowseName for the Sensor_77_connect component.
    /// </summary>
    public const string Sensor77Connect = "Sensor_77_connect";
    /// <summary>
    /// The BrowseName for the Sensor_77_offline_time component.
    /// </summary>
    public const string Sensor77OfflineTime = "Sensor_77_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_78 component.
    /// </summary>
    public const string Sensor78 = "Sensor_78";
    /// <summary>
    /// The BrowseName for the Sensor_78_connect component.
    /// </summary>
    public const string Sensor78Connect = "Sensor_78_connect";
    /// <summary>
    /// The BrowseName for the Sensor_78_offline_time component.
    /// </summary>
    public const string Sensor78OfflineTime = "Sensor_78_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_79 component.
    /// </summary>
    public const string Sensor79 = "Sensor_79";
    /// <summary>
    /// The BrowseName for the Sensor_79_connect component.
    /// </summary>
    public const string Sensor79Connect = "Sensor_79_connect";
    /// <summary>
    /// The BrowseName for the Sensor_79_offline_time component.
    /// </summary>
    public const string Sensor79OfflineTime = "Sensor_79_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_7_connect component.
    /// </summary>
    public const string Sensor7Connect = "Sensor_7_connect";
    /// <summary>
    /// The BrowseName for the Sensor_7_offline_time component.
    /// </summary>
    public const string Sensor7OfflineTime = "Sensor_7_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_8 component.
    /// </summary>
    public const string Sensor8 = "Sensor_8";
    /// <summary>
    /// The BrowseName for the Sensor_80 component.
    /// </summary>
    public const string Sensor80 = "Sensor_80";
    /// <summary>
    /// The BrowseName for the Sensor_80_connect component.
    /// </summary>
    public const string Sensor80Connect = "Sensor_80_connect";
    /// <summary>
    /// The BrowseName for the Sensor_80_offline_time component.
    /// </summary>
    public const string Sensor80OfflineTime = "Sensor_80_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_81 component.
    /// </summary>
    public const string Sensor81 = "Sensor_81";
    /// <summary>
    /// The BrowseName for the Sensor_81_connect component.
    /// </summary>
    public const string Sensor81Connect = "Sensor_81_connect";
    /// <summary>
    /// The BrowseName for the Sensor_81_offline_time component.
    /// </summary>
    public const string Sensor81OfflineTime = "Sensor_81_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_82 component.
    /// </summary>
    public const string Sensor82 = "Sensor_82";
    /// <summary>
    /// The BrowseName for the Sensor_82_connect component.
    /// </summary>
    public const string Sensor82Connect = "Sensor_82_connect";
    /// <summary>
    /// The BrowseName for the Sensor_82_offline_time component.
    /// </summary>
    public const string Sensor82OfflineTime = "Sensor_82_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_83 component.
    /// </summary>
    public const string Sensor83 = "Sensor_83";
    /// <summary>
    /// The BrowseName for the Sensor_83_connect component.
    /// </summary>
    public const string Sensor83Connect = "Sensor_83_connect";
    /// <summary>
    /// The BrowseName for the Sensor_83_offline_time component.
    /// </summary>
    public const string Sensor83OfflineTime = "Sensor_83_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_84 component.
    /// </summary>
    public const string Sensor84 = "Sensor_84";
    /// <summary>
    /// The BrowseName for the Sensor_84_connect component.
    /// </summary>
    public const string Sensor84Connect = "Sensor_84_connect";
    /// <summary>
    /// The BrowseName for the Sensor_84_offline_time component.
    /// </summary>
    public const string Sensor84OfflineTime = "Sensor_84_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_85 component.
    /// </summary>
    public const string Sensor85 = "Sensor_85";
    /// <summary>
    /// The BrowseName for the Sensor_85_connect component.
    /// </summary>
    public const string Sensor85Connect = "Sensor_85_connect";
    /// <summary>
    /// The BrowseName for the Sensor_85_offline_time component.
    /// </summary>
    public const string Sensor85OfflineTime = "Sensor_85_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_86 component.
    /// </summary>
    public const string Sensor86 = "Sensor_86";
    /// <summary>
    /// The BrowseName for the Sensor_86_connect component.
    /// </summary>
    public const string Sensor86Connect = "Sensor_86_connect";
    /// <summary>
    /// The BrowseName for the Sensor_86_offline_time component.
    /// </summary>
    public const string Sensor86OfflineTime = "Sensor_86_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_87 component.
    /// </summary>
    public const string Sensor87 = "Sensor_87";
    /// <summary>
    /// The BrowseName for the Sensor_87_connect component.
    /// </summary>
    public const string Sensor87Connect = "Sensor_87_connect";
    /// <summary>
    /// The BrowseName for the Sensor_87_offline_time component.
    /// </summary>
    public const string Sensor87OfflineTime = "Sensor_87_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_88 component.
    /// </summary>
    public const string Sensor88 = "Sensor_88";
    /// <summary>
    /// The BrowseName for the Sensor_88_connect component.
    /// </summary>
    public const string Sensor88Connect = "Sensor_88_connect";
    /// <summary>
    /// The BrowseName for the Sensor_88_offline_time component.
    /// </summary>
    public const string Sensor88OfflineTime = "Sensor_88_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_89 component.
    /// </summary>
    public const string Sensor89 = "Sensor_89";
    /// <summary>
    /// The BrowseName for the Sensor_89_connect component.
    /// </summary>
    public const string Sensor89Connect = "Sensor_89_connect";
    /// <summary>
    /// The BrowseName for the Sensor_89_offline_time component.
    /// </summary>
    public const string Sensor89OfflineTime = "Sensor_89_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_8_connect component.
    /// </summary>
    public const string Sensor8Connect = "Sensor_8_connect";
    /// <summary>
    /// The BrowseName for the Sensor_8_offline_time component.
    /// </summary>
    public const string Sensor8OfflineTime = "Sensor_8_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_9 component.
    /// </summary>
    public const string Sensor9 = "Sensor_9";
    /// <summary>
    /// The BrowseName for the Sensor_90 component.
    /// </summary>
    public const string Sensor90 = "Sensor_90";
    /// <summary>
    /// The BrowseName for the Sensor_90_connect component.
    /// </summary>
    public const string Sensor90Connect = "Sensor_90_connect";
    /// <summary>
    /// The BrowseName for the Sensor_90_offline_time component.
    /// </summary>
    public const string Sensor90OfflineTime = "Sensor_90_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_91 component.
    /// </summary>
    public const string Sensor91 = "Sensor_91";
    /// <summary>
    /// The BrowseName for the Sensor_91_connect component.
    /// </summary>
    public const string Sensor91Connect = "Sensor_91_connect";
    /// <summary>
    /// The BrowseName for the Sensor_91_offline_time component.
    /// </summary>
    public const string Sensor91OfflineTime = "Sensor_91_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_92 component.
    /// </summary>
    public const string Sensor92 = "Sensor_92";
    /// <summary>
    /// The BrowseName for the Sensor_92_connect component.
    /// </summary>
    public const string Sensor92Connect = "Sensor_92_connect";
    /// <summary>
    /// The BrowseName for the Sensor_92_offline_time component.
    /// </summary>
    public const string Sensor92OfflineTime = "Sensor_92_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_93 component.
    /// </summary>
    public const string Sensor93 = "Sensor_93";
    /// <summary>
    /// The BrowseName for the Sensor_93_connect component.
    /// </summary>
    public const string Sensor93Connect = "Sensor_93_connect";
    /// <summary>
    /// The BrowseName for the Sensor_93_offline_time component.
    /// </summary>
    public const string Sensor93OfflineTime = "Sensor_93_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_94 component.
    /// </summary>
    public const string Sensor94 = "Sensor_94";
    /// <summary>
    /// The BrowseName for the Sensor_94_connect component.
    /// </summary>
    public const string Sensor94Connect = "Sensor_94_connect";
    /// <summary>
    /// The BrowseName for the Sensor_94_offline_time component.
    /// </summary>
    public const string Sensor94OfflineTime = "Sensor_94_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_95 component.
    /// </summary>
    public const string Sensor95 = "Sensor_95";
    /// <summary>
    /// The BrowseName for the Sensor_95_connect component.
    /// </summary>
    public const string Sensor95Connect = "Sensor_95_connect";
    /// <summary>
    /// The BrowseName for the Sensor_95_offline_time component.
    /// </summary>
    public const string Sensor95OfflineTime = "Sensor_95_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_96 component.
    /// </summary>
    public const string Sensor96 = "Sensor_96";
    /// <summary>
    /// The BrowseName for the Sensor_96_connect component.
    /// </summary>
    public const string Sensor96Connect = "Sensor_96_connect";
    /// <summary>
    /// The BrowseName for the Sensor_96_offline_time component.
    /// </summary>
    public const string Sensor96OfflineTime = "Sensor_96_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_97 component.
    /// </summary>
    public const string Sensor97 = "Sensor_97";
    /// <summary>
    /// The BrowseName for the Sensor_97_connect component.
    /// </summary>
    public const string Sensor97Connect = "Sensor_97_connect";
    /// <summary>
    /// The BrowseName for the Sensor_97_offline_time component.
    /// </summary>
    public const string Sensor97OfflineTime = "Sensor_97_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_98 component.
    /// </summary>
    public const string Sensor98 = "Sensor_98";
    /// <summary>
    /// The BrowseName for the Sensor_98_connect component.
    /// </summary>
    public const string Sensor98Connect = "Sensor_98_connect";
    /// <summary>
    /// The BrowseName for the Sensor_98_offline_time component.
    /// </summary>
    public const string Sensor98OfflineTime = "Sensor_98_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_99 component.
    /// </summary>
    public const string Sensor99 = "Sensor_99";
    /// <summary>
    /// The BrowseName for the Sensor_99_connect component.
    /// </summary>
    public const string Sensor99Connect = "Sensor_99_connect";
    /// <summary>
    /// The BrowseName for the Sensor_99_offline_time component.
    /// </summary>
    public const string Sensor99OfflineTime = "Sensor_99_offline_time";
    /// <summary>
    /// The BrowseName for the Sensor_9_connect component.
    /// </summary>
    public const string Sensor9Connect = "Sensor_9_connect";
    /// <summary>
    /// The BrowseName for the Sensor_9_offline_time component.
    /// </summary>
    public const string Sensor9OfflineTime = "Sensor_9_offline_time";
    /// <summary>
    /// The BrowseName for the WTMS component.
    /// </summary>
    public const string WTMS = "WTMS";
    /// <summary>
    /// The BrowseName for the http://sentron.org/WTMS/ component.
    /// </summary>
    public const string httpSentronOrgWTMS = "http://sentron.org/WTMS/";
}
#endregion

#region Namespace Declarations
/// <summary>
/// Defines constants for all namespaces referenced by the Model.
/// </summary>
public static partial class Namespaces
{
    /// <summary>
    /// The URI for the OpcUa namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUa = "http://opcfoundation.org/UA/";

    /// <summary>
    /// The URI for the OpcUaXsd namespace (.NET code namespace is 'Opc.Ua').
    /// </summary>
    public const string OpcUaXsd = "http://opcfoundation.org/UA/2008/02/Types.xsd";

    /// <summary>
    /// The URI for the WTMS namespace.
    /// </summary>
    public const string WTMS = "http://sentron.org/WTMS/";

    /// <summary>
    /// The URI for the WTMSXsd namespace.
    /// </summary>
    public const string WTMSXsd = "http://sentron.org/WTMS/Types.xsd";
}
#endregion

