namespace RolePermissionBasedAccess;

public enum PermissionType : uint
{
    None = 0,                    // No permission
    Browse = 1,                  // Browse permission (see that node exists)
    ReadRolePermissions = 2,     // Read role permission information
    WriteAttribute = 4,          // Write attributes
    WriteRolePermissions = 8,    // Write role permissions
    WriteHistorizing = 16,       // Write historizing settings
    Read = 32,                   // Read data values
    Write = 64,                  // Write data values
    ReadHistory = 128,           // Read historical data
    InsertHistory = 256,         // Insert historical data
    ModifyHistory = 512,         // Modify historical data
    DeleteHistory = 1024,        // Delete historical data
    ReceiveEvents = 2048,        // Receive events
    Call = 4096,                 // Call methods
    AddReference = 8192,         // Add references
    RemoveReference = 16384,     // Remove references
    DeleteNode = 32768,          // Delete nodes
    AddNode = 65536              // Add nodes
}
