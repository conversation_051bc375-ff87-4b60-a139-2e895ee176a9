using OPCUAServer.Common;
using OPCUAServer.Devices.Circuit;
using OPCUAServer.Devices.PAC1020;
using OPCUAServer.Devices.PAC3120;
using OPCUAServer.Devices.PAC3220;
using OPCUAServer.Devices.PAC4200;
using OPCUAServer.Devices.Panel;
using OPCUAServer.Devices.Substation;
using OPCUAServer.Devices.ThreeVA;
using OPCUAServer.Devices.ThreeWA;
using OPCUAServer.Devices.ThreeWL;
using OPCUAServer.Devices.WTMS;
using OPCUAServer.UserManagement;
using PanelAlarm;
using PanelHttp;
using Sentron.OPCUAServer.Configuration;
using Serilog;
using UnifiedAutomation.UaServer;

namespace OPCUAServer;

/// <summary>
/// Provides management functionality for the Panel OPC Server.
/// This class handles the configuration and interaction with the data source.
/// </summary>
internal partial class OPCServerManager : ServerManager, IQueryManager
{
    private readonly ILogger _logger = LoggingHelper.GetLogger<OPCServerManager>();

    private readonly IDataSourceConfiguration _dataSourceConfig;
    private readonly string _dataSourceIP;
    private readonly string _dataSourcePort;
    private readonly int _interval;
    private readonly int _maxRetry;
    private readonly int _retryInterval;

    private ReadPanelHttp _readUdcHttpObj;
    private ReadPanelMessage _readUdcMessageHttpObj;
    private Dictionary<string, List<IDeviceInfo>> _dictCategoryDevices;

    public OPCServerManager(IDataSourceConfiguration dataSourceConfig)
    {
        _dataSourceConfig = dataSourceConfig ?? throw new ArgumentNullException(nameof(dataSourceConfig));

        _dataSourceIP = _dataSourceConfig.IP;
        _dataSourcePort = _dataSourceConfig.Port;
        _interval = _dataSourceConfig.Interval;
        _maxRetry = _dataSourceConfig.MaxRetry;
        _retryInterval = _dataSourceConfig.RetryInterval;
    }

    protected override void OnRootNodeManagerStarted(RootNodeManager nodeManager)
    {
        string sToken = string.Empty; //PanelLogIn.LoginAndGotToken(this._dataSourceIP, this._dataSourcePort);
        this.InitDataSource(this._dataSourceIP, this._dataSourcePort, sToken, this._interval);
        this.InitDeviceMessage(this._dataSourceIP, this._dataSourcePort, sToken);

        // Get Device List and Itemid
        bool waitSuccess = !(ReadPanelHttp.ListDevice.Count == 0);
        if (ReadPanelHttp.ListDevice.Count == 0)
        {
            Thread.Sleep(_retryInterval);
        }

        int retryCount = 0;
        while (retryCount < _maxRetry)
        {
            if (waitSuccess)
            {
                break;
            }
            else
            {
                retryCount++;
                Thread.Sleep(_retryInterval);
            }
        }

        if (!waitSuccess)
        {
            throw new Exception("Waiting data source timeout");
        }

        // All Devices type
        this._dictCategoryDevices = new Dictionary<string, List<IDeviceInfo>>();
        for (int nIdx = 0; nIdx < ReadPanelHttp.ListDevice.Count; nIdx++)
        {
            string key = ReadPanelHttp.ListDevice[nIdx].DeviceType;
            IDeviceInfo value = ReadPanelHttp.ListDevice[nIdx];

            // add value
            if (this._dictCategoryDevices.ContainsKey(key))
            {
                this._dictCategoryDevices[key].Add(value);
            }
            else
            {
                this._dictCategoryDevices[key] = new List<IDeviceInfo> { value };
            }
        }

        // Start OPC UA Event Server
        // this.StartPanelEventHistory();

        var userManagementNodeManager = new UserManagementNodeManager(this);
        userManagementNodeManager.Startup();

        var alertingNodeManager = new PanelAlarmNodeManager(this);
        alertingNodeManager.Startup();

        // Start OPC UA Data Server
        // foreach (var categoryDevice in this._dictCategoryDevices)
        // {
        //     this.StartOPCUAServer(categoryDevice.Key);
        // }

        this.StartOPCUAServer2(ReadPanelHttp.ListDevice);

        // [Add ImpersonateEventHandler]
        this.SessionManager.ImpersonateUser += new ImpersonateEventHandler(this.SessionManager_ImpersonateUser);
    }

    private void InitDataSource(string ip, string port, string token, int nInterval)
    {
        this._readUdcHttpObj = new ReadPanelHttp();
        this._readUdcHttpObj.StartReadUdcHttp(ip, port, token);
    }

    private void InitDeviceMessage(string ip, string port, string token)
    {
        this._readUdcMessageHttpObj = new ReadPanelMessage();
        // this._readUdcMessageHttpObj.StartReadUdcMessage(ip, port, token);
    }

    // private void StartPanelEventHistory()
    // {
    //     var listPanelMsgNode = new List<PanelHistoricalMsgNodeManager>();
    //     var listPanelMsgItemid = new List<string>();
    //     for (int nIdx = 0; nIdx < 1; nIdx++)
    //     {
    //         var panelMsgNode = new PanelHistoricalMsgNodeManager(this);
    //         listPanelMsgNode.Add(panelMsgNode);
    //         listPanelMsgItemid.Add(HISTORICALEVENTID);
    //     }
    //
    //     PanelHistoricalMsgNodeManager.InitialPanelHistoricalMsgSystem(1, listPanelMsgItemid);
    //     foreach (var panelMsgNode in listPanelMsgNode)
    //     {
    //         panelMsgNode.PanelHistoricalMsgItemId = HISTORICALEVENTID;
    //         panelMsgNode.PanelHistoricalMsgName = "PanelEventHistory";
    //         panelMsgNode.Startup();
    //     }
    // }

    private void StartOPCUAServer2(List<IDeviceInfo> devices)
    {
        OPCUANodeManager.InitialPAC4200System(devices.Count, devices.Select(d => d.ItemId).ToList());
        foreach (IDeviceInfo deviceInfo in devices)
        {

            var threeVAObj = new OPCUANodeManager(this) { ItemId = deviceInfo.ItemId, ItemName = deviceInfo.DeviceType + " " + deviceInfo.DeviceIP };
            threeVAObj.Startup();
        }
    }

    private void StartOPCUAServer(string key)
    {
        if (string.Equals(key, "3VA"))
        {
            // 3VA device
            var listThreeVANode = new List<ThreeVANodeManager>();
            var dictThreeVAItemidIP = new Dictionary<string, string>();
            var listThreeVAItemid = new List<string>();
            for (int nIdx = 0; nIdx < this._dictCategoryDevices["3VA"].Count; nIdx++)
            {
                var threeVAObj = new ThreeVANodeManager(this);
                listThreeVANode.Add(threeVAObj);
                dictThreeVAItemidIP.Add(this._dictCategoryDevices["3VA"][nIdx].ItemId, this._dictCategoryDevices["3VA"][nIdx].DeviceIP);
                listThreeVAItemid.Add(this._dictCategoryDevices["3VA"][nIdx].ItemId);
            }

            ThreeVANodeManager.InitialThreeVASystem(dictThreeVAItemidIP.Count, listThreeVAItemid);
            var nodesAndDevices = listThreeVANode.Zip(dictThreeVAItemidIP, (n, w) => new { Nodes = n, Devices = w });
            foreach (var nw in nodesAndDevices)
            {
                nw.Nodes.ThreeVAItemId = nw.Devices.Key;
                nw.Nodes.ThreeVAName = "TheeVA" + " " + nw.Devices.Value;
                nw.Nodes.Startup();
            }
        }
        else if (string.Equals(key, "3WA"))
        {
            // 3WA device
            var listThreeWANode = new List<ThreeWANodeManager>();
            var dictThreeWAItemidIP = new Dictionary<string, string>();
            var listThreeWAItemid = new List<string>();
            for (int nIdx = 0; nIdx < this._dictCategoryDevices["3WA"].Count; nIdx++)
            {
                var threeWAObj = new ThreeWANodeManager(this);
                listThreeWANode.Add(threeWAObj);
                dictThreeWAItemidIP.Add(this._dictCategoryDevices["3WA"][nIdx].ItemId, this._dictCategoryDevices["3WA"][nIdx].DeviceIP);
                listThreeWAItemid.Add(this._dictCategoryDevices["3WA"][nIdx].ItemId);
            }

            ThreeWANodeManager.InitialThreeWASystem(dictThreeWAItemidIP.Count, listThreeWAItemid);
            var nodesAndDevices = listThreeWANode.Zip(dictThreeWAItemidIP, (n, w) => new { Nodes = n, Devices = w });
            foreach (var nw in nodesAndDevices)
            {
                nw.Nodes.ThreeWAItemId = nw.Devices.Key;
                nw.Nodes.ThreeWAName = "TheeWA" + " " + nw.Devices.Value;
                nw.Nodes.Startup();
            }
        }
        else if (string.Equals(key, "3WL"))
        {
            // 3WL device
            var listThreeWLNode = new List<ThreeWLNodeManager>();
            var dictThreeWLItemidIP = new Dictionary<string, string>();
            var listThreeWLItemid = new List<string>();
            for (int nIdx = 0; nIdx < this._dictCategoryDevices["3WL"].Count; nIdx++)
            {
                var threeWLObj = new ThreeWLNodeManager(this);
                listThreeWLNode.Add(threeWLObj);
                dictThreeWLItemidIP.Add(this._dictCategoryDevices["3WL"][nIdx].ItemId, this._dictCategoryDevices["3WL"][nIdx].DeviceIP);
                listThreeWLItemid.Add(this._dictCategoryDevices["3WL"][nIdx].ItemId);
            }

            ThreeWLNodeManager.InitialThreeWLSystem(dictThreeWLItemidIP.Count, listThreeWLItemid);
            var nodesAndDevices = listThreeWLNode.Zip(dictThreeWLItemidIP, (n, w) => new { Nodes = n, Devices = w });
            foreach (var nw in nodesAndDevices)
            {
                nw.Nodes.ThreeWLItemId = nw.Devices.Key;
                nw.Nodes.ThreeWLName = "TheeWL" + " " + nw.Devices.Value;
                nw.Nodes.Startup();
            }
        }
        else if (string.Equals(key, "PAC1020"))
        {
            // PAC1020 device
            var listPAC1020Node = new List<PAC1020NodeManager>();
            var dictPAC1020ItemidIP = new Dictionary<string, string>();
            var listPAC1020Itemid = new List<string>();
            for (int nIdx = 0; nIdx < this._dictCategoryDevices["PAC1020"].Count; nIdx++)
            {
                var pac1020Obj = new PAC1020NodeManager(this);
                listPAC1020Node.Add(pac1020Obj);
                dictPAC1020ItemidIP.Add(this._dictCategoryDevices["PAC1020"][nIdx].ItemId, this._dictCategoryDevices["PAC1020"][nIdx].DeviceIP);
                listPAC1020Itemid.Add(this._dictCategoryDevices["PAC1020"][nIdx].ItemId);
            }

            PAC1020NodeManager.InitialPAC1020System(dictPAC1020ItemidIP.Count, listPAC1020Itemid);
            var nodesAndDevices = listPAC1020Node.Zip(dictPAC1020ItemidIP, (n, w) => new { Nodes = n, Devices = w });
            foreach (var nw in nodesAndDevices)
            {
                nw.Nodes.PAC1020ItemId = nw.Devices.Key;
                nw.Nodes.PAC1020Name = "PAC1020" + " " + nw.Devices.Value;
                nw.Nodes.Startup();
            }
        }
        else if (string.Equals(key, "PAC3120"))
        {
            // PAC3120 device
            var listPAC3120Node = new List<PAC3120NodeManager>();
            var dictPAC3120ItemidIP = new Dictionary<string, string>();
            var listPAC3120Itemid = new List<string>();
            for (int nIdx = 0; nIdx < this._dictCategoryDevices["PAC3120"].Count; nIdx++)
            {
                var pac3120Obj = new PAC3120NodeManager(this);
                listPAC3120Node.Add(pac3120Obj);
                dictPAC3120ItemidIP.Add(this._dictCategoryDevices["PAC3120"][nIdx].ItemId, this._dictCategoryDevices["PAC3120"][nIdx].DeviceIP);
                listPAC3120Itemid.Add(this._dictCategoryDevices["PAC3120"][nIdx].ItemId);
            }

            PAC3120NodeManager.InitialPAC3120System(dictPAC3120ItemidIP.Count, listPAC3120Itemid);
            var nodesAndDevices = listPAC3120Node.Zip(dictPAC3120ItemidIP, (n, w) => new { Nodes = n, Devices = w });
            foreach (var nw in nodesAndDevices)
            {
                nw.Nodes.PAC3120ItemId = nw.Devices.Key;
                nw.Nodes.PAC3120Name = "PAC3120" + " " + nw.Devices.Value;
                nw.Nodes.Startup();
            }
        }
        else if (string.Equals(key, "PAC3220"))
        {
            // PAC3220 device
            var listPAC3220Node = new List<PAC3220NodeManager>();
            var dictPAC3220ItemidIP = new Dictionary<string, string>();
            var listPAC3220Itemid = new List<string>();
            for (int nIdx = 0; nIdx < this._dictCategoryDevices["PAC3220"].Count; nIdx++)
            {
                var pac3220Obj = new PAC3220NodeManager(this);
                listPAC3220Node.Add(pac3220Obj);
                dictPAC3220ItemidIP.Add(this._dictCategoryDevices["PAC3220"][nIdx].ItemId, this._dictCategoryDevices["PAC3220"][nIdx].DeviceIP);
                listPAC3220Itemid.Add(this._dictCategoryDevices["PAC3220"][nIdx].ItemId);
            }

            PAC3220NodeManager.InitialPAC3220System(dictPAC3220ItemidIP.Count, listPAC3220Itemid);
            var nodesAndDevices = listPAC3220Node.Zip(dictPAC3220ItemidIP, (n, w) => new { Nodes = n, Devices = w });
            foreach (var nw in nodesAndDevices)
            {
                nw.Nodes.PAC3220ItemId = nw.Devices.Key;
                nw.Nodes.PAC3220Name = "PAC3220" + " " + nw.Devices.Value;
                nw.Nodes.Startup();
            }
        }
        // else if (string.Equals(key, "PAC4200"))
        // {
        //     // PAC4200 device
        //     var listPAC4200Node = new List<PAC4200NodeManager>();
        //     var dictPAC4200ItemidIP = new Dictionary<string, string>();
        //     var listPAC4200Itemid = new List<string>();
        //     for (int nIdx = 0; nIdx < this._dictCategoryDevices["PAC4200"].Count; nIdx++)
        //     {
        //         var pac4200Obj = new PAC4200NodeManager(this);
        //         listPAC4200Node.Add(pac4200Obj);
        //         dictPAC4200ItemidIP.Add(this._dictCategoryDevices["PAC4200"][nIdx].ItemId, this._dictCategoryDevices["PAC4200"][nIdx].DeviceIP);
        //         listPAC4200Itemid.Add(this._dictCategoryDevices["PAC4200"][nIdx].ItemId);
        //     }

        //     PAC4200NodeManager.InitialPAC4200System(dictPAC4200ItemidIP.Count, listPAC4200Itemid);
        //     var nodesAndDevices = listPAC4200Node.Zip(dictPAC4200ItemidIP, (n, w) => new { Nodes = n, Devices = w });
        //     foreach (var nw in nodesAndDevices)
        //     {
        //         nw.Nodes.PAC4200ItemId = nw.Devices.Key;
        //         nw.Nodes.PAC4200Name = "PAC4200" + " " + nw.Devices.Value;
        //         nw.Nodes.Startup();
        //         // DynamicNodeServiceIntegration.StartDynamicNodeService(new List<PAC4200NodeManager> { nw.Nodes }, 5);

        //         // _logger.Information("✅ Dynamic node service started!");
        //         // _logger.Information("🔄 New properties will be added every 30 seconds");
        //     }
        // }
        else if (string.Equals(key, "Circuit"))
        {
            // Circuit device
            var listCircuitNode = new List<CircuitNodeManager>();
            var dictCircuitItemidIP = new Dictionary<string, string>();
            var listCircuitItemid = new List<string>();
            for (int nIdx = 0; nIdx < this._dictCategoryDevices["Circuit"].Count; nIdx++)
            {
                var circuitObj = new CircuitNodeManager(this);
                listCircuitNode.Add(circuitObj);
                dictCircuitItemidIP.Add(this._dictCategoryDevices["Circuit"][nIdx].ItemId, this._dictCategoryDevices["Circuit"][nIdx].DeviceIP);
                listCircuitItemid.Add(this._dictCategoryDevices["Circuit"][nIdx].ItemId);
            }

            CircuitNodeManager.InitialCircuitSystem(dictCircuitItemidIP.Count, listCircuitItemid);
            var nodesAndDevices = listCircuitNode.Zip(dictCircuitItemidIP, (n, w) => new { Nodes = n, Devices = w });
            foreach (var nw in nodesAndDevices)
            {
                nw.Nodes.CircuitItemId = nw.Devices.Key;
                nw.Nodes.CircuitName = "Circuit" + " " + nw.Devices.Value;
                nw.Nodes.Startup();
            }
        }
        else if (string.Equals(key, "Panel"))
        {
            // Panel device
            var listPanelNode = new List<PanelNodeManager>();
            var dictPanelItemidIP = new Dictionary<string, string>();
            var listPanelItemid = new List<string>();
            for (int nIdx = 0; nIdx < this._dictCategoryDevices["Panel"].Count; nIdx++)
            {
                var panelObj = new PanelNodeManager(this);
                listPanelNode.Add(panelObj);
                dictPanelItemidIP.Add(this._dictCategoryDevices["Panel"][nIdx].ItemId, this._dictCategoryDevices["Panel"][nIdx].DeviceIP);
                listPanelItemid.Add(this._dictCategoryDevices["Panel"][nIdx].ItemId);
            }

            PanelNodeManager.InitialPanelSystem(dictPanelItemidIP.Count, listPanelItemid);
            var nodesAndDevices = listPanelNode.Zip(dictPanelItemidIP, (n, w) => new { Nodes = n, Devices = w });
            foreach (var nw in nodesAndDevices)
            {
                nw.Nodes.PanelItemId = nw.Devices.Key;
                nw.Nodes.PanelName = "Panel" + " " + nw.Devices.Value;
                nw.Nodes.Startup();
            }
        }
        else if (string.Equals(key, "Substation"))
        {
            // Substation device
            var listSubstationNode = new List<SubstationNodeManager>();
            var dictSubstationItemidIP = new Dictionary<string, string>();
            var listSubstationItemid = new List<string>();
            for (int nIdx = 0; nIdx < this._dictCategoryDevices["Substation"].Count; nIdx++)
            {
                var substationObj = new SubstationNodeManager(this);
                listSubstationNode.Add(substationObj);
                dictSubstationItemidIP.Add(this._dictCategoryDevices["Substation"][nIdx].ItemId, this._dictCategoryDevices["Substation"][nIdx].DeviceIP);
                listSubstationItemid.Add(this._dictCategoryDevices["Substation"][nIdx].ItemId);
            }

            SubstationNodeManager.InitialSubstationSystem(dictSubstationItemidIP.Count, listSubstationItemid);
            var nodesAndDevices = listSubstationNode.Zip(dictSubstationItemidIP, (n, w) => new { Nodes = n, Devices = w });
            foreach (var nw in nodesAndDevices)
            {
                nw.Nodes.SubstationItemId = nw.Devices.Key;
                nw.Nodes.SubstationName = "Substation" + " " + nw.Devices.Value;
                nw.Nodes.Startup();
            }
        }
        else if (string.Equals(key, "WTMS"))
        {
            // WTMS device
            var listWTMSNode = new List<WTMSNodeManager>();
            var dictWTMSItemidIP = new Dictionary<string, string>();
            var listWTMSItemid = new List<string>();
            for (int nIdx = 0; nIdx < this._dictCategoryDevices["WTMS"].Count; nIdx++)
            {
                var wtmsObj = new WTMSNodeManager(this);
                listWTMSNode.Add(wtmsObj);
                dictWTMSItemidIP.Add(this._dictCategoryDevices["WTMS"][nIdx].ItemId, this._dictCategoryDevices["WTMS"][nIdx].DeviceIP);
                listWTMSItemid.Add(this._dictCategoryDevices["WTMS"][nIdx].ItemId);
            }

            WTMSNodeManager.InitialWTMSSystem(dictWTMSItemidIP.Count, listWTMSItemid);
            var nodesAndDevices = listWTMSNode.Zip(dictWTMSItemidIP, (n, w) => new { Nodes = n, Devices = w });
            foreach (var nw in nodesAndDevices)
            {
                nw.Nodes.WTMSItemId = nw.Devices.Key;
                nw.Nodes.WTMSName = "WTMS" + " " + nw.Devices.Value;
                nw.Nodes.Startup();
            }
        }
    }
}
