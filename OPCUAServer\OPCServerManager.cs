
using Microsoft.Extensions.Configuration;
using OPCUAServer.Common;
using OPCUAServer.Devices.Circuit;
using OPCUAServer.Devices.PAC1020;
using OPCUAServer.Devices.PAC3120;
using OPCUAServer.Devices.PAC3220;
using OPCUAServer.Devices.PAC4200;
using OPCUAServer.Devices.Panel;
using OPCUAServer.Devices.Substation;
using OPCUAServer.Devices.ThreeVA;
using OPCUAServer.Devices.ThreeWA;
using OPCUAServer.Devices.ThreeWL;
using OPCUAServer.Devices.WTMS;
using OPCUAServer.UserManagement;
using PanelAlarm;
using PanelHttp;
using Sentron.OPCUAServer.Configuration;
using Serilog;
using UnifiedAutomation.UaServer;

namespace OPCUAServer;

/// <summary>
/// Provides management functionality for the Panel OPC Server.
/// This class handles the configuration and interaction with the data source.
/// </summary>
internal partial class OPCServerManager : ServerManager, IQueryManager
{
    private readonly ILogger _logger = LoggingHelper.GetLogger<OPCServerManager>();

    private readonly IConfiguration _config;
    private readonly DataSourceConfiguration _dataSourceConfig;
    // private readonly string _dataSourcePort;
    // private readonly int _interval;
    // private readonly int _maxRetry;
    // private readonly int _retryInterval;

    private ReadPanelHttp _readUdcHttpObj;
    private ReadPanelMessage _readUdcMessageHttpObj;
    private Dictionary<string, List<IDeviceInfo>> _dictCategoryDevices;

    public OPCServerManager(IConfiguration configuration)
    {
        _config = configuration;

        _dataSourceConfig = _config.GetValue<DataSourceConfiguration>("PanelManagerConnection") ?? throw new InvalidOperationException("PanelManagerConnection configuration section is missing or invalid");

        // _dataSourceIP = _dataSourceConfig.IP;
        // _dataSourcePort = _dataSourceConfig.Port;
        // _interval = _dataSourceConfig.Interval;
        // _maxRetry = _dataSourceConfig.MaxRetry;
        // _retryInterval = _dataSourceConfig.RetryInterval;
    }

    protected override void OnRootNodeManagerStarted(RootNodeManager nodeManager)
    {
        string sToken = string.Empty; //PanelLogIn.LoginAndGotToken(this._dataSourceIP, this._dataSourcePort);
        this.InitDataSource(this._dataSourceConfig.IP, this._dataSourceConfig.Port, sToken, this._dataSourceConfig.Interval);
        this.InitDeviceMessage(this._dataSourceConfig.IP, this._dataSourceConfig.Port, sToken);

        // Get Device List and Itemid
        bool waitSuccess = !(ReadPanelHttp.ListDevice.Count == 0);
        if (ReadPanelHttp.ListDevice.Count == 0)
        {
            Thread.Sleep(_dataSourceConfig.RetryInterval);
        }

        int retryCount = 0;
        while (retryCount < _dataSourceConfig.MaxRetry)
        {
            if (waitSuccess)
            {
                break;
            }
            else
            {
                retryCount++;
                Thread.Sleep(_dataSourceConfig.RetryInterval);
            }
        }

        if (!waitSuccess)
        {
            throw new Exception("Waiting data source timeout");
        }

        // All Devices type
        this._dictCategoryDevices = new Dictionary<string, List<IDeviceInfo>>();
        for (int nIdx = 0; nIdx < ReadPanelHttp.ListDevice.Count; nIdx++)
        {
            string key = ReadPanelHttp.ListDevice[nIdx].DeviceType;
            IDeviceInfo value = ReadPanelHttp.ListDevice[nIdx];

            // add value
            if (this._dictCategoryDevices.ContainsKey(key))
            {
                this._dictCategoryDevices[key].Add(value);
            }
            else
            {
                this._dictCategoryDevices[key] = new List<IDeviceInfo> { value };
            }
        }

        var userManagementNodeManager = new UserManagementNodeManager(this);
        userManagementNodeManager.Startup();

        // var alertingNodeManager = new PanelAlarmNodeManager(this);
        // alertingNodeManager.Startup();

        this.StartOPCUANodeManager(ReadPanelHttp.ListDevice);

        this.SessionManager.ImpersonateUser += new ImpersonateEventHandler(this.SessionManager_ImpersonateUser);
    }

    private void InitDataSource(string ip, string port, string token, int nInterval)
    {
        this._readUdcHttpObj = new ReadPanelHttp();
        this._readUdcHttpObj.StartReadUdcHttp(ip, port, token);
    }

    private void InitDeviceMessage(string ip, string port, string token)
    {
        this._readUdcMessageHttpObj = new ReadPanelMessage();
        // this._readUdcMessageHttpObj.StartReadUdcMessage(ip, port, token);
    }

    private void StartOPCUANodeManager(List<IDeviceInfo> devices)
    {
        OPCUANodeManager.InitialPAC4200System(devices.Count, devices.Select(d => d.ItemId).ToList());

        int successCount = 0;
        int failureCount = 0;
        const int timeoutMilliseconds = 30000;
        const int maxRetries = 3;

        foreach (IDeviceInfo deviceInfo in devices)
        {
            int retryCount = 0;
            bool success = false;

            while (retryCount < maxRetries && !success)
            {
                try
                {
                    var logPrefix = retryCount > 0 ? $"[Retry {retryCount}] " : string.Empty;
                    _logger.Information($"{logPrefix}Starting NodeManager for device: {deviceInfo.DeviceType} {deviceInfo.DeviceIP}");

                    var startupTask = Task.Run(() =>
                    {
                        var nodeManager = new OPCUANodeManager(this)
                        {
                            ItemId = deviceInfo.ItemId,
                            ItemName = deviceInfo.DeviceType + " " + deviceInfo.DeviceIP
                        };
                        nodeManager.Startup();
                        return nodeManager;
                    });

                    if (startupTask.Wait(timeoutMilliseconds))
                    {
                        success = true;
                        successCount++;
                        _logger.Information($"Successfully started NodeManager for {deviceInfo.DeviceType} {deviceInfo.DeviceIP}");
                    }
                    else
                    {
                        _logger.Warning($"Timeout starting NodeManager for {deviceInfo.DeviceType} {deviceInfo.DeviceIP} after {timeoutMilliseconds}ms");
                        retryCount++;

                        if (retryCount < maxRetries)
                        {
                            _logger.Information($"Will retry in 5 seconds... ({retryCount}/{maxRetries})");
                            Thread.Sleep(5000);
                        }
                    }
                }
                catch (AggregateException ex)
                {
                    var innerEx = ex.InnerException ?? ex;
                    _logger.Error($"Failed to start NodeManager for {deviceInfo.DeviceType} {deviceInfo.DeviceIP}: {innerEx.Message}");
                    retryCount++;

                    if (retryCount < maxRetries)
                    {
                        _logger.Information($"Will retry in 5 seconds... ({retryCount}/{maxRetries})");
                        Thread.Sleep(5000);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"Unexpected error starting NodeManager for {deviceInfo.DeviceType} {deviceInfo.DeviceIP}: {ex.Message}");
                    retryCount++;

                    if (retryCount < maxRetries)
                    {
                        _logger.Information($"Will retry in 5 seconds... ({retryCount}/{maxRetries})");
                        Thread.Sleep(5000);
                    }
                }
            }

            if (!success)
            {
                failureCount++;
                _logger.Error($"Failed to start NodeManager for {deviceInfo.DeviceType} {deviceInfo.DeviceIP} after {maxRetries} attempts");
            }
        }

        _logger.Information($"NodeManager startup completed. Success: {successCount}, Failures: {failureCount}, Total: {devices.Count}");

        if (failureCount > 0)
        {
            _logger.Warning($"Some NodeManagers failed to start after multiple retries. Check logs for details.");
        }
    }

        //         // DynamicNodeServiceIntegration.StartDynamicNodeService(new List<PAC4200NodeManager> { nw.Nodes }, 5);

    //         // _logger.Information("✅ Dynamic node service started!");
}
