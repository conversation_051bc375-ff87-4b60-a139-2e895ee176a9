using System.Xml.Serialization;
using HTTPInterface;
using OPCUAServer.Common;
using Serilog;
using UnifiedAutomation.UaBase;

namespace OPCUAServer.Devices.Panel;

/// <summary>
/// A class that provides access to the underlying system.
/// </summary>
public class PanelSystem : IDisposable
{
    private const int PUSHINTERVAL = 2000;
    private static readonly ILogger _logger = LoggingHelper.GetLogger<PanelSystem>();
    private readonly object _lock = new();
    private readonly Dictionary<string, string> _registers = new();
    private readonly Dictionary<int, BlockConfiguration> _blocks = new();
    private readonly PanelRealtimeData _realtimeDataObj = new();

    private Configuration? _configuration;
    private Timer? _panelTimer;
    private int _position;

    public PanelSystem()
    {
    }

    /// <summary>
    /// Initializes this instance.
    /// </summary>
    /// <param name="itemId">The item id.</param>
    public void Initialize(string itemId)
    {
        this.Load();

        this._realtimeDataObj.PanelRealtimeInit(itemId);

        this._panelTimer = new Timer(this.DoWork, itemId, PUSHINTERVAL, PUSHINTERVAL);
    }

    /// <summary>
    /// Gets the blockAddress configurations.
    /// </summary>
    /// <returns>Returns the block configurations.</returns>
    public IEnumerable<BlockConfiguration> GetBlocks()
    {
        return this._blocks.Values;
    }

    /// <summary>
    /// Reads the tag value.
    /// </summary>
    /// <param name="blockAddress">The blockAddress.</param>
    /// <param name="tag">The tag.</param>
    /// <returns>The value. null if no value exists.</returns>
    public object? Read(int blockAddress, int tag)
    {
        lock (this._lock)
        {
            if (blockAddress < 0 || tag < 0)
            {
                return null;
            }

            if (!this._blocks.TryGetValue(blockAddress, out var controller))
            {
                return null;
            }

            foreach (BlockProperty property in controller.Properties)
            {
                if (property.Offset == tag && property.DataType == DataTypeIds.String)
                {
                    return this._registers[property.Name];
                }
            }

            return null;
        }
    }

    /// <summary>
    /// Writes the tag value.
    /// </summary>
    /// <param name="blockAddress">The blockAddress.</param>
    /// <param name="tag">The tag.</param>
    /// <param name="value">The value.</param>
    /// <returns>
    /// True if the write was successful.
    /// </returns>
    public bool Write(int blockAddress, int tag, object value)
    {
        lock (this._lock)
        {
            if (blockAddress < 0 || tag < 0)
            {
                return false;
            }

            if (blockAddress + tag > this._position - sizeof(int))
            {
                return false;
            }

            if (!this._blocks.TryGetValue(blockAddress, out var controller))
            {
                _logger.Error($"Failed to write to blockAddress {blockAddress} and tag {tag}. BlockAddress not found.");
                return false;
            }

            foreach (BlockProperty property in controller.Properties)
            {
                if (property.Offset == tag)
                {
                    if (!property.Writeable)
                    {
                        _logger.Error($"Failed to write to blockAddress {blockAddress} and tag {tag}. Property is not writeable.");
                        return false;
                    }

                    if (property.DataType == DataTypeIds.Double)
                    {
                        this.Write(blockAddress, tag, (double)value);
                    }
                    else if (property.DataType == DataTypeIds.Int32)
                    {
                        this.Write(blockAddress, tag, (int)value);
                    }
                    else if (property.DataType == DataTypeIds.String)
                    {
                        this.Write(property.Name, (string)value);
                    }
                }

                _logger.Information($"Successfully write to blockAddress {blockAddress} and tag {tag}.");
                return true;
            }

            _logger.Error($"Failed to write to blockAddress {blockAddress} and tag {tag}. Property not found.");
            return false;
        }
    }

    /// <summary>
    /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
    /// </summary>
    public void Dispose()
    {
        this.Dispose(true);
    }

    /// <summary>
    /// Releases unmanaged and - optionally - managed resources
    /// </summary>
    /// <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>true</c> to release only unmanaged resources.</param>
    protected void Dispose(bool disposing)
    {
        if (disposing)
        {
            this._panelTimer?.Dispose();
            this._panelTimer = null;
        }
    }

    /// <summary>
    /// Loads the configuration for the system.
    /// </summary>
    private void Load()
    {
        var assembly = PlatformUtils.GetAssembly(typeof(PanelSystem));
        foreach (string resourceName in assembly.GetManifestResourceNames())
        {
            if (resourceName.EndsWith(".PanelConfiguration.xml"))
            {
                try
                {
                    using Stream? istrm = assembly.GetManifestResourceStream(resourceName);
                    if (istrm is null)
                    {
                        _logger.Error("Failed to load configuration for Panel system from resource: {ResourceName}", resourceName);
                        continue;
                    }

                    var serializer = new XmlSerializer(typeof(Configuration));
                    var deserializedObject = serializer.Deserialize(istrm);

                    if (deserializedObject is Configuration config)
                    {
                        this._configuration = config;
                        _logger.Information("Panel configuration loaded successfully from {ResourceName}", resourceName);
                    }
                    else
                    {
                        _logger.Error(
                            "Deserialized object is not of type Configuration. Actual type: {ActualType}",
                            deserializedObject?.GetType().Name ?? "null");
                        throw new InvalidOperationException("Failed to deserialize configuration: unexpected type");
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "Failed to deserialize Panel configuration from {ResourceName}", resourceName);
                    throw;
                }
            }
        }

        if (this._configuration?.Controllers is not null)
        {
            foreach (var controller in this._configuration.Controllers)
            {
                int blockAddress = this._position;
                int offset = this._position - blockAddress;

                var data = new BlockConfiguration()
                {
                    Address = blockAddress,
                    Name = controller.Name,
                    Type = controller.Type,
                    Properties = new List<BlockProperty>()
                };

                if (controller.Properties is not null)
                {
                    foreach (var property in controller.Properties)
                    {
                        var dataTypeId = NodeId.Parse(property.DataType);
                        string value = property.Value;

                        data.Properties.Add(new BlockProperty()
                        {
                            Offset = offset,
                            Name = property.Name,
                            DataType = dataTypeId,
                            Writeable = property.Writeable,
                        });

                        switch ((uint)dataTypeId.Identifier)
                        {
                            case DataTypes.Int32:
                                {
                                    this.Write(blockAddress, offset, (int)TypeUtils.Cast(value, BuiltInType.Int32));
                                    offset += 4;
                                    break;
                                }

                            case DataTypes.Double:
                                {
                                    this.Write(blockAddress, offset, (double)TypeUtils.Cast(value, BuiltInType.Double));
                                    offset += 4;
                                    break;
                                }

                            case DataTypes.String:
                                {
                                    this.Write(property.Name, property.Value);
                                    offset += 1;
                                    break;
                                }
                        }
                    }
                }

                this._position += offset;
                this._blocks[blockAddress] = data;
            }
        }
    }

    /// <summary>
    /// Writes the specified offset.
    /// </summary>
    /// <param name="value">The value.</param>
    private void Write(string name, string value)
    {
        this._registers[name] = value;
    }

    /// <summary>
    /// Does the simulation.
    /// </summary>
    /// <param name="state">The state.</param>
    private void DoWork(object? state)
    {
        try
        {
            lock (this._lock)
            {
                string? sItem = state as string;
                if (string.IsNullOrEmpty(sItem))
                {
                    _logger.Error("Failed to run simulation. ItemId is null or empty.");
                    return;
                }

                this._realtimeDataObj.PanelRealtimeUpdate(sItem);
                foreach (var blockAddress in this._blocks)
                {
                    foreach (var property in blockAddress.Value.Properties)
                    {
                        string channelName = property.Name;
                        string val = this.GetChannelValue(channelName);
                        this.Write(channelName, val);
                    }
                }
            }
        }
        catch (Exception e)
        {
            _logger.Error(e, "Failed run simulation.");
        }
    }

    private string GetChannelValue(string firstName)
    {
        return this._realtimeDataObj.GetChannelValue(firstName);
    }
}
